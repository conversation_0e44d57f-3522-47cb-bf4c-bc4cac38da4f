# 完整版脑卒中T1DM样式地图 - 包含主图和区域放大图
library(ggplot2)
library(dplyr)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(RColorBrewer)
library(gridExtra)
library(grid)

# 读取数据
data <- read.csv("脑卒中65岁以上人群患病率分析表.csv", stringsAsFactors = FALSE, fileEncoding = "UTF-8")

# 提取地区AAPC数据
region_aapc <- data %>%
  filter(分类 %in% c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", "高收入", 
                    "中欧、东欧和中亚", "拉丁美洲和加勒比海", "北非和中东")) %>%
  select(分类, AAPC = colnames(data)[14])

# 创建国家映射
country_region_mapping <- data.frame(
  country = c(
    # 高收入国家
    "United States of America", "Germany", "United Kingdom", "France", "Italy", "Canada", "Spain", 
    "Netherlands", "Belgium", "Switzerland", "Austria", "Sweden", "Norway", "Denmark", "Finland", 
    "Ireland", "Portugal", "Greece", "Israel", "Luxembourg", "Iceland", "Malta", "Cyprus",
    "Australia", "New Zealand", "Japan", "South Korea", "Singapore",
    
    # 中欧、东欧和中亚
    "Russia", "Poland", "Ukraine", "Romania", "Hungary", "Belarus", "Bulgaria", "Serbia",
    "Croatia", "Bosnia and Herzegovina", "Albania", "North Macedonia", "Moldova", "Montenegro", 
    "Kazakhstan", "Uzbekistan", "Tajikistan", "Kyrgyzstan", "Turkmenistan", "Georgia", 
    "Armenia", "Azerbaijan", "Czech Republic", "Slovakia", "Slovenia", "Estonia", "Latvia", "Lithuania",
    
    # 东南亚、东亚和大洋洲
    "China", "Indonesia", "Philippines", "Vietnam", "Thailand", "Myanmar", "Malaysia", 
    "Cambodia", "Laos", "Brunei", "Timor-Leste", "Papua New Guinea", "Fiji", "Solomon Islands", 
    "Vanuatu", "Samoa", "Kiribati", "Tonga", "Micronesia", "Palau", "Marshall Islands", 
    "Nauru", "Tuvalu", "North Korea", "Mongolia",
    
    # 南亚
    "India", "Pakistan", "Bangladesh", "Afghanistan", "Nepal", "Sri Lanka", "Bhutan", "Maldives",
    
    # 拉丁美洲和加勒比海
    "Brazil", "Mexico", "Colombia", "Argentina", "Peru", "Venezuela", "Chile", "Ecuador",
    "Guatemala", "Cuba", "Bolivia", "Haiti", "Dominican Republic", "Honduras", "Paraguay",
    "Nicaragua", "El Salvador", "Costa Rica", "Panama", "Uruguay", "Jamaica", 
    "Trinidad and Tobago", "Guyana", "Suriname", "Belize", "Barbados",
    
    # 北非和中东
    "Egypt", "Iran", "Turkey", "Iraq", "Saudi Arabia", "Yemen", "Syria", "Jordan", "Lebanon",
    "United Arab Emirates", "Oman", "Kuwait", "Qatar", "Bahrain", "Algeria", "Morocco", 
    "Sudan", "Tunisia", "Libya",
    
    # 撒哈拉以南非洲
    "Nigeria", "Ethiopia", "South Africa", "Kenya", "Uganda", "Tanzania", "Ghana", "Mozambique", 
    "Madagascar", "Cameroon", "Angola", "Niger", "Burkina Faso", "Mali", "Malawi", "Zambia", 
    "Somalia", "Senegal", "Chad", "Zimbabwe", "Guinea", "Rwanda", "Benin", "Burundi",
    "South Sudan", "Togo", "Sierra Leone", "Liberia", "Central African Republic",
    "Mauritania", "Eritrea", "Gambia", "Botswana", "Namibia", "Gabon", "Lesotho", 
    "Guinea-Bissau", "Equatorial Guinea", "Mauritius", "Eswatini", "Djibouti", 
    "Comoros", "Cape Verde", "São Tomé and Príncipe"
  ),
  region = c(
    rep("高收入", 28),
    rep("中欧、东欧和中亚", 28),
    rep("东南亚、东亚和大洋洲", 25),
    rep("南亚", 8),
    rep("拉丁美洲和加勒比海", 26),
    rep("北非和中东", 19),
    rep("撒哈拉以南非洲", 44)
  )
)

# 合并数据
country_data <- country_region_mapping %>%
  left_join(region_aapc, by = c("region" = "分类"))

# 获取世界地图数据
world <- ne_countries(scale = "medium", returnclass = "sf")

# 合并地图数据
world_data <- world %>%
  left_join(country_data, by = c("name" = "country")) %>%
  mutate(
    # 创建颜色分组（基于实际AAPC值）
    aapc_group = case_when(
      is.na(AAPC) ~ "No data",
      AAPC < -0.5 ~ "< -0.5",
      AAPC >= -0.5 & AAPC < 0 ~ "-0.5 to < 0",
      AAPC >= 0 & AAPC < 0.5 ~ "0 to < 0.5",
      AAPC >= 0.5 ~ "≥ 0.5"
    ),
    aapc_group = factor(aapc_group, levels = c("< -0.5", "-0.5 to < 0", "0 to < 0.5", 
                                               "≥ 0.5", "No data"))
  )

# 定义颜色方案（蓝色表示下降，红色表示上升）
colors <- c("#2166ac", "#67a9cf", "#f7f7f7", "#e31a1c", "#cccccc")
names(colors) <- levels(world_data$aapc_group)

# 创建主地图
create_main_map <- function() {
  ggplot(world_data) +
    geom_sf(aes(fill = aapc_group), color = "white", size = 0.1) +
    scale_fill_manual(values = colors, name = "AAPC (%)", drop = FALSE) +
    theme_void() +
    theme(
      legend.position = "bottom",
      legend.title = element_text(size = 12, face = "bold"),
      legend.text = element_text(size = 10),
      plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
      plot.subtitle = element_text(size = 14, hjust = 0.5),
      legend.key.size = unit(0.8, "cm"),
      panel.background = element_rect(fill = "lightblue", color = NA),
      plot.background = element_rect(fill = "white", color = NA),
      plot.margin = margin(10, 10, 10, 10)
    ) +
    labs(
      title = "Average Annual Percentage Change in Stroke Prevalence",
      subtitle = "Among people aged ≥65 years, 1990-2019"
    ) +
    coord_sf(crs = "+proj=robin")
}

# 创建区域放大图
create_regional_map <- function(xlim, ylim, title) {
  ggplot(world_data) +
    geom_sf(aes(fill = aapc_group), color = "white", size = 0.2) +
    scale_fill_manual(values = colors, guide = "none") +
    coord_sf(xlim = xlim, ylim = ylim, expand = FALSE) +
    theme_void() +
    theme(
      plot.title = element_text(size = 9, face = "bold", hjust = 0.5),
      panel.border = element_rect(color = "black", fill = NA, linewidth = 1),
      plot.background = element_rect(fill = "white", color = NA),
      plot.margin = margin(2, 2, 2, 2)
    ) +
    labs(title = title)
}

# 创建主地图
main_map <- create_main_map()

# 创建区域放大图
caribbean <- create_regional_map(c(-90, -60), c(10, 25), "Caribbean and\nCentral America")
persian_gulf <- create_regional_map(c(45, 60), c(24, 32), "Persian Gulf")
balkans <- create_regional_map(c(15, 30), c(40, 48), "Balkan Peninsula")
southeast_asia <- create_regional_map(c(95, 140), c(-10, 25), "South East Asia")
west_africa <- create_regional_map(c(-20, 10), c(4, 20), "West Africa")
northern_europe <- create_regional_map(c(5, 30), c(55, 70), "Northern Europe")

# 保存主地图
ggsave("stroke_main_map_t1dm_style.png", main_map, 
       width = 16, height = 10, dpi = 300, bg = "white")

# 创建区域放大图组合
regional_combined <- grid.arrange(caribbean, persian_gulf, balkans, 
                                 southeast_asia, west_africa, northern_europe,
                                 ncol = 6, 
                                 top = textGrob("Regional Detail Maps", 
                                               gp = gpar(fontsize = 14, fontface = "bold")))

# 保存区域放大图
ggsave("stroke_regional_maps_t1dm_style.png", regional_combined, 
       width = 18, height = 4, dpi = 300, bg = "white")

# 创建完整的组合图
# 使用layout来组合主图和区域图
png("stroke_complete_t1dm_style_map.png", width = 16*300, height = 12*300, res = 300, bg = "white")

# 设置布局
layout_matrix <- matrix(c(1, 1, 1, 1,
                         1, 1, 1, 1,
                         1, 1, 1, 1,
                         2, 2, 2, 2), nrow = 4, byrow = TRUE)
layout(layout_matrix)

# 绘制主图
par(mar = c(0, 0, 2, 0))
print(main_map)

# 绘制区域图
par(mar = c(0, 0, 1, 0))
grid.arrange(caribbean, persian_gulf, balkans, 
             southeast_asia, west_africa, northern_europe,
             ncol = 6)

dev.off()

# 显示主图
print(main_map)

# 打印统计信息
cat("\n🌍 脑卒中65岁以上人群AAPC数据摘要 (1990-2019):\n")
for(i in 1:nrow(region_aapc)) {
  cat(sprintf("📍 %s: %+.2f%%\n", region_aapc$分类[i], region_aapc$AAPC[i]))
}

cat("\n📊 AAPC分组统计:\n")
print(table(world_data$aapc_group, useNA = "ifany"))

cat("\n🎨 颜色图例说明:\n")
cat("🔵 深蓝色 (< -0.5%): 患病率显著下降\n")
cat("🔷 浅蓝色 (-0.5 to 0%): 患病率轻微下降\n") 
cat("⚪ 白色/灰色 (0 to 0.5%): 患病率基本稳定\n")
cat("🔴 红色 (≥ 0.5%): 患病率显著上升\n")
cat("⬜ 灰色: 无数据\n")
