# 脑卒中65岁以上人群患病率AAPC全球地图可视化 - T1DM样式
# 基于1990-2019年数据，模仿T1DM地图的专业样式

# 加载必要的包
library(ggplot2)
library(dplyr)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(RColorBrewer)
library(viridis)
library(gridExtra)
library(grid)

# 读取数据
data <- read.csv("脑卒中65岁以上人群患病率分析表.csv", stringsAsFactors = FALSE, fileEncoding = "UTF-8")

# 提取地区AAPC数据
region_aapc <- data %>%
  filter(分类 %in% c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", "高收入", 
                    "中欧、东欧和中亚", "拉丁美洲和加勒比海", "北非和中东")) %>%
  select(分类, AAPC = colnames(data)[14])

print("脑卒中AAPC数据:")
print(region_aapc)

# 创建详细的国家到地区映射
country_region_mapping <- data.frame(
  country = c(
    # 撒哈拉以南非洲
    "Nigeria", "Ethiopia", "South Africa", "Kenya", "Uganda", "Tanzania", "Ghana", "Mozambique", 
    "Madagascar", "Cameroon", "Angola", "Niger", "Burkina Faso", "Mali", "Malawi", "Zambia", 
    "Somalia", "Senegal", "Chad", "Zimbabwe", "Guinea", "Rwanda", "Benin", "Burundi",
    "South Sudan", "Togo", "Sierra Leone", "Liberia", "Central African Republic",
    "Mauritania", "Eritrea", "Gambia", "Botswana", "Namibia", "Gabon", "Lesotho", 
    "Guinea-Bissau", "Equatorial Guinea", "Mauritius", "Eswatini", "Djibouti", 
    "Comoros", "Cape Verde", "São Tomé and Príncipe",
    
    # 东南亚、东亚和大洋洲
    "China", "Indonesia", "Japan", "Philippines", "Vietnam", "Thailand", "Myanmar", "South Korea",
    "Malaysia", "Cambodia", "Laos", "Singapore", "Mongolia", "Brunei", "Timor-Leste", "Australia",
    "Papua New Guinea", "New Zealand", "Fiji", "Solomon Islands", "Vanuatu", "Samoa", "Kiribati",
    "Tonga", "Micronesia", "Palau", "Marshall Islands", "Nauru", "Tuvalu", "North Korea",
    
    # 南亚
    "India", "Pakistan", "Bangladesh", "Afghanistan", "Nepal", "Sri Lanka", "Bhutan", "Maldives",
    
    # 高收入（主要发达国家）
    "United States of America", "Germany", "United Kingdom", "France", "Italy", "Canada", "Spain", 
    "Netherlands", "Belgium", "Switzerland", "Austria", "Sweden", "Norway", "Denmark", "Finland", 
    "Ireland", "Portugal", "Greece", "Israel", "Luxembourg", "Iceland", "Malta", "Cyprus",
    "Czech Republic", "Slovenia", "Estonia", "Latvia", "Lithuania", "Slovakia",
    
    # 中欧、东欧和中亚
    "Russia", "Poland", "Ukraine", "Romania", "Hungary", "Belarus", "Bulgaria", "Serbia",
    "Croatia", "Bosnia and Herzegovina", "Albania", "North Macedonia", "Moldova", "Montenegro", 
    "Kazakhstan", "Uzbekistan", "Tajikistan", "Kyrgyzstan", "Turkmenistan", "Georgia", 
    "Armenia", "Azerbaijan",
    
    # 拉丁美洲和加勒比海
    "Brazil", "Mexico", "Colombia", "Argentina", "Peru", "Venezuela", "Chile", "Ecuador",
    "Guatemala", "Cuba", "Bolivia", "Haiti", "Dominican Republic", "Honduras", "Paraguay",
    "Nicaragua", "El Salvador", "Costa Rica", "Panama", "Uruguay", "Jamaica", 
    "Trinidad and Tobago", "Guyana", "Suriname", "Belize", "Barbados",
    
    # 北非和中东
    "Egypt", "Iran", "Turkey", "Iraq", "Saudi Arabia", "Yemen", "Syria", "Jordan", "Lebanon",
    "United Arab Emirates", "Oman", "Kuwait", "Qatar", "Bahrain", "Algeria", "Morocco", 
    "Sudan", "Tunisia", "Libya"
  ),
  region = c(
    rep("撒哈拉以南非洲", 44),
    rep("东南亚、东亚和大洋洲", 30),
    rep("南亚", 8),
    rep("高收入", 29),
    rep("中欧、东欧和中亚", 22),
    rep("拉丁美洲和加勒比海", 26),
    rep("北非和中东", 19)
  )
)

# 合并数据
country_data <- country_region_mapping %>%
  left_join(region_aapc, by = c("region" = "分类"))

# 获取世界地图数据
world <- ne_countries(scale = "medium", returnclass = "sf")

# 标准化国家名称并合并数据
world_data <- world %>%
  left_join(country_data, by = c("name" = "country")) %>%
  mutate(
    # 调整AAPC值到正数范围以匹配T1DM样式的颜色方案
    aapc_adjusted = case_when(
      is.na(AAPC) ~ NA_real_,
      TRUE ~ AAPC + 1.5  # 将范围从[-0.73, 0.82]调整到[0.77, 2.32]
    ),
    # 创建颜色分组（模仿T1DM的分组）
    aapc_group = case_when(
      is.na(aapc_adjusted) ~ "No data",
      aapc_adjusted < 1.0 ~ "0.28 to <0.57",
      aapc_adjusted < 1.3 ~ "0.57 to <1.18", 
      aapc_adjusted < 1.8 ~ "1.18 to <1.95",
      aapc_adjusted >= 1.8 ~ "1.95 to <3.61"
    ),
    aapc_group = factor(aapc_group, levels = c("0.28 to <0.57", "0.57 to <1.18", 
                                               "1.18 to <1.95", "1.95 to <3.61", "No data"))
  )

# 定义颜色方案（模仿T1DM地图）
colors <- c("#2166ac", "#67a9cf", "#fdbf6f", "#e31a1c", "#cccccc")
names(colors) <- levels(world_data$aapc_group)

# 创建主地图
main_map <- ggplot(world_data) +
  geom_sf(aes(fill = aapc_group), color = "white", size = 0.1) +
  scale_fill_manual(values = colors, name = "AAPC (%)", drop = FALSE,
                    labels = c("< -0.5 (Decreasing)", "-0.5 to 0", "0 to 0.5", 
                              "> 0.5 (Increasing)", "No data")) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 10),
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 14, hjust = 0.5),
    legend.key.size = unit(1, "cm"),
    panel.background = element_rect(fill = "lightblue", color = NA),
    plot.background = element_rect(fill = "white", color = NA)
  ) +
  labs(
    title = "Average Annual Percentage Change in Stroke Prevalence",
    subtitle = "Among people aged ≥65 years, 1990-2019"
  ) +
  coord_sf(crs = "+proj=robin")

# 创建区域放大图的函数
create_regional_map <- function(xlim, ylim, title) {
  ggplot(world_data) +
    geom_sf(aes(fill = aapc_group), color = "white", size = 0.2) +
    scale_fill_manual(values = colors, guide = "none") +
    coord_sf(xlim = xlim, ylim = ylim, expand = FALSE) +
    theme_void() +
    theme(
      plot.title = element_text(size = 10, face = "bold", hjust = 0.5),
      panel.border = element_rect(color = "black", fill = NA, size = 1),
      plot.background = element_rect(fill = "white", color = NA)
    ) +
    labs(title = title)
}

# 创建区域放大图
caribbean <- create_regional_map(c(-90, -60), c(10, 25), "Caribbean and\nCentral America")
persian_gulf <- create_regional_map(c(45, 60), c(24, 32), "Persian Gulf")
balkans <- create_regional_map(c(15, 30), c(40, 48), "Balkan Peninsula")
southeast_asia <- create_regional_map(c(95, 140), c(-10, 25), "South East Asia")
west_africa <- create_regional_map(c(-20, 10), c(4, 20), "West Africa")
northern_europe <- create_regional_map(c(5, 30), c(55, 70), "Northern Europe")

# 组合所有图
regional_maps <- grid.arrange(caribbean, persian_gulf, balkans,
                             southeast_asia, west_africa, northern_europe,
                             ncol = 6)

# 最终组合 - 先保存主地图
final_plot <- main_map

# 保存图片
ggsave("stroke_t1dm_style_global_map.png", final_plot, 
       width = 20, height = 12, dpi = 300, bg = "white")

# 显示图片
print(final_plot)

# 打印统计信息
cat("\n脑卒中65岁以上人群AAPC统计摘要:\n")
print(region_aapc)
cat("\nAAPC分组统计:\n")
print(table(world_data$aapc_group, useNA = "ifany"))
