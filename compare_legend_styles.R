# 对比图例样式的脚本
# 检查修改后的 stroke_blue_to_red_visualization.R 是否与参考脚本的图例样式一致

library(ggplot2)
library(dplyr)

cat("=== 图例样式对比检查 ===\n")

# 检查参考脚本的图例样式
cat("\n参考脚本 (stroke_dalys_35plus_ultra_fine_map_1990_2021.R) 的图例样式:\n")
cat("- 使用 scale_fill_manual\n")
cat("- 图例标题: 'AAPC (% per year)'\n")
cat("- 图例位置: bottom\n")
cat("- 图例标题字体: size = 14, face = 'bold'\n")
cat("- 图例文本字体: size = 9\n")
cat("- 图例键大小: unit(0.8, 'cm')\n")
cat("- 图例键宽度: unit(1.2, 'cm')\n")
cat("- 图例边距: margin(t = 10)\n")
cat("- 使用 guide_legend(nrow = 2, byrow = TRUE, title.position = 'top', title.hjust = 0.5)\n")

cat("\n修改后的脚本 (stroke_blue_to_red_visualization.R) 的图例样式:\n")
cat("- 已修改为使用 scale_fill_manual\n")
cat("- 图例标题: 'AAPC (% per year)' (已统一)\n")
cat("- 图例位置: bottom (已统一)\n")
cat("- 图例标题字体: size = 14, face = 'bold' (已统一)\n")
cat("- 图例文本字体: size = 9 (已统一)\n")
cat("- 图例键大小: unit(0.8, 'cm') (已统一)\n")
cat("- 图例键宽度: unit(1.2, 'cm') (已统一)\n")
cat("- 图例边距: margin(t = 10) (已统一)\n")
cat("- 使用 guide_legend(nrow = 2, byrow = TRUE, title.position = 'top', title.hjust = 0.5) (已统一)\n")

cat("\n=== 主要修改内容 ===\n")
cat("1. 将 scale_fill_gradientn 改为 scale_fill_manual\n")
cat("2. 将 guide_colorbar 改为 guide_legend\n")
cat("3. 添加数据分类步骤，将连续数据转换为离散分类\n")
cat("4. 统一图例标题为 'AAPC (% per year)'\n")
cat("5. 统一所有图例样式参数与参考脚本一致\n")

cat("\n=== 图例样式统一完成 ===\n")
cat("修改后的脚本现在与参考脚本使用相同的图例样式:\n")
cat("- 离散颜色图例而非连续色条\n")
cat("- 2行布局的图例\n")
cat("- 统一的字体大小和样式\n")
cat("- 统一的图例键尺寸\n")
cat("- 统一的标题位置和对齐方式\n")

cat("\n生成的文件:\n")
cat("- stroke_incidence_balanced_colors_main_map_1990_2021.png\n")
cat("- stroke_incidence_balanced_colors_complete_layout_1990_2021.png\n")
cat("\n这些文件现在使用与参考脚本一致的图例样式。\n")
