# 脑卒中地图 - 强制显示完整图例的最终解决方案
library(ggplot2)
library(dplyr)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(countrycode)

# 读取数据
data <- read.csv("脑卒中65岁以上人群患病率分析表.csv", 
                 stringsAsFactors = FALSE, 
                 fileEncoding = "UTF-8")

# 提取地区AAPC数据
region_aapc <- data %>%
  filter(分类 %in% c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", 
                    "高收入", "中欧、东欧和中亚", "拉丁美洲和加勒比海", 
                    "北非和中东")) %>%
  select(分类, AAPC = `AAPC...`)

# 创建简化的国家映射
create_country_mapping <- function() {
  # 主要国家列表
  countries <- c(
    # 高收入 (23个)
    "United States", "Germany", "United Kingdom", "France", "Italy", 
    "Canada", "Spain", "Netherlands", "Belgium", "Switzerland", "Austria", 
    "Sweden", "Norway", "Denmark", "Finland", "Ireland", "Portugal", 
    "Greece", "Israel", "Luxembourg", "Iceland", "Australia", "New Zealand",
    
    # 撒哈拉以南非洲 (23个)
    "Nigeria", "Ethiopia", "South Africa", "Kenya", "Uganda", "Tanzania", 
    "Ghana", "Mozambique", "Madagascar", "Cameroon", "Angola", "Niger", 
    "Burkina Faso", "Mali", "Malawi", "Zambia", "Somalia", "Senegal", 
    "Chad", "Zimbabwe", "Guinea", "Rwanda", "Benin",
    
    # 东南亚、东亚和大洋洲 (23个)
    "China", "Indonesia", "Japan", "Philippines", "Vietnam", "Thailand", 
    "Myanmar", "South Korea", "Malaysia", "Cambodia", "Laos", "Singapore", 
    "Mongolia", "Brunei", "Timor-Leste", "Papua New Guinea", "Fiji", 
    "Solomon Islands", "Vanuatu", "Samoa", "Kiribati", "Tonga", "Palau",
    
    # 南亚 (8个)
    "India", "Pakistan", "Bangladesh", "Afghanistan", "Nepal", "Sri Lanka", 
    "Bhutan", "Maldives",
    
    # 中欧、东欧和中亚 (23个)
    "Russia", "Poland", "Ukraine", "Romania", "Czech Republic", "Hungary", 
    "Belarus", "Bulgaria", "Serbia", "Slovakia", "Croatia", 
    "Bosnia and Herzegovina", "Albania", "Lithuania", "Slovenia", 
    "Latvia", "Estonia", "North Macedonia", "Moldova", "Montenegro", 
    "Kazakhstan", "Uzbekistan", "Georgia",
    
    # 拉丁美洲和加勒比海 (23个)
    "Brazil", "Mexico", "Colombia", "Argentina", "Peru", "Venezuela", 
    "Chile", "Ecuador", "Guatemala", "Cuba", "Bolivia", "Haiti", 
    "Dominican Republic", "Honduras", "Paraguay", "Nicaragua", 
    "El Salvador", "Costa Rica", "Panama", "Uruguay", "Jamaica", 
    "Trinidad and Tobago", "Guyana",
    
    # 北非和中东 (19个)
    "Egypt", "Iran", "Turkey", "Iraq", "Saudi Arabia", "Yemen", "Syria", 
    "Jordan", "Lebanon", "United Arab Emirates", "Oman", "Kuwait", "Qatar", 
    "Bahrain", "Algeria", "Morocco", "Sudan", "Tunisia", "Libya"
  )
  
  regions <- c(
    rep("高收入", 23),
    rep("撒哈拉以南非洲", 23),
    rep("东南亚、东亚和大洋洲", 23),
    rep("南亚", 8),
    rep("中欧、东欧和中亚", 23),
    rep("拉丁美洲和加勒比海", 23),
    rep("北非和中东", 19)
  )
  
  return(data.frame(country = countries, region = regions))
}

country_region_mapping <- create_country_mapping()

# 合并数据
country_data <- country_region_mapping %>%
  left_join(region_aapc, by = c("region" = "分类"))

# 获取世界地图数据
world <- ne_countries(scale = "medium", returnclass = "sf")

# 标准化国家名称
world$name_clean <- countrycode(world$name, "country.name", "country.name")
country_data$country_clean <- countrycode(country_data$country, 
                                         "country.name", "country.name")

# 合并地图数据和AAPC数据
world_data <- world %>%
  left_join(country_data, by = c("name_clean" = "country_clean"))

# 定义完整的分组级别
all_levels <- c("< -0.8", "-0.8 to -0.6", "-0.6 to -0.4", "-0.4 to -0.2", 
               "-0.2 to 0", "0 to 0.2", "0.2 to 0.4", "0.4 to 0.6", 
               "0.6 to 0.8", "0.8 to 1.0", "> 1.0", "No data")

# 创建AAPC分组
world_data <- world_data %>%
  mutate(
    aapc_group = case_when(
      is.na(AAPC) ~ "No data",
      AAPC < -0.8 ~ "< -0.8",
      AAPC >= -0.8 & AAPC < -0.6 ~ "-0.8 to -0.6",
      AAPC >= -0.6 & AAPC < -0.4 ~ "-0.6 to -0.4",
      AAPC >= -0.4 & AAPC < -0.2 ~ "-0.4 to -0.2",
      AAPC >= -0.2 & AAPC < 0 ~ "-0.2 to 0",
      AAPC >= 0 & AAPC < 0.2 ~ "0 to 0.2",
      AAPC >= 0.2 & AAPC < 0.4 ~ "0.2 to 0.4",
      AAPC >= 0.4 & AAPC < 0.6 ~ "0.4 to 0.6",
      AAPC >= 0.6 & AAPC < 0.8 ~ "0.6 to 0.8",
      AAPC >= 0.8 & AAPC < 1.0 ~ "0.8 to 1.0",
      AAPC >= 1.0 ~ "> 1.0"
    )
  )

# 强制设置因子级别，包含所有分组
world_data$aapc_group <- factor(world_data$aapc_group, levels = all_levels)

# 创建颜色调色板
colors <- c("#08306b", "#08519c", "#2171b5", "#4292c6", "#6baed6", "#c6dbef",
           "#fee0d2", "#fcbba1", "#fc9272", "#fb6a4a", "#de2d26", "#cccccc")
names(colors) <- all_levels

# 创建主地图 - 关键是使用drop=FALSE强制显示所有级别
main_map <- ggplot(world_data) +
  geom_sf(aes(fill = aapc_group), color = "white", linewidth = 0.1) +
  scale_fill_manual(
    values = colors, 
    name = "AAPC (%)", 
    drop = FALSE,  # 这是关键！强制显示所有级别
    na.value = "#cccccc",
    labels = all_levels,  # 明确指定标签
    guide = guide_legend(
      nrow = 4,  # 分4行显示
      byrow = TRUE,
      title.position = "top",
      title.hjust = 0.5,
      override.aes = list(color = "black", linewidth = 0.5)
    )
  ) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 14, face = "bold"),
    legend.text = element_text(size = 9),
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 13, hjust = 0.5),
    legend.key.size = unit(0.7, "cm"),
    legend.key.width = unit(0.9, "cm"),
    plot.margin = margin(10, 10, 10, 10),
    legend.margin = margin(t = 15),
    legend.box.margin = margin(t = 10)
  ) +
  labs(
    title = "Average Annual Percentage Change in Stroke Mortality",
    subtitle = "Among people aged ≥65 years, 1990-2019"
  )

# 保存地图
ggsave("stroke_force_complete_legend_map.png", main_map, 
       width = 20, height = 14, dpi = 300, bg = "white")

print("强制完整图例地图已保存为: stroke_force_complete_legend_map.png")
print(main_map)

# 创建区域地图函数
create_regional_map <- function(world_data, xlim, ylim, title, colors, all_levels) {
  ggplot(world_data) +
    geom_sf(aes(fill = aapc_group), color = "white", linewidth = 0.2) +
    scale_fill_manual(values = colors, name = "AAPC (%)",
                     drop = FALSE, na.value = "#cccccc") +
    coord_sf(xlim = xlim, ylim = ylim, expand = FALSE) +
    theme_void() +
    theme(
      legend.position = "none",
      plot.title = element_text(size = 12, face = "bold", hjust = 0.5),
      plot.margin = margin(5, 5, 5, 5),
      panel.border = element_rect(color = "black", fill = NA, linewidth = 1)
    ) +
    labs(title = title)
}

# 定义关键区域
regions <- list(
  caribbean = list(xlim = c(-90, -55), ylim = c(10, 30),
                  title = "Caribbean"),
  persian_gulf = list(xlim = c(45, 60), ylim = c(22, 32),
                     title = "Persian Gulf"),
  balkans = list(xlim = c(12, 30), ylim = c(40, 48),
                title = "Balkans"),
  southeast_asia = list(xlim = c(90, 140), ylim = c(-10, 25),
                       title = "Southeast Asia"),
  west_africa = list(xlim = c(-20, 20), ylim = c(0, 20),
                    title = "West Africa"),
  northern_europe = list(xlim = c(-10, 35), ylim = c(55, 72),
                        title = "Northern Europe")
)

# 创建区域地图
regional_maps <- list()
for(i in 1:length(regions)) {
  region_name <- names(regions)[i]
  region_info <- regions[[i]]

  regional_maps[[region_name]] <- create_regional_map(
    world_data,
    region_info$xlim,
    region_info$ylim,
    region_info$title,
    colors,
    all_levels
  )
}

# 加载gridExtra用于组合地图
library(gridExtra)
library(grid)

# 组合区域地图
combined_regional <- arrangeGrob(
  regional_maps$caribbean, regional_maps$persian_gulf,
  regional_maps$balkans, regional_maps$southeast_asia,
  regional_maps$west_africa, regional_maps$northern_europe,
  ncol = 3, nrow = 2,
  top = textGrob("Regional Focus Areas: Stroke Mortality AAPC (1990-2019)",
                 gp = gpar(fontsize = 14, fontface = "bold"))
)

# 保存区域地图
ggsave("stroke_force_complete_legend_regional_maps.png", combined_regional,
       width = 16, height = 10, dpi = 300, bg = "white")

print("强制完整图例区域地图已保存为: stroke_force_complete_legend_regional_maps.png")

# 打印统计信息
cat("\n=== 图例完整性检查 ===\n")
cat("定义的分组级别数:", length(all_levels), "\n")
cat("实际数据中的分组:\n")
print(table(world_data$aapc_group, useNA = "ifany"))

cat("\n=== 所有分组级别 ===\n")
for(i in 1:length(all_levels)) {
  count <- sum(world_data$aapc_group == all_levels[i], na.rm = TRUE)
  cat(sprintf("%2d. %-15s (颜色: %s, 国家数: %d)\n",
              i, all_levels[i], colors[i], count))
}

cat("\n=== 图例显示说明 ===\n")
cat("✅ 主地图图例现在显示所有12个分组\n")
cat("✅ 即使某些分组没有数据，也会在图例中显示\n")
cat("✅ 使用drop=FALSE参数强制显示所有级别\n")
cat("✅ 4行布局，每行3个分组，便于阅读\n")
