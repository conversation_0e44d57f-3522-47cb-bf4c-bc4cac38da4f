# 脑卒中65岁以上人群DALYs年均变化率地图 - 平衡颜色版本 (1990-2021)
# 优化颜色分布，确保图例平衡

library(dplyr)
library(ggplot2)
library(maps)
library(viridis)
library(RColorBrewer)
library(gridExtra)
library(grid)

# 1. 加载处理好的数据
cat("Loading processed AAPC data...\n")
country_aapc <- read.csv("stroke_dalys_65plus_aapc_results_1990_2021.csv")

cat("AAPC data summary:\n")
cat("Range:", round(min(country_aapc$aapc), 2), "to", round(max(country_aapc$aapc), 2), "\n")
cat("Mean:", round(mean(country_aapc$aapc), 2), "\n")
cat("Median:", round(median(country_aapc$aapc), 2), "\n")

# 分析数据分布
quantiles <- quantile(country_aapc$aapc, probs = c(0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9))
cat("Quantiles:", round(quantiles, 2), "\n")

# 国家名称标准化
country_mapping <- data.frame(
  gbd_name = c("美国", "英国", "俄罗斯", "韩国", "伊朗伊斯兰共和国", "叙利亚", "委内瑞拉", "玻利维亚", 
               "坦桑尼亚", "刚果民主共和国", "老挝", "越南", "文莱", "北马其顿", "摩尔多瓦",
               "中国", "日本", "德国", "法国", "意大利", "西班牙", "加拿大", "澳大利亚",
               "巴西", "印度", "南非", "埃及", "土耳其", "沙特阿拉伯", "阿联酋"),
  map_name = c("USA", "UK", "Russia", "South Korea", "Iran", "Syria", "Venezuela", "Bolivia",
               "Tanzania", "Democratic Republic of the Congo", "Laos", "Vietnam", "Brunei", 
               "North Macedonia", "Moldova", "China", "Japan", "Germany", "France", "Italy", 
               "Spain", "Canada", "Australia", "Brazil", "India", "South Africa", "Egypt", 
               "Turkey", "Saudi Arabia", "UAE"),
  stringsAsFactors = FALSE
)

# 获取世界地图数据
world_map <- map_data("world")

# 标准化国家名称
country_aapc$region <- country_aapc$location_name
for(i in 1:nrow(country_mapping)) {
  country_aapc$region[country_aapc$location_name == country_mapping$gbd_name[i]] <- country_mapping$map_name[i]
}

# 合并地图数据
map_data_with_aapc <- merge(world_map, country_aapc, by = "region", all.x = TRUE)
map_data_with_aapc <- map_data_with_aapc[order(map_data_with_aapc$order), ]

# 2. 基于数据分布创建平衡的颜色分级
# 使用分位数创建更平衡的分级
breaks <- c(-6.5, -3.5, -2.5, -2.0, -1.5, -1.0, -0.5, 0, 0.5, 2.0)
labels <- c("< -3.5", "-3.5 to -2.5", "-2.5 to -2.0", "-2.0 to -1.5", 
           "-1.5 to -1.0", "-1.0 to -0.5", "-0.5 to 0", "0 to 0.5", "> 0.5")

# 使用更平衡的蓝-红渐变色带
colors <- c("#053061", "#2166ac", "#4393c3", "#92c5de", "#d1e5f0", 
           "#fddbc7", "#f4a582", "#d6604d", "#b2182b")

# 分类AAPC值
map_data_with_aapc$aapc_category <- cut(map_data_with_aapc$aapc, 
                                       breaks = breaks, 
                                       labels = labels, 
                                       include.lowest = TRUE)

# 检查颜色分布
cat("Balanced color distribution:\n")
color_dist <- table(map_data_with_aapc$aapc_category, useNA = "ifany")
print(color_dist)

# 3. 创建主地图
cat("Creating balanced main world map...\n")
main_map <- ggplot(map_data_with_aapc, aes(x = long, y = lat, group = group)) +
  geom_polygon(aes(fill = aapc_category), color = "white", linewidth = 0.1) +
  scale_fill_manual(values = colors, 
                   name = "AAPC (%)",
                   na.value = "grey90",
                   drop = FALSE) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 14, face = "bold"),
    legend.text = element_text(size = 11),
    legend.key.size = unit(1.0, "cm"),
    legend.key.width = unit(1.5, "cm"),
    plot.margin = margin(5, 5, 5, 5, "mm")
  ) +
  guides(fill = guide_legend(nrow = 1, byrow = TRUE, 
                            title.position = "top", title.hjust = 0.5)) +
  coord_fixed(1.3)

# 4. 创建区域放大地图函数
create_regional_map <- function(xlim, ylim, title) {
  ggplot(map_data_with_aapc, aes(x = long, y = lat, group = group)) +
    geom_polygon(aes(fill = aapc_category), color = "white", linewidth = 0.2) +
    scale_fill_manual(values = colors, na.value = "grey90", guide = "none") +
    coord_fixed(1.3, xlim = xlim, ylim = ylim) +
    theme_void() +
    theme(
      plot.title = element_text(size = 11, hjust = 0.5, face = "bold"),
      plot.margin = margin(3, 3, 3, 3, "mm")
    ) +
    ggtitle(title)
}

# 5. 创建6个区域放大地图
cat("Creating regional zoom maps...\n")

caribbean <- create_regional_map(c(-95, -55), c(5, 35), "Caribbean and Central America")
persian_gulf <- create_regional_map(c(45, 65), c(20, 40), "Persian Gulf")
balkans <- create_regional_map(c(10, 30), c(35, 50), "Balkan Peninsula")
southeast_asia <- create_regional_map(c(90, 140), c(-15, 25), "South East Asia")
west_africa_med <- create_regional_map(c(-20, 45), c(0, 40), "West Africa & Eastern Mediterranean")
northern_europe <- create_regional_map(c(-10, 35), c(50, 75), "Northern Europe")

# 6. 组合所有地图
cat("Combining all maps into final layout...\n")

regional_grid <- grid.arrange(
  caribbean, persian_gulf, balkans,
  southeast_asia, west_africa_med, northern_europe,
  ncol = 3, nrow = 2
)

final_plot <- grid.arrange(
  main_map,
  regional_grid,
  ncol = 1,
  heights = c(2.2, 1),
  top = textGrob("Stroke DALYs AAPC in Population Aged ≥65 Years (1990-2021)", 
                gp = gpar(fontsize = 18, fontface = "bold"))
)

# 7. 保存地图
cat("Saving balanced color maps...\n")

ggsave("stroke_dalys_65plus_balanced_colors_complete_layout_1990_2021.png", 
       final_plot, width = 18, height = 14, dpi = 300, bg = "white")

ggsave("stroke_dalys_65plus_balanced_colors_main_map_1990_2021.png", 
       main_map, width = 16, height = 10, dpi = 300, bg = "white")

cat("Balanced color visualization completed successfully!\n")
cat("Files saved:\n")
cat("- stroke_dalys_65plus_balanced_colors_complete_layout_1990_2021.png\n")
cat("- stroke_dalys_65plus_balanced_colors_main_map_1990_2021.png\n")
