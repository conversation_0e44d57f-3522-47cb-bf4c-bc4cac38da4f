# 验证一个中国原则在全球中国卒中发病率趋势对比脚本中的实施
# 检查台湾数据是否正确合并到中国数据中

library(dplyr)
library(readr)

cat("=== 验证一个中国原则实施情况 ===\n")

# 读取死亡数据文件（与主脚本相同）
mortality_files <- c(
  "死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-1/IHME-GBD_2021_DATA-336925ca-1.csv",
  "死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-2/IHME-GBD_2021_DATA-336925ca-2.csv"
)

all_mortality_data <- data.frame()

for(file in mortality_files) {
  if(file.exists(file)) {
    temp_data <- read_csv(file, show_col_types = FALSE)
    all_mortality_data <- rbind(all_mortality_data, temp_data)
    cat("已读取文件:", basename(file), "行数:", nrow(temp_data), "\n")
  }
}

# 定义35岁以上年龄组
age_35plus <- c("35-39岁", "40-44岁", "45-49岁", "50-54岁", "55-59岁",
                "60-64岁", "65-69岁", "70-74岁", "75-79岁", "80-84岁", "85岁以上")

# 检查原始数据中的中国和台湾数据
cat("\n=== 检查原始数据 ===\n")

china_raw <- all_mortality_data %>%
  filter(
    location_name == "中国",
    cause_name == "脑卒中",
    measure_name == "死亡",
    age_name %in% age_35plus,
    metric_name == "率",
    sex_name == "合计",
    year %in% c(1990, 2021)
  )

taiwan_raw <- all_mortality_data %>%
  filter(
    location_name == "台湾",
    cause_name == "脑卒中",
    measure_name == "死亡",
    age_name %in% age_35plus,
    metric_name == "率",
    sex_name == "合计",
    year %in% c(1990, 2021)
  )

cat("原始中国数据行数:", nrow(china_raw), "\n")
cat("原始台湾数据行数:", nrow(taiwan_raw), "\n")

if(nrow(china_raw) > 0) {
  cat("中国数据样本（1990年）:\n")
  china_1990 <- china_raw[china_raw$year == 1990, ]
  if(nrow(china_1990) > 0) {
    cat("  年龄组:", china_1990$age_name[1], "发病率:", round(china_1990$val[1], 2), "\n")
  }
}

if(nrow(taiwan_raw) > 0) {
  cat("台湾数据样本（1990年）:\n")
  taiwan_1990 <- taiwan_raw[taiwan_raw$year == 1990, ]
  if(nrow(taiwan_1990) > 0) {
    cat("  年龄组:", taiwan_1990$age_name[1], "发病率:", round(taiwan_1990$val[1], 2), "\n")
  }
}

# 模拟主脚本的合并过程
cat("\n=== 模拟一个中国原则合并过程 ===\n")

china_taiwan_combined <- all_mortality_data %>%
  filter(
    location_name %in% c("中国", "台湾"),
    cause_name == "脑卒中",
    measure_name == "死亡",
    age_name %in% age_35plus,
    metric_name == "率",
    sex_name == "合计",
    year %in% c(1990, 2021)
  ) %>%
  group_by(age_name, year) %>%
  summarise(
    val = mean(val, na.rm = TRUE),
    upper = mean(upper, na.rm = TRUE),
    lower = mean(lower, na.rm = TRUE),
    .groups = 'drop'
  ) %>%
  mutate(location_name = "中国")

cat("合并后的中国数据行数:", nrow(china_taiwan_combined), "\n")

if(nrow(china_taiwan_combined) > 0) {
  cat("合并后数据样本（1990年）:\n")
  combined_1990 <- china_taiwan_combined[china_taiwan_combined$year == 1990, ]
  if(nrow(combined_1990) > 0) {
    cat("  年龄组:", combined_1990$age_name[1], "合并后发病率:", round(combined_1990$val[1], 2), "\n")
  }
}

# 验证合并效果
cat("\n=== 验证合并效果 ===\n")

if(nrow(china_raw) > 0 && nrow(taiwan_raw) > 0 && nrow(china_taiwan_combined) > 0) {
  # 比较1990年第一个年龄组的数据
  china_val <- china_raw$val[china_raw$year == 1990][1]
  taiwan_val <- taiwan_raw$val[taiwan_raw$year == 1990][1]
  combined_val <- china_taiwan_combined$val[china_taiwan_combined$year == 1990][1]
  
  expected_combined <- mean(c(china_val, taiwan_val), na.rm = TRUE)
  
  cat("原始中国值:", round(china_val, 2), "\n")
  cat("原始台湾值:", round(taiwan_val, 2), "\n")
  cat("期望合并值:", round(expected_combined, 2), "\n")
  cat("实际合并值:", round(combined_val, 2), "\n")
  
  if(abs(combined_val - expected_combined) < 0.01) {
    cat("✓ 一个中国原则实施正确：台湾数据已成功合并到中国数据中\n")
  } else {
    cat("✗ 一个中国原则实施可能有问题\n")
  }
} else {
  cat("数据不足，无法完成验证\n")
}

cat("\n=== 验证完成 ===\n")
