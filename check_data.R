# 检查数据库中的measure_name类型
data5 <- read.csv("死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-5/IHME-GBD_2021_DATA-336925ca-5.csv")
print("File 5 - Unique measure_name values:")
print(unique(data5$measure_name))

data6 <- read.csv("死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-6/IHME-GBD_2021_DATA-336925ca-6.csv")
print("File 6 - Unique measure_name values:")
print(unique(data6$measure_name))

# 检查年份范围
print("File 5 - Year range:")
print(range(data5$year))

print("File 6 - Year range:")
print(range(data6$year))

# 检查65岁以上年龄组
print("File 5 - Age groups:")
print(unique(data5$age_name))

print("File 6 - Age groups:")
print(unique(data6$age_name))
