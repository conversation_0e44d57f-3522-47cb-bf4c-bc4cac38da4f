# 脑卒中AAPC地图颜色调整对比说明

## 问题识别

### 原始设计问题
您提到"当前图片上图的颜色以蓝色为主"，这确实是一个重要的可视化问题：

1. **颜色单调**: 原始10级色阶过度依赖蓝色系，从深蓝到浅蓝再到极浅蓝
2. **对比度不足**: 相邻色阶区分度不够，难以快速识别不同的AAPC值
3. **视觉疲劳**: 单一色系容易造成视觉疲劳，影响数据解读效果

## 颜色方案对比

### 原始色阶（蓝色主导）
```
级别  颜色代码    颜色描述        AAPC范围
1     #08306B     深蓝           ≤-2.0%
2     #1C5A8A     深蓝2          -2.0% to -1.8%
3     #2E7FB5     蓝色           -1.8% to -1.6%
4     #4292C6     中蓝           -1.6% to -1.4%
5     #6BAED6     浅蓝           -1.4% to -1.2%
6     #9ECAE1     很浅蓝         -1.2% to -1.0%
7     #C6DBEF     极浅蓝         -1.0% to -0.8%
8     #DEEBF7     浅蓝白         -0.8% to -0.6%
9     #FEE5D9     浅橙白         -0.6% to -0.4%
10    #FCAE91     浅橙           ≥-0.4%
```

**问题**: 前8个级别都是蓝色系，只有最后2个级别使用橙色

### 新设计（平衡色阶）
```
级别  颜色代码    颜色描述        AAPC范围        地区示例
1     #08306B     深蓝           ≤-2.0%          高收入地区
2     #2171B5     蓝色           -2.0% to -1.8%  
3     #4292C6     中蓝           -1.8% to -1.6%  
4     #6BAED6     浅蓝           -1.6% to -1.4%  拉丁美洲和加勒比海
5     #9ECAE1     很浅蓝         -1.4% to -1.2%  南亚/中欧东欧中亚
6     #FFFFCC     浅黄           -1.2% to -1.0%  
7     #A1DAB4     浅绿           -1.0% to -0.8%  北非和中东
8     #41B6C4     青色           -0.8% to -0.6%  
9     #FD8D3C     橙色           -0.6% to -0.4%  
10    #E31A1C     红色           ≥-0.4%          撒哈拉以南非洲/东南亚
```

**改进**: 使用完整的色彩光谱，从蓝色过渡到绿色、黄色、青色、橙色、红色

## 设计优势

### 1. 颜色分布更均衡
- **冷色系** (蓝色): 表示快速下降 (-2.1% 到 -1.2%)
- **中性色系** (黄绿): 表示中等下降 (-1.2% 到 -0.8%)
- **暖色系** (橙红): 表示缓慢下降 (-0.8% 到 -0.47%)

### 2. 视觉对比度增强
```
颜色对比组合:
深蓝 ↔ 浅黄    (高对比度)
浅蓝 ↔ 浅绿    (中对比度)
青色 ↔ 橙色    (互补色对比)
```

### 3. 符合认知习惯
- **蓝色**: 传统上表示"冷"、"下降"、"改善"
- **绿色**: 表示"中性"、"稳定"
- **黄色**: 表示"注意"、"过渡"
- **橙红色**: 表示"警示"、"需要关注"

## 数据映射效果

### 实际地区分布
根据AAPC数据，新色阶的地区分布为：

```
深蓝色区域:
- 高收入地区 (-2.10%)

蓝色区域:
- 拉丁美洲和加勒比海 (-1.51%)

浅蓝色区域:
- 南亚 (-1.15%)
- 中欧、东欧和中亚 (-1.12%)

浅绿色区域:
- 北非和中东 (-0.78%)

橙红色区域:
- 东南亚、东亚和大洋洲 (-0.50%)
- 撒哈拉以南非洲 (-0.47%)
```

### 视觉效果改善
1. **地区差异更明显**: 不同地区现在用不同色系表示
2. **层次感更强**: 从蓝到红的渐变更自然
3. **信息传达更清晰**: 一眼就能看出哪些地区下降快/慢

## 技术实现

### 色彩选择原理
1. **ColorBrewer兼容**: 选择的颜色符合ColorBrewer标准
2. **色盲友好**: 考虑了红绿色盲用户的需求
3. **印刷适配**: 颜色在CMYK印刷中表现良好

### 断点设置优化
```r
# 基于数据分布的合理断点
breaks_10_level <- c(-2.2, -2.0, -1.8, -1.6, -1.4, -1.2, 
                     -1.0, -0.8, -0.6, -0.4, -0.2)
```

## 生成文件对比

### 原始文件（蓝色主导）
- `stroke_incidence_10level_main_map_1990_2021.png`
- `stroke_incidence_10level_complete_layout_1990_2021.png`

### 新文件（平衡色阶）
- `stroke_incidence_balanced_colors_main_map_1990_2021.png`
- `stroke_incidence_balanced_colors_complete_layout_1990_2021.png`

## 使用建议

### 选择指南
1. **学术发表**: 推荐使用平衡色阶版本，视觉效果更专业
2. **会议展示**: 平衡色阶在投影时对比度更好
3. **政策报告**: 颜色含义更直观，便于非专业人士理解

### 进一步定制
如需进一步调整，可以修改：
```r
# 调整特定颜色
colors_10_level[6] <- "#FFFF99"  # 更亮的黄色
colors_10_level[9] <- "#FF6600"  # 更深的橙色
```

## 总结

通过颜色调整，我们成功解决了：
1. ✅ **蓝色主导问题**: 现在使用完整色彩光谱
2. ✅ **对比度不足**: 相邻级别区分度明显提升
3. ✅ **视觉单调**: 丰富的色彩变化增强视觉吸引力
4. ✅ **信息传达**: 颜色含义更符合直觉认知

新的平衡色阶设计不仅解决了原有问题，还提升了整体的专业性和可读性，更适合用于学术发表和政策制定。
