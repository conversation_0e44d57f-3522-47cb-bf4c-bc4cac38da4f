# 全球与中国脑卒中死亡率趋势对比 - 关键发现图
# 简化版可视化，突出主要发现
#
# 重要说明：本脚本遵守一个中国原则
# - 基于已生成的数据文件进行分析，该数据文件应当遵守一个中国原则
# - 如果数据源包含台湾数据，应将台湾数据合并到中国数据中
# - 确保中国数据代表完整的中国地区

library(ggplot2)
library(dplyr)
library(readr)
library(scales)
library(gridExtra)
library(grid)

# 设置中文字体
if (.Platform$OS.type == "windows") {
  windowsFonts(SimSun = windowsFont("SimSun"))
  par(family = "SimSun")
}

# 读取已生成的数据
data <- read_csv("global_china_stroke_mortality_trend_data_1990_2021.csv", show_col_types = FALSE)

cat("读取数据行数:", nrow(data), "\n")
cat("年份范围:", min(data$year), "-", max(data$year), "\n")

# 1. 主要趋势对比图
p1 <- ggplot(data, aes(x = year, y = mortality_rate, color = location_name, fill = location_name)) +
  geom_line(linewidth = 2, alpha = 0.9) +
  geom_point(size = 3, alpha = 0.8) +
  geom_ribbon(aes(ymin = lower_ci, ymax = upper_ci), alpha = 0.2, color = NA) +
  # 添加起点和终点标注
  geom_text(data = data %>% filter(year == 1990), 
            aes(label = paste0(round(mortality_rate, 0), "/10万")), 
            hjust = 1.2, vjust = -0.5, size = 4, fontface = "bold", show.legend = FALSE) +
  geom_text(data = data %>% filter(year == 2021), 
            aes(label = paste0(round(mortality_rate, 0), "/10万")), 
            hjust = -0.2, vjust = -0.5, size = 4, fontface = "bold", show.legend = FALSE) +
  labs(
    title = "全球与中国35岁以上人群脑卒中死亡率趋势对比 (1990-2021)",
    subtitle = "年龄标准化死亡率（每10万人）",
    x = "年份",
    y = "死亡率（每10万人）",
    color = "地区",
    fill = "地区"
  ) +
  scale_color_manual(values = c("中国" = "#E31A1C", "全球" = "#1F78B4")) +
  scale_fill_manual(values = c("中国" = "#E31A1C", "全球" = "#1F78B4")) +
  scale_x_continuous(breaks = seq(1990, 2021, 5), limits = c(1988, 2023)) +
  scale_y_continuous(labels = comma_format()) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 16, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5, size = 12),
    legend.position = "bottom",
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 11),
    axis.title = element_text(size = 12, face = "bold"),
    axis.text = element_text(size = 10),
    panel.grid.minor = element_blank()
  )

# 2. 计算关键指标
china_1990 <- data$mortality_rate[data$location_name == "中国" & data$year == 1990]
china_2021 <- data$mortality_rate[data$location_name == "中国" & data$year == 2021]
global_1990 <- data$mortality_rate[data$location_name == "全球" & data$year == 1990]
global_2021 <- data$mortality_rate[data$location_name == "全球" & data$year == 2021]

# 计算AAPC
china_aapc <- ((china_2021 / china_1990)^(1/31) - 1) * 100
global_aapc <- ((global_2021 / global_1990)^(1/31) - 1) * 100

# 创建关键指标数据框
key_metrics <- data.frame(
  指标 = c("1990年死亡率", "2021年死亡率", "绝对下降", "相对下降", "AAPC"),
  中国 = c(
    paste0(round(china_1990, 0), "/10万"),
    paste0(round(china_2021, 0), "/10万"),
    paste0(round(china_1990 - china_2021, 0), "/10万"),
    paste0(round((china_2021/china_1990 - 1) * 100, 1), "%"),
    paste0(round(china_aapc, 2), "%/年")
  ),
  全球 = c(
    paste0(round(global_1990, 0), "/10万"),
    paste0(round(global_2021, 0), "/10万"),
    paste0(round(global_1990 - global_2021, 0), "/10万"),
    paste0(round((global_2021/global_1990 - 1) * 100, 1), "%"),
    paste0(round(global_aapc, 2), "%/年")
  ),
  stringsAsFactors = FALSE
)

# 3. 创建差距分析图
gap_data <- data %>%
  select(year, location_name, mortality_rate) %>%
  tidyr::pivot_wider(names_from = location_name, values_from = mortality_rate) %>%
  mutate(
    绝对差距 = 中国 - 全球,
    相对差距 = (中国 / 全球 - 1) * 100
  )

p2 <- ggplot(gap_data, aes(x = year, y = 绝对差距)) +
  geom_line(linewidth = 2, color = "#FF7F00", alpha = 0.9) +
  geom_point(size = 3, color = "#FF7F00", alpha = 0.8) +
  geom_hline(yintercept = 0, linetype = "dashed", color = "gray50") +
  # 添加起点和终点标注
  geom_text(data = gap_data %>% filter(year == 1990), 
            aes(label = paste0("+", round(绝对差距, 0))), 
            hjust = 1.2, vjust = -0.5, size = 4, fontface = "bold") +
  geom_text(data = gap_data %>% filter(year == 2021), 
            aes(label = paste0("+", round(绝对差距, 0))), 
            hjust = -0.2, vjust = -0.5, size = 4, fontface = "bold") +
  labs(
    title = "中国与全球脑卒中死亡率差距变化",
    x = "年份",
    y = "绝对差距（中国-全球，每10万人）"
  ) +
  scale_x_continuous(breaks = seq(1990, 2021, 5), limits = c(1988, 2023)) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    axis.title = element_text(size = 12, face = "bold"),
    axis.text = element_text(size = 10),
    panel.grid.minor = element_blank()
  )

# 4. 创建AAPC对比图
aapc_data <- data.frame(
  地区 = c("中国", "全球"),
  AAPC = c(china_aapc, global_aapc)
)

p3 <- ggplot(aapc_data, aes(x = 地区, y = AAPC, fill = 地区)) +
  geom_col(alpha = 0.8, width = 0.6) +
  geom_text(aes(label = paste0(round(AAPC, 2), "%/年")), 
            vjust = -0.5, size = 5, fontface = "bold") +
  labs(
    title = "平均年度百分比变化对比",
    x = "地区",
    y = "AAPC (%/年)"
  ) +
  scale_fill_manual(values = c("中国" = "#E31A1C", "全球" = "#1F78B4")) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    legend.position = "none",
    axis.title = element_text(size = 12, face = "bold"),
    axis.text = element_text(size = 11)
  )

# 5. 创建表格图
library(gridExtra)
library(grid)

# 创建表格
table_grob <- tableGrob(key_metrics, rows = NULL, 
                       theme = ttheme_default(
                         core = list(fg_params = list(cex = 0.8)),
                         colhead = list(fg_params = list(cex = 0.9, fontface = "bold"))
                       ))

# 保存关键发现图
png("global_china_stroke_mortality_key_findings_1990_2021.png", 
    width = 16, height = 12, units = "in", res = 300)

grid.arrange(
  p1,
  arrangeGrob(p2, p3, ncol = 2),
  arrangeGrob(table_grob, ncol = 1),
  ncol = 1,
  heights = c(2, 1, 0.8),
  top = textGrob("全球与中国脑卒中死亡率趋势对比 - 关键发现", 
                 gp = gpar(fontsize = 18, fontface = "bold"))
)

dev.off()

# 输出关键发现摘要
cat("\n=== 全球与中国脑卒中死亡率趋势对比 - 关键发现 ===\n")
cat("数据处理原则: 遵守一个中国原则，确保中国数据的完整性和统一性\n\n")

cat("📊 死亡率水平对比:\n")
cat("1990年 - 中国:", round(china_1990, 0), "/10万 | 全球:", round(global_1990, 0), "/10万\n")
cat("2021年 - 中国:", round(china_2021, 0), "/10万 | 全球:", round(global_2021, 0), "/10万\n\n")

cat("📈 变化趋势对比:\n")
cat("中国AAPC:", round(china_aapc, 2), "%/年 | 全球AAPC:", round(global_aapc, 2), "%/年\n")
cat("中国绝对下降:", round(china_1990 - china_2021, 0), "/10万 | 全球绝对下降:", round(global_1990 - global_2021, 0), "/10万\n")
cat("中国相对下降:", round((china_2021/china_1990 - 1) * 100, 1), "% | 全球相对下降:", round((global_2021/global_1990 - 1) * 100, 1), "%\n\n")

cat("🔍 差距分析:\n")
cat("1990年差距:", round(china_1990 - global_1990, 0), "/10万 (中国高", round((china_1990/global_1990 - 1) * 100, 1), "%)\n")
cat("2021年差距:", round(china_2021 - global_2021, 0), "/10万 (中国高", round((china_2021/global_2021 - 1) * 100, 1), "%)\n")
cat("差距变化:", round((china_2021 - global_2021) - (china_1990 - global_1990), 0), "/10万\n\n")

cat("💡 主要结论:\n")
cat("1. 中国和全球脑卒中死亡率均呈显著下降趋势\n")
cat("2. 全球下降速度更快 (AAPC: ", round(global_aapc, 2), "% vs ", round(china_aapc, 2), "%)\n")
cat("3. 中国死亡率始终高于全球平均水平\n")
cat("4. 绝对差距在缩小，但相对差距在扩大\n\n")

cat("关键发现图表已保存为: global_china_stroke_mortality_key_findings_1990_2021.png\n")

cat("\n分析完成！\n")
