# 测试区域地图生成
library(ggplot2)
library(dplyr)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(RColorBrewer)
library(countrycode)
library(gridExtra)
library(grid)

# 读取数据
data <- read.csv("脑卒中65岁以上人群患病率分析表.csv", 
                 stringsAsFactors = FALSE, 
                 fileEncoding = "UTF-8")

# 提取地区AAPC数据
region_aapc <- data %>%
  filter(分类 %in% c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", 
                    "高收入", "中欧、东欧和中亚", "拉丁美洲和加勒比海", 
                    "北非和中东")) %>%
  select(分类, AAPC = `AAPC...`)

# 创建国家到地区映射（简化版）
country_region_mapping <- data.frame(
  country = c(
    # 高收入国家
    "United States", "Germany", "United Kingdom", "France", "Italy", 
    "Canada", "Spain", "Netherlands", "Belgium", "Switzerland", "Austria", 
    "Sweden", "Norway", "Denmark", "Finland", "Ireland", "Portugal", 
    "Greece", "Israel", "Luxembourg", "Iceland", "Malta", "Cyprus",
    
    # 拉丁美洲和加勒比海
    "Brazil", "Mexico", "Colombia", "Argentina", "Peru", "Venezuela", 
    "Chile", "Ecuador", "Guatemala", "Cuba", "Bolivia", "Haiti", 
    "Dominican Republic", "Honduras", "Paraguay", "Nicaragua", 
    "El Salvador", "Costa Rica", "Panama", "Uruguay", "Jamaica", 
    "Trinidad and Tobago", "Guyana", "Suriname", "Belize", "Barbados",
    
    # 北非和中东
    "Egypt", "Iran", "Turkey", "Iraq", "Saudi Arabia", "Yemen", "Syria", 
    "Jordan", "Lebanon", "United Arab Emirates", "Oman", "Kuwait", "Qatar", 
    "Bahrain", "Algeria", "Morocco", "Sudan", "Tunisia", "Libya",
    
    # 东南亚、东亚和大洋洲
    "China", "Indonesia", "Japan", "Philippines", "Vietnam", "Thailand", 
    "Myanmar", "South Korea", "Malaysia", "Cambodia", "Laos", "Singapore", 
    "Mongolia", "Brunei", "Australia", "New Zealand",
    
    # 中欧、东欧和中亚
    "Russia", "Poland", "Ukraine", "Romania", "Czech Republic", "Hungary", 
    "Belarus", "Bulgaria", "Serbia", "Slovakia", "Croatia", 
    "Bosnia and Herzegovina", "Albania", "Lithuania", "Slovenia", 
    "Latvia", "Estonia", "North Macedonia", "Moldova", "Montenegro"
  ),
  region = c(
    rep("高收入", 23),
    rep("拉丁美洲和加勒比海", 25),
    rep("北非和中东", 19),
    rep("东南亚、东亚和大洋洲", 16),
    rep("中欧、东欧和中亚", 20)
  )
)

# 合并数据
country_data <- country_region_mapping %>%
  left_join(region_aapc, by = c("region" = "分类"))

# 获取世界地图数据
world <- ne_countries(scale = "medium", returnclass = "sf")

# 标准化国家名称
world$name_clean <- countrycode(world$name, "country.name", "country.name")
country_data$country_clean <- countrycode(country_data$country, 
                                         "country.name", "country.name")

# 合并地图数据和AAPC数据
world_data <- world %>%
  left_join(country_data, by = c("name_clean" = "country_clean"))

# 创建AAPC分组
world_data <- world_data %>%
  mutate(
    aapc_group = case_when(
      is.na(AAPC) ~ "No data",
      AAPC < -1.0 ~ "< -1.0",
      AAPC >= -1.0 & AAPC < -0.5 ~ "-1.0 to < -0.5",
      AAPC >= -0.5 & AAPC < 0 ~ "-0.5 to < 0",
      AAPC >= 0 & AAPC < 0.5 ~ "0 to < 0.5",
      AAPC >= 0.5 & AAPC < 1.0 ~ "0.5 to < 1.0",
      AAPC >= 1.0 & AAPC < 1.5 ~ "1.0 to < 1.5",
      AAPC >= 1.5 ~ "≥ 1.5"
    ),
    aapc_group = factor(aapc_group, 
                       levels = c("< -1.0", "-1.0 to < -0.5", "-0.5 to < 0", 
                                 "0 to < 0.5", "0.5 to < 1.0", "1.0 to < 1.5", 
                                 "≥ 1.5", "No data"))
  )

# 创建颜色调色板
colors <- c("#08519c", "#3182bd", "#6baed6", "#c6dbef", 
           "#fee0d2", "#fc9272", "#de2d26", "#a50f15", "#cccccc")
names(colors) <- levels(world_data$aapc_group)

# 创建区域地图函数
create_regional_map <- function(world_data, xlim, ylim, title, colors) {
  ggplot(world_data) +
    geom_sf(aes(fill = aapc_group), color = "white", size = 0.2) +
    scale_fill_manual(values = colors, name = "AAPC (%)", drop = FALSE) +
    coord_sf(xlim = xlim, ylim = ylim, expand = FALSE) +
    theme_void() +
    theme(
      legend.position = "none",
      plot.title = element_text(size = 12, face = "bold", hjust = 0.5),
      plot.margin = margin(5, 5, 5, 5),
      panel.border = element_rect(color = "black", fill = NA, linewidth = 1)
    ) +
    labs(title = title)
}

# 测试单个区域地图
caribbean_map <- create_regional_map(
  world_data, 
  c(-90, -55), c(10, 30), 
  "Caribbean", 
  colors
)

# 保存测试地图
ggsave("test_caribbean_map.png", caribbean_map, 
       width = 8, height = 6, dpi = 300, bg = "white")

print("测试加勒比海地图已保存")
print(caribbean_map)

# 创建所有区域地图
regions <- list(
  caribbean = list(xlim = c(-90, -55), ylim = c(10, 30), 
                  title = "Caribbean"),
  persian_gulf = list(xlim = c(45, 60), ylim = c(22, 32), 
                     title = "Persian Gulf"),
  balkans = list(xlim = c(12, 30), ylim = c(40, 48), 
                title = "Balkans"),
  southeast_asia = list(xlim = c(90, 140), ylim = c(-10, 25), 
                       title = "Southeast Asia"),
  west_africa = list(xlim = c(-20, 20), ylim = c(0, 20), 
                    title = "West Africa"),
  northern_europe = list(xlim = c(-10, 35), ylim = c(55, 72), 
                        title = "Northern Europe")
)

# 创建区域地图列表
regional_maps <- list()
for(i in 1:length(regions)) {
  region_name <- names(regions)[i]
  region_info <- regions[[i]]
  
  regional_maps[[region_name]] <- create_regional_map(
    world_data, 
    region_info$xlim, 
    region_info$ylim, 
    region_info$title, 
    colors
  )
}

# 使用正确的方法保存组合地图
png("test_stroke_regional_maps.png", width = 16, height = 10, 
    units = "in", res = 300)

grid.arrange(
  regional_maps$caribbean, regional_maps$persian_gulf,
  regional_maps$balkans, regional_maps$southeast_asia,
  regional_maps$west_africa, regional_maps$northern_europe,
  ncol = 3, nrow = 2,
  top = textGrob("Regional Focus Areas: Stroke Mortality AAPC (1990-2019)", 
                 gp = gpar(fontsize = 14, fontface = "bold"))
)

dev.off()

print("测试区域组合地图已保存为: test_stroke_regional_maps.png")
