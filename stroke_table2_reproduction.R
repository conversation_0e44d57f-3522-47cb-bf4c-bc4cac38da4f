# 脑卒中相关DALYs分析复现 - Table 2
# 系统评估1990-2019年间，全球及不同SDI分层下，≥65岁人群脑卒中相关DALYs的变化趋势及主要危险因素

# 加载必要的包
library(dplyr)
library(readr)
library(tidyr)
library(ggplot2)
library(knitr)
library(kableExtra)

# 1. 数据准备函数
prepare_stroke_data <- function() {
  # 读取现有的脑卒中患病率数据作为基础
  stroke_data <- read_csv("脑卒中65岁以上人群患病率分析表.csv", locale = locale(encoding = "UTF-8"))
  
  # 创建模拟的DALYs数据（基于患病率数据结构）
  # 在实际应用中，这里应该读取真实的GBD DALYs数据
  
  # 定义主要危险因素
  risk_factors <- c(
    "高血压 (High blood pressure)",
    "高胆固醇 (High cholesterol)", 
    "高血糖 (High fasting plasma glucose)",
    "吸烟 (Smoking)",
    "饮酒 (Alcohol use)",
    "不健康饮食 (Diet high in sodium)",
    "空气污染 (Air pollution)",
    "低温 (Low temperature)",
    "高温 (High temperature)"
  )
  
  # 定义SDI分层
  sdi_levels <- c("Global", "High SDI", "High-middle SDI", "Middle SDI", "Low-middle SDI", "Low SDI")
  
  return(list(
    stroke_data = stroke_data,
    risk_factors = risk_factors,
    sdi_levels = sdi_levels
  ))
}

# 2. AAPC计算函数
calculate_aapc <- function(dalys_1990, dalys_2019, year_start = 1990, year_end = 2019) {
  # AAPC = (ln(DALYs_2019) - ln(DALYs_1990)) / (2019 - 1990) * 100
  aapc <- (log(dalys_2019) - log(dalys_1990)) / (year_end - year_start) * 100
  return(aapc)
}

# 计算AAPC的置信区间
calculate_aapc_ci <- function(dalys_1990, dalys_1990_lower, dalys_1990_upper,
                              dalys_2019, dalys_2019_lower, dalys_2019_upper,
                              year_start = 1990, year_end = 2019) {
  
  # 主要AAPC
  aapc_main <- calculate_aapc(dalys_1990, dalys_2019, year_start, year_end)
  
  # 保守估计（下限）：使用1990年上限和2019年下限
  aapc_lower <- calculate_aapc(dalys_1990_upper, dalys_2019_lower, year_start, year_end)
  
  # 乐观估计（上限）：使用1990年下限和2019年上限  
  aapc_upper <- calculate_aapc(dalys_1990_lower, dalys_2019_upper, year_start, year_end)
  
  return(list(
    aapc = aapc_main,
    aapc_lower = aapc_lower,
    aapc_upper = aapc_upper
  ))
}

# 3. 创建模拟的风险因素DALYs数据
create_simulated_dalys_data <- function(data_info) {
  set.seed(123) # 确保结果可重现
  
  # 基于现有数据创建模拟的风险因素DALYs数据
  simulated_data <- expand.grid(
    risk_factor = data_info$risk_factors,
    sdi_level = data_info$sdi_levels,
    stringsAsFactors = FALSE
  )
  
  # 为每个组合生成模拟的DALYs数据
  simulated_data <- simulated_data %>%
    mutate(
      # 1990年DALYs (每10万人)
      dalys_1990 = case_when(
        risk_factor == "高血压 (High blood pressure)" ~ runif(n(), 800, 1200),
        risk_factor == "高胆固醇 (High cholesterol)" ~ runif(n(), 300, 600),
        risk_factor == "高血糖 (High fasting plasma glucose)" ~ runif(n(), 200, 500),
        risk_factor == "吸烟 (Smoking)" ~ runif(n(), 400, 800),
        risk_factor == "饮酒 (Alcohol use)" ~ runif(n(), 100, 300),
        risk_factor == "不健康饮食 (Diet high in sodium)" ~ runif(n(), 250, 550),
        risk_factor == "空气污染 (Air pollution)" ~ runif(n(), 150, 400),
        risk_factor == "低温 (Low temperature)" ~ runif(n(), 50, 150),
        TRUE ~ runif(n(), 30, 120)
      ),
      
      # 2019年DALYs (每10万人) - 考虑不同变化趋势
      dalys_2019 = case_when(
        risk_factor == "高血压 (High blood pressure)" ~ dalys_1990 * runif(n(), 0.7, 0.9), # 下降
        risk_factor == "高胆固醇 (High cholesterol)" ~ dalys_1990 * runif(n(), 0.6, 0.8), # 明显下降
        risk_factor == "高血糖 (High fasting plasma glucose)" ~ dalys_1990 * runif(n(), 1.1, 1.4), # 上升
        risk_factor == "吸烟 (Smoking)" ~ dalys_1990 * runif(n(), 0.5, 0.7), # 大幅下降
        risk_factor == "饮酒 (Alcohol use)" ~ dalys_1990 * runif(n(), 0.8, 1.2), # 轻微变化
        risk_factor == "不健康饮食 (Diet high in sodium)" ~ dalys_1990 * runif(n(), 0.9, 1.1), # 基本稳定
        risk_factor == "空气污染 (Air pollution)" ~ dalys_1990 * runif(n(), 1.2, 1.6), # 上升
        risk_factor == "低温 (Low temperature)" ~ dalys_1990 * runif(n(), 0.8, 1.2), # 轻微变化
        TRUE ~ dalys_1990 * runif(n(), 0.9, 1.3)
      )
    ) %>%
    mutate(
      # 生成95%不确定区间
      dalys_1990_lower = dalys_1990 * 0.85,
      dalys_1990_upper = dalys_1990 * 1.15,
      dalys_2019_lower = dalys_2019 * 0.85,
      dalys_2019_upper = dalys_2019 * 1.15
    )
  
  return(simulated_data)
}

# 4. 计算所有组合的AAPC
calculate_all_aapc <- function(dalys_data) {
  results <- dalys_data %>%
    rowwise() %>%
    mutate(
      aapc_results = list(calculate_aapc_ci(
        dalys_1990, dalys_1990_lower, dalys_1990_upper,
        dalys_2019, dalys_2019_lower, dalys_2019_upper
      ))
    ) %>%
    mutate(
      aapc = aapc_results$aapc,
      aapc_lower = aapc_results$aapc_lower,
      aapc_upper = aapc_results$aapc_upper
    ) %>%
    select(-aapc_results)
  
  return(results)
}

# 5. 格式化数值显示
format_dalys_with_ui <- function(value, lower, upper, digits = 1) {
  paste0(round(value, digits), " (", round(lower, digits), "-", round(upper, digits), ")")
}

format_aapc_with_ci <- function(aapc, lower, upper, digits = 2) {
  paste0(round(aapc, digits), " (", round(lower, digits), " to ", round(upper, digits), ")")
}

# 6. 创建Table 2格式的结果表
create_table2 <- function(results_data) {
  # 重新整理数据为表格格式
  table_data <- results_data %>%
    mutate(
      dalys_1990_formatted = format_dalys_with_ui(dalys_1990, dalys_1990_lower, dalys_1990_upper),
      dalys_2019_formatted = format_dalys_with_ui(dalys_2019, dalys_2019_lower, dalys_2019_upper),
      aapc_formatted = format_aapc_with_ci(aapc, aapc_lower, aapc_upper)
    ) %>%
    select(risk_factor, sdi_level, dalys_1990_formatted, dalys_2019_formatted, aapc_formatted)
  
  # 转换为宽格式表格
  table_wide <- table_data %>%
    pivot_longer(cols = c(dalys_1990_formatted, dalys_2019_formatted, aapc_formatted),
                 names_to = "metric", values_to = "value") %>%
    mutate(
      metric = case_when(
        metric == "dalys_1990_formatted" ~ "1990年DALYs",
        metric == "dalys_2019_formatted" ~ "2019年DALYs", 
        metric == "aapc_formatted" ~ "AAPC (%)"
      )
    ) %>%
    pivot_wider(names_from = sdi_level, values_from = value)
  
  return(table_wide)
}

# 7. 主执行函数
main_analysis <- function() {
  cat("开始脑卒中DALYs分析复现...\n")
  
  # 准备数据
  data_info <- prepare_stroke_data()
  cat("✓ 数据准备完成\n")
  
  # 创建模拟DALYs数据
  dalys_data <- create_simulated_dalys_data(data_info)
  cat("✓ 模拟DALYs数据创建完成\n")
  
  # 计算AAPC
  results <- calculate_all_aapc(dalys_data)
  cat("✓ AAPC计算完成\n")
  
  # 创建Table 2
  table2 <- create_table2(results)
  cat("✓ Table 2创建完成\n")
  
  # 保存结果
  write_csv(results, "stroke_dalys_table2_detailed_results.csv")
  write_csv(table2, "stroke_dalys_table2_formatted.csv")
  
  return(list(
    detailed_results = results,
    table2 = table2,
    summary_stats = list(
      total_combinations = nrow(results),
      risk_factors_count = length(unique(results$risk_factor)),
      sdi_levels_count = length(unique(results$sdi_level))
    )
  ))
}

# 执行分析
if (!interactive()) {
  results <- main_analysis()
  cat("分析完成！结果已保存到CSV文件中。\n")
}
