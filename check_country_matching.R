# 检查国家名称匹配问题
library(dplyr)
library(sf)
library(rnaturalearth)

cat("=== 检查国家名称匹配问题 ===\n")

# 读取AAPC数据
country_aapc <- read.csv("stroke_dalys_35plus_aapc_results_1990_2021.csv", 
                        stringsAsFactors = FALSE, 
                        fileEncoding = "UTF-8")

# 获取世界地图
world <- ne_countries(scale = "medium", returnclass = "sf")

cat("AAPC数据中的国家数量:", nrow(country_aapc), "\n")
cat("世界地图中的国家数量:", nrow(world), "\n")

# 检查字段名
cat("\n世界地图的字段名:\n")
print(names(world))

cat("\nAAPC数据的字段名:\n")
print(names(country_aapc))

# 显示前10个国家名称对比
cat("\n前10个AAPC数据中的国家名称:\n")
print(head(country_aapc$location_name, 10))

cat("\n前10个世界地图中的国家名称 (name_en):\n")
print(head(world$name_en, 10))

if("admin" %in% names(world)) {
  cat("\n前10个世界地图中的国家名称 (admin):\n")
  print(head(world$admin, 10))
}

if("name" %in% names(world)) {
  cat("\n前10个世界地图中的国家名称 (name):\n")
  print(head(world$name, 10))
}

# 尝试匹配
world_with_aapc <- world %>%
  left_join(country_aapc, by = c("name_en" = "location_name"))

cat("\n匹配结果:\n")
cat("成功匹配的国家数量:", sum(!is.na(world_with_aapc$aapc)), "\n")
cat("未匹配的国家数量:", sum(is.na(world_with_aapc$aapc)), "\n")

# 显示一些匹配和未匹配的例子
matched_countries <- world_with_aapc[!is.na(world_with_aapc$aapc), c("name_en", "aapc")]
unmatched_countries <- world_with_aapc[is.na(world_with_aapc$aapc), "name_en"]

cat("\n成功匹配的前5个国家:\n")
if(nrow(matched_countries) > 0) {
  print(head(matched_countries, 5))
} else {
  cat("没有成功匹配的国家！\n")
}

cat("\n未匹配的前10个国家:\n")
print(head(unmatched_countries, 10))

# 检查是否有相似的国家名称
cat("\n寻找可能的名称差异...\n")
aapc_names <- country_aapc$location_name
world_names <- world$name_en

# 查找包含"中国"的名称
china_aapc <- aapc_names[grepl("中国|China", aapc_names, ignore.case = TRUE)]
china_world <- world_names[grepl("中国|China", world_names, ignore.case = TRUE)]

cat("AAPC数据中包含'中国'的名称:", china_aapc, "\n")
cat("世界地图中包含'China'的名称:", china_world, "\n")

# 查找包含"美国"的名称
usa_aapc <- aapc_names[grepl("美国|United States|America", aapc_names, ignore.case = TRUE)]
usa_world <- world_names[grepl("United States|America", world_names, ignore.case = TRUE)]

cat("AAPC数据中包含'美国'的名称:", usa_aapc, "\n")
cat("世界地图中包含'United States'的名称:", usa_world, "\n")

cat("\n=== 诊断完成 ===\n")
