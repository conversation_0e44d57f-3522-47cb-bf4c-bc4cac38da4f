# 检查死亡损伤数据库中的全球数据可用性
# 确认是否有全球/世界这个分项，还是需要将各个地区的数据加和

library(dplyr)
library(readr)

# 检查GBD super region数据库
file_path <- "死亡损伤-数据库/GBD super region/IHME-GBD_2021_DATA-26cc51e1-1.csv"

cat("读取GBD super region数据库...\n")
data <- read_csv(file_path, show_col_types = FALSE)

# 获取所有独特的地区名称
unique_locations <- unique(data$location_name)
cat("发现", length(unique_locations), "个地区\n\n")

cat("=== 所有地区名称 ===\n")
for(i in 1:length(unique_locations)) {
  cat(i, ":", unique_locations[i], "\n")
}

# 查找可能的全球数据
cat("\n=== 可能的全球数据选项 ===\n")

global_candidates <- unique_locations[grepl("全球|Global|世界|World|银行|Bank|四大|Four", 
                                           unique_locations, ignore.case = TRUE)]

if(length(global_candidates) > 0) {
  cat("找到以下可能的全球数据选项:\n")
  for(i in 1:length(global_candidates)) {
    cat(i, ":", global_candidates[i], "\n")
  }
} else {
  cat("未找到明确的全球数据选项\n")
}

# 检查每个候选项的数据量
cat("\n=== 各候选项的脑卒中数据量 ===\n")

for(location in global_candidates) {
  stroke_count <- data %>%
    filter(location_name == location, cause_name == "脑卒中") %>%
    nrow()
  
  cat(location, ":", stroke_count, "条脑卒中记录\n")
  
  # 检查年份范围
  if(stroke_count > 0) {
    years <- data %>%
      filter(location_name == location, cause_name == "脑卒中") %>%
      pull(year) %>%
      range()
    cat("  年份范围:", years[1], "-", years[2], "\n")
    
    # 检查是否有死亡数据
    death_count <- data %>%
      filter(location_name == location, cause_name == "脑卒中", measure_name == "死亡") %>%
      nrow()
    cat("  死亡数据:", death_count, "条记录\n")
    
    # 检查年龄组
    age_groups <- data %>%
      filter(location_name == location, cause_name == "脑卒中") %>%
      pull(age_name) %>%
      unique()
    cat("  年龄组数量:", length(age_groups), "\n")
  }
  cat("\n")
}

# 检查是否需要将各个地区数据加和
cat("=== 地区分析 ===\n")

# 查找所有可能的地区级别数据
regional_locations <- unique_locations[!grepl("全球|Global|世界|World|银行|Bank|四大|Four", 
                                              unique_locations, ignore.case = TRUE)]

cat("非全球性地区数量:", length(regional_locations), "\n")

if(length(regional_locations) > 0) {
  cat("前10个地区示例:\n")
  for(i in 1:min(10, length(regional_locations))) {
    cat(i, ":", regional_locations[i], "\n")
  }
}

# 推荐最佳选择
cat("\n=== 推荐方案 ===\n")

if("世界银行分区" %in% global_candidates) {
  cat("✅ 推荐使用 '世界银行分区' 作为全球数据\n")
  cat("   理由: 世界银行分区通常包含全球所有国家和地区的汇总数据\n")
} else if("四大世界区域" %in% global_candidates) {
  cat("✅ 推荐使用 '四大世界区域' 作为全球数据\n")
  cat("   理由: 四大世界区域是全球地理区域的汇总\n")
} else {
  cat("❌ 未找到理想的全球数据\n")
  cat("   建议: 需要将各个地区的数据进行加和来计算全球数据\n")
}

cat("\n检查完成！\n")
