# 脑卒中死亡率AAPC完整布局 (1990-2021)
# 主地图 + 6个区域放大图的完整布局

library(readr)
library(dplyr)
library(ggplot2)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(RColorBrewer)
library(viridis)
library(scales)
library(gridExtra)
library(grid)

# 读取AAPC结果
aapc_data <- read_csv("stroke_mortality_aapc_results_1990_2021.csv", show_col_types = FALSE)

# 获取世界地图数据
world <- ne_countries(scale = "medium", returnclass = "sf")

# 扩展的国家名称映射
create_country_mapping <- function() {
  # 基础映射
  basic_mapping <- data.frame(
    chinese_name = c("美国", "英国", "大韩民国", "俄罗斯联邦", "伊朗伊斯兰共和国", 
                     "委内瑞拉玻利瓦尔共和国", "叙利亚阿拉伯共和国", "阿拉伯联合酋长国",
                     "沙特阿拉伯王国", "朝鲜民主主义人民共和国", "台湾", "中国"),
    english_name = c("United States of America", "United Kingdom", "South Korea", 
                     "Russia", "Iran", "Venezuela", "Syria", "United Arab Emirates",
                     "Saudi Arabia", "North Korea", "Taiwan", "China")
  )
  
  # 欧洲国家映射
  europe_mapping <- data.frame(
    chinese_name = c("德国", "法国", "意大利", "西班牙", "日本", "印度", "巴西", 
                     "澳大利亚", "加拿大", "墨西哥", "阿根廷", "南非", "埃及",
                     "土耳其", "波兰", "荷兰", "比利时", "瑞士", "奥地利",
                     "瑞典", "挪威", "丹麦", "芬兰", "希腊", "葡萄牙",
                     "捷克共和国", "匈牙利", "罗马尼亚", "保加利亚", "克罗地亚",
                     "塞尔维亚", "波斯尼亚和黑塞哥维那", "黑山共和国", "北马其顿",
                     "阿尔巴尼亚", "斯洛文尼亚", "斯洛伐克", "爱沙尼亚", "拉脱维亚",
                     "立陶宛", "白俄罗斯", "乌克兰", "摩尔多瓦共和国", "格鲁吉亚",
                     "亚美尼亚", "阿塞拜疆", "哈萨克斯坦", "乌兹别克斯坦", "土库曼斯坦",
                     "吉尔吉斯斯坦", "塔吉克斯坦", "阿富汗", "巴基斯坦", "孟加拉国",
                     "斯里兰卡", "尼泊尔", "不丹", "马尔代夫", "缅甸", "泰国",
                     "老挝人民民主共和国", "柬埔寨", "越南", "马来西亚", "新加坡",
                     "印度尼西亚", "菲律宾", "文莱达鲁萨兰国", "东帝汶", "蒙古",
                     "卢森堡公国", "马耳他", "塞浦路斯", "冰岛", "爱尔兰"),
    english_name = c("Germany", "France", "Italy", "Spain", "Japan", "India", "Brazil",
                     "Australia", "Canada", "Mexico", "Argentina", "South Africa", "Egypt",
                     "Turkey", "Poland", "Netherlands", "Belgium", "Switzerland", "Austria",
                     "Sweden", "Norway", "Denmark", "Finland", "Greece", "Portugal",
                     "Czech Republic", "Hungary", "Romania", "Bulgaria", "Croatia",
                     "Serbia", "Bosnia and Herzegovina", "Montenegro", "North Macedonia",
                     "Albania", "Slovenia", "Slovakia", "Estonia", "Latvia",
                     "Lithuania", "Belarus", "Ukraine", "Moldova", "Georgia",
                     "Armenia", "Azerbaijan", "Kazakhstan", "Uzbekistan", "Turkmenistan",
                     "Kyrgyzstan", "Tajikistan", "Afghanistan", "Pakistan", "Bangladesh",
                     "Sri Lanka", "Nepal", "Bhutan", "Maldives", "Myanmar", "Thailand",
                     "Laos", "Cambodia", "Vietnam", "Malaysia", "Singapore",
                     "Indonesia", "Philippines", "Brunei", "East Timor", "Mongolia",
                     "Luxembourg", "Malta", "Cyprus", "Iceland", "Ireland")
  )
  
  return(rbind(basic_mapping, europe_mapping))
}

# 应用国家名称映射
all_mapping <- create_country_mapping()
aapc_data_mapped <- aapc_data %>%
  left_join(all_mapping, by = c("location_name" = "chinese_name")) %>%
  mutate(country_name = ifelse(is.na(english_name), location_name, english_name))

# 定义9级色阶
breaks <- c(-6.01, -4.5, -3.5, -2.5, -1.5, -0.5, 0, 0.5, 1.0, 1.85)
labels <- c("-6.01 to <-4.5", "-4.5 to <-3.5", "-3.5 to <-2.5", "-2.5 to <-1.5", 
            "-1.5 to <-0.5", "-0.5 to <0", "0 to <0.5", "0.5 to <1.0", "1.0 to 1.85")

# 创建颜色分级
aapc_data_mapped$aapc_category <- cut(aapc_data_mapped$aapc, 
                                      breaks = breaks, 
                                      labels = labels, 
                                      include.lowest = TRUE)

# 定义颜色（深蓝到红色渐变）
colors <- c("#08306b", "#2171b5", "#4292c6", "#6baed6", "#9ecae1", 
            "#c6dbef", "#fee391", "#fec44f", "#d94801")

# 合并地图数据
world_data <- world %>%
  left_join(aapc_data_mapped, by = c("name" = "country_name"))

# 创建主地图（无图例）
main_map <- ggplot(world_data) +
  geom_sf(aes(fill = aapc_category), color = "white", size = 0.1) +
  scale_fill_manual(values = colors, 
                    name = "AAPC (%)",
                    na.value = "grey90",
                    drop = FALSE) +
  theme_void() +
  theme(
    legend.position = "none",
    plot.title = element_text(size = 14, face = "bold", hjust = 0.5),
    plot.margin = margin(5, 5, 5, 5)
  ) +
  labs(title = "Global Stroke Mortality AAPC in Population ≥65 Years (1990-2021)")

# 定义区域边界
regions <- list(
  "Caribbean and\nCentral America" = list(xlim = c(-95, -55), ylim = c(5, 30)),
  "Persian Gulf" = list(xlim = c(45, 65), ylim = c(20, 35)),
  "Balkan Peninsula" = list(xlim = c(15, 30), ylim = c(40, 50)),
  "South East Asia" = list(xlim = c(90, 140), ylim = c(-10, 25)),
  "West Africa &\nEastern Mediterranean" = list(xlim = c(-20, 45), ylim = c(10, 40)),
  "Northern Europe" = list(xlim = c(-10, 35), ylim = c(50, 70))
)

# 创建区域地图函数
create_regional_map <- function(region_name, xlim, ylim) {
  ggplot(world_data) +
    geom_sf(aes(fill = aapc_category), color = "white", size = 0.2) +
    scale_fill_manual(values = colors, 
                      name = "AAPC (%)",
                      na.value = "grey90",
                      drop = FALSE) +
    coord_sf(xlim = xlim, ylim = ylim, expand = FALSE) +
    theme_void() +
    theme(
      legend.position = "none",
      plot.title = element_text(size = 9, face = "bold", hjust = 0.5),
      plot.margin = margin(2, 2, 2, 2)
    ) +
    labs(title = region_name)
}

# 生成所有区域地图
regional_maps <- list()
for(i in seq_along(regions)) {
  region_name <- names(regions)[i]
  region_bounds <- regions[[i]]
  
  regional_maps[[i]] <- create_regional_map(
    region_name, 
    region_bounds$xlim, 
    region_bounds$ylim
  )
}

# 创建图例
legend_data <- data.frame(
  category = factor(labels, levels = labels),
  color = colors
)

legend_plot <- ggplot(legend_data, aes(x = 1, y = category, fill = category)) +
  geom_tile(color = "white", size = 0.5) +
  scale_fill_manual(values = colors, guide = "none") +
  theme_void() +
  theme(
    axis.text.y = element_text(size = 8, hjust = 0),
    plot.title = element_text(size = 10, face = "bold", hjust = 0.5),
    plot.margin = margin(5, 5, 5, 5)
  ) +
  labs(title = "AAPC (%)") +
  scale_x_continuous(expand = c(0, 0)) +
  scale_y_discrete(expand = c(0, 0))

cat("正在创建完整布局...\n")

# 创建完整布局
# 使用grid.arrange创建复杂布局
layout_matrix <- rbind(
  c(1, 1, 1, 1, 1, 1, 1, 1),  # 主地图占8列
  c(2, 2, 3, 3, 4, 4, 7, 7),  # 区域地图第一行 + 图例
  c(5, 5, 6, 6, 8, 8, 7, 7)   # 区域地图第二行 + 图例
)

complete_layout <- grid.arrange(
  main_map,                    # 1
  regional_maps[[1]],          # 2 - Caribbean
  regional_maps[[2]],          # 3 - Persian Gulf
  regional_maps[[3]],          # 4 - Balkan
  regional_maps[[4]],          # 5 - South East Asia
  regional_maps[[5]],          # 6 - West Africa
  regional_maps[[6]],          # 7 - Northern Europe
  legend_plot,                 # 8 - Legend
  layout_matrix = layout_matrix,
  heights = c(3, 1.2, 1.2),
  widths = c(1, 1, 1, 1, 1, 1, 1, 1)
)

# 保存完整布局
ggsave("stroke_mortality_complete_layout_1990_2021.png", complete_layout,
       width = 20, height = 14, dpi = 300, bg = "white")

cat("完整布局已保存: stroke_mortality_complete_layout_1990_2021.png\n")
cat("分析完成！\n")
