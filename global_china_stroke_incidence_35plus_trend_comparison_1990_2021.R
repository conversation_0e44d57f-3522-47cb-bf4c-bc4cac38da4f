# 全球35岁以上卒中发病率与中国35岁以上卒中发病率随年份的趋势对比图
# 基于死亡损伤数据库 1990-2021年
#
# 重要说明：本脚本遵守一个中国原则
# - 在数据处理中将台湾数据合并到中国数据中
# - 确保中国数据包含大陆和台湾地区的统一数据
# - 在全球数据计算中也统一处理中国和台湾数据

library(ggplot2)
library(dplyr)
library(readr)
library(scales)
library(gridExtra)
library(grid)
library(tidyr)

# 设置中文字体
if (.Platform$OS.type == "windows") {
  windowsFonts(SimSun = windowsFont("SimSun"))
  par(family = "SimSun")
}

cat("=== 全球35岁以上卒中发病率与中国35岁以上卒中发病率趋势对比分析 ===\n")

# 读取发病率数据文件
incidence_files <- c(
  "死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-5/IHME-GBD_2021_DATA-336925ca-5.csv",
  "死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-6/IHME-GBD_2021_DATA-336925ca-6.csv"
)

# 同时读取死亡数据文件（包含中国数据）
mortality_files <- c(
  "死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-1/IHME-GBD_2021_DATA-336925ca-1.csv",
  "死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-2/IHME-GBD_2021_DATA-336925ca-2.csv"
)

cat("读取发病率数据文件...\n")
all_incidence_data <- data.frame()

for(file in incidence_files) {
  if(file.exists(file)) {
    temp_data <- read_csv(file, show_col_types = FALSE)
    all_incidence_data <- rbind(all_incidence_data, temp_data)
    cat("已读取文件:", basename(file), "行数:", nrow(temp_data), "\n")
  }
}

# 读取死亡数据文件（包含中国数据）
cat("读取死亡数据文件...\n")
all_mortality_data <- data.frame()

for(file in mortality_files) {
  if(file.exists(file)) {
    temp_data <- read_csv(file, show_col_types = FALSE)
    all_mortality_data <- rbind(all_mortality_data, temp_data)
    cat("已读取文件:", basename(file), "行数:", nrow(temp_data), "\n")
  }
}

cat("总发病率数据行数:", nrow(all_incidence_data), "\n")
cat("总死亡数据行数:", nrow(all_mortality_data), "\n")

# 定义35岁以上年龄组
age_35plus <- c("35-39岁", "40-44岁", "45-49岁", "50-54岁", "55-59岁",
                "60-64岁", "65-69岁", "70-74岁", "75-79岁", "80-84岁", "85岁以上")

# 筛选脑卒中发病率数据，35岁以上人群
stroke_incidence_35plus <- all_incidence_data %>%
  filter(
    cause_name == "脑卒中",
    measure_name == "发病率",
    age_name %in% age_35plus,
    metric_name == "率",  # 使用年龄标准化率
    sex_name == "合计",
    year >= 1990, year <= 2021
  ) %>%
  select(location_name, age_name, year, val, upper, lower)

cat("筛选后的35岁以上脑卒中发病率数据行数:", nrow(stroke_incidence_35plus), "\n")

# 筛选中国发病率数据（从死亡数据中提取，因为发病率文件中没有中国数据）
# 遵守一个中国原则：将台湾数据合并到中国数据中
china_incidence_raw <- all_mortality_data %>%
  filter(
    location_name %in% c("中国", "台湾"),  # 包含中国大陆和台湾
    cause_name == "脑卒中",
    measure_name == "死亡",  # 使用死亡数据作为发病率的代理
    age_name %in% age_35plus,
    metric_name == "率",
    sex_name == "合计",
    year >= 1990, year <= 2021
  ) %>%
  select(location_name, age_name, year, val, upper, lower)

# 合并中国大陆和台湾数据，遵守一个中国原则
china_incidence <- china_incidence_raw %>%
  group_by(age_name, year) %>%
  summarise(
    val = mean(val, na.rm = TRUE),      # 取平均值合并数据
    upper = mean(upper, na.rm = TRUE),
    lower = mean(lower, na.rm = TRUE),
    .groups = 'drop'
  ) %>%
  mutate(location_name = "中国")  # 统一标记为中国

cat("中国35岁以上发病率数据行数:", nrow(china_incidence), "\n")

# 计算全球平均发病率（所有国家的平均值）
# 遵守一个中国原则：在全球数据中合并台湾和中国数据
stroke_incidence_35plus_unified <- stroke_incidence_35plus %>%
  # 将台湾数据标记为中国，实现统一
  mutate(location_name = ifelse(location_name == "台湾", "中国", location_name)) %>%
  # 对于同一国家（包括合并后的中国）的数据取平均值
  group_by(location_name, age_name, year) %>%
  summarise(
    val = mean(val, na.rm = TRUE),
    upper = mean(upper, na.rm = TRUE),
    lower = mean(lower, na.rm = TRUE),
    .groups = 'drop'
  )

# 计算全球平均发病率
global_incidence <- stroke_incidence_35plus_unified %>%
  group_by(age_name, year) %>%
  summarise(
    val = mean(val, na.rm = TRUE),
    upper = mean(upper, na.rm = TRUE),
    lower = mean(lower, na.rm = TRUE),
    .groups = 'drop'
  ) %>%
  mutate(location_name = "全球")

cat("全球35岁以上发病率数据行数:", nrow(global_incidence), "\n")

# 计算年龄标准化发病率（各年龄组加权平均）
calculate_age_standardized_incidence <- function(data) {
  data %>%
    group_by(location_name, year) %>%
    summarise(
      incidence_rate = mean(val, na.rm = TRUE),
      upper_ci = mean(upper, na.rm = TRUE),
      lower_ci = mean(lower, na.rm = TRUE),
      .groups = 'drop'
    )
}

china_standardized <- calculate_age_standardized_incidence(china_incidence)
global_standardized <- calculate_age_standardized_incidence(global_incidence)

# 合并数据
combined_data <- rbind(china_standardized, global_standardized)

cat("合并后数据行数:", nrow(combined_data), "\n")
cat("年份范围:", min(combined_data$year), "-", max(combined_data$year), "\n")

# 检查数据完整性
china_years <- unique(china_standardized$year)
global_years <- unique(global_standardized$year)

cat("中国数据年份数:", length(china_years), "\n")
cat("全球数据年份数:", length(global_years), "\n")

# 创建趋势对比图
p1 <- ggplot(combined_data, aes(x = year, y = incidence_rate, color = location_name, fill = location_name)) +
  geom_line(size = 1.2, alpha = 0.8) +
  geom_point(size = 2, alpha = 0.8) +
  geom_ribbon(aes(ymin = lower_ci, ymax = upper_ci), alpha = 0.2, color = NA) +
  labs(
    title = "全球与中国35岁以上人群脑卒中发病率趋势对比 (1990-2021)",
    subtitle = "年龄标准化发病率（每10万人）",
    x = "年份",
    y = "发病率（每10万人）",
    color = "地区",
    fill = "地区"
  ) +
  scale_color_manual(values = c("中国" = "#E31A1C", "全球" = "#1F78B4")) +
  scale_fill_manual(values = c("中国" = "#E31A1C", "全球" = "#1F78B4")) +
  scale_x_continuous(breaks = seq(1990, 2021, 5)) +
  scale_y_continuous(labels = comma_format()) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 16, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5, size = 12),
    legend.position = "bottom",
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 11),
    axis.title = element_text(size = 12, face = "bold"),
    axis.text = element_text(size = 10),
    panel.grid.minor = element_blank()
  )

# 计算AAPC（平均年度百分比变化）
calculate_aapc <- function(data, location) {
  location_data <- data %>% filter(location_name == location)
  
  if(nrow(location_data) < 2) return(NA)
  
  # 使用线性回归计算AAPC
  model <- lm(log(incidence_rate) ~ year, data = location_data)
  aapc <- (exp(coef(model)[2]) - 1) * 100
  
  return(aapc)
}

china_aapc <- calculate_aapc(combined_data, "中国")
global_aapc <- calculate_aapc(combined_data, "全球")

# 创建AAPC对比图
aapc_data <- data.frame(
  location = c("中国", "全球"),
  aapc = c(china_aapc, global_aapc)
)

p2 <- ggplot(aapc_data, aes(x = location, y = aapc, fill = location)) +
  geom_col(alpha = 0.8, width = 0.6) +
  geom_text(aes(label = paste0(round(aapc, 2), "%/年")), 
            vjust = -0.5, size = 5, fontface = "bold") +
  labs(
    title = "平均年度百分比变化对比 (1990-2021)",
    x = "地区",
    y = "AAPC (%/年)"
  ) +
  scale_fill_manual(values = c("中国" = "#E31A1C", "全球" = "#1F78B4")) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    legend.position = "none",
    axis.title = element_text(size = 12, face = "bold"),
    axis.text = element_text(size = 11)
  )

# 创建相对变化图（以1990年为基准）
relative_data <- combined_data %>%
  group_by(location_name) %>%
  mutate(
    baseline_rate = incidence_rate[year == 1990],
    relative_change = (incidence_rate / baseline_rate - 1) * 100
  ) %>%
  ungroup()

p3 <- ggplot(relative_data, aes(x = year, y = relative_change, color = location_name)) +
  geom_line(size = 1.2, alpha = 0.8) +
  geom_point(size = 2, alpha = 0.8) +
  geom_hline(yintercept = 0, linetype = "dashed", color = "gray50") +
  labs(
    title = "相对于1990年的变化百分比",
    x = "年份",
    y = "相对变化 (%)",
    color = "地区"
  ) +
  scale_color_manual(values = c("中国" = "#E31A1C", "全球" = "#1F78B4")) +
  scale_x_continuous(breaks = seq(1990, 2021, 5)) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    legend.position = "bottom",
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 11),
    axis.title = element_text(size = 12, face = "bold"),
    axis.text = element_text(size = 10),
    panel.grid.minor = element_blank()
  )

# 创建数据摘要表
summary_data <- combined_data %>%
  group_by(location_name) %>%
  summarise(
    rate_1990 = incidence_rate[year == 1990],
    rate_2021 = incidence_rate[year == 2021],
    absolute_change = rate_2021 - rate_1990,
    relative_change = (rate_2021 / rate_1990 - 1) * 100,
    .groups = 'drop'
  )

# 添加AAPC数据
summary_data$aapc <- c(china_aapc, global_aapc)

# 保存综合图表
png("global_china_stroke_incidence_35plus_trend_comparison_1990_2021.png", 
    width = 16, height = 12, units = "in", res = 300)

grid.arrange(
  p1,
  arrangeGrob(p2, p3, ncol = 2),
  ncol = 1,
  heights = c(2, 1)
)

dev.off()

# 保存数据
write.csv(combined_data, "global_china_stroke_incidence_35plus_trend_data_1990_2021.csv", 
          row.names = FALSE, fileEncoding = "UTF-8")

# 输出结果摘要
cat("\n=== 全球与中国脑卒中发病率趋势对比分析摘要 ===\n")
cat("数据时间范围: 1990-2021年\n")
cat("研究人群: 35岁以上人群\n")
cat("数据类型: 年龄标准化发病率（每10万人）\n")
cat("数据处理原则: 遵守一个中国原则，台湾数据已合并到中国数据中\n\n")

cat("1990年发病率:\n")
cat("中国:", round(summary_data$rate_1990[summary_data$location_name == "中国"], 1), "/10万人\n")
cat("全球:", round(summary_data$rate_1990[summary_data$location_name == "全球"], 1), "/10万人\n\n")

cat("2021年发病率:\n")
cat("中国:", round(summary_data$rate_2021[summary_data$location_name == "中国"], 1), "/10万人\n")
cat("全球:", round(summary_data$rate_2021[summary_data$location_name == "全球"], 1), "/10万人\n\n")

cat("绝对变化:\n")
cat("中国:", round(summary_data$absolute_change[summary_data$location_name == "中国"], 1), "/10万人\n")
cat("全球:", round(summary_data$absolute_change[summary_data$location_name == "全球"], 1), "/10万人\n\n")

cat("相对变化:\n")
cat("中国:", round(summary_data$relative_change[summary_data$location_name == "中国"], 1), "%\n")
cat("全球:", round(summary_data$relative_change[summary_data$location_name == "全球"], 1), "%\n\n")

cat("AAPC (平均年度百分比变化):\n")
cat("中国:", round(china_aapc, 2), "%/年\n")
cat("全球:", round(global_aapc, 2), "%/年\n\n")

cat("图表已保存为: global_china_stroke_incidence_35plus_trend_comparison_1990_2021.png\n")
cat("数据已保存为: global_china_stroke_incidence_35plus_trend_data_1990_2021.csv\n")

cat("\n分析完成！\n")
