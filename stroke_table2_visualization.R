# 脑卒中DALYs分析可视化和报告生成
# 配合 stroke_table2_reproduction.R 使用

library(ggplot2)
library(dplyr)
library(tidyr)
library(kableExtra)
library(RColorBrewer)
library(gridExtra)

# 1. 创建热力图显示AAPC变化
create_aapc_heatmap <- function(results_data) {
  # 准备热力图数据
  heatmap_data <- results_data %>%
    select(risk_factor, sdi_level, aapc) %>%
    mutate(
      risk_factor_short = case_when(
        grepl("高血压", risk_factor) ~ "高血压",
        grepl("高胆固醇", risk_factor) ~ "高胆固醇",
        grepl("高血糖", risk_factor) ~ "高血糖",
        grepl("吸烟", risk_factor) ~ "吸烟",
        grepl("饮酒", risk_factor) ~ "饮酒",
        grepl("饮食", risk_factor) ~ "不健康饮食",
        grepl("空气污染", risk_factor) ~ "空气污染",
        grepl("低温", risk_factor) ~ "低温",
        grepl("高温", risk_factor) ~ "高温",
        TRUE ~ risk_factor
      )
    )
  
  # 创建热力图
  p <- ggplot(heatmap_data, aes(x = sdi_level, y = risk_factor_short, fill = aapc)) +
    geom_tile(color = "white", size = 0.5) +
    scale_fill_gradient2(
      low = "blue", mid = "white", high = "red",
      midpoint = 0,
      name = "AAPC (%)",
      breaks = seq(-6, 6, 2),
      labels = seq(-6, 6, 2)
    ) +
    geom_text(aes(label = round(aapc, 1)), size = 3, color = "black") +
    labs(
      title = "脑卒中相关DALYs年均变化率 (AAPC) - 按危险因素和SDI分层",
      subtitle = "1990-2019年，≥65岁人群",
      x = "社会人口发展指数 (SDI) 分层",
      y = "危险因素"
    ) +
    theme_minimal() +
    theme(
      axis.text.x = element_text(angle = 45, hjust = 1),
      axis.text.y = element_text(size = 10),
      plot.title = element_text(size = 14, face = "bold"),
      plot.subtitle = element_text(size = 12),
      legend.position = "right"
    ) +
    scale_x_discrete(limits = c("Global", "High SDI", "High-middle SDI", "Middle SDI", "Low-middle SDI", "Low SDI"))
  
  return(p)
}

# 2. 创建条形图显示各危险因素的全球AAPC
create_global_aapc_barplot <- function(results_data) {
  global_data <- results_data %>%
    filter(sdi_level == "Global") %>%
    mutate(
      risk_factor_short = case_when(
        grepl("高血压", risk_factor) ~ "高血压",
        grepl("高胆固醇", risk_factor) ~ "高胆固醇", 
        grepl("高血糖", risk_factor) ~ "高血糖",
        grepl("吸烟", risk_factor) ~ "吸烟",
        grepl("饮酒", risk_factor) ~ "饮酒",
        grepl("饮食", risk_factor) ~ "不健康饮食",
        grepl("空气污染", risk_factor) ~ "空气污染",
        grepl("低温", risk_factor) ~ "低温",
        grepl("高温", risk_factor) ~ "高温",
        TRUE ~ risk_factor
      ),
      color_group = ifelse(aapc >= 0, "增加", "减少")
    ) %>%
    arrange(aapc)
  
  p <- ggplot(global_data, aes(x = reorder(risk_factor_short, aapc), y = aapc, fill = color_group)) +
    geom_col(width = 0.7) +
    geom_errorbar(aes(ymin = aapc_lower, ymax = aapc_upper), width = 0.2, alpha = 0.7) +
    scale_fill_manual(values = c("增加" = "#d73027", "减少" = "#4575b4")) +
    geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.5) +
    labs(
      title = "全球脑卒中相关DALYs年均变化率 (AAPC)",
      subtitle = "1990-2019年，≥65岁人群，按危险因素分类",
      x = "危险因素",
      y = "AAPC (%)",
      fill = "变化趋势"
    ) +
    theme_minimal() +
    theme(
      axis.text.x = element_text(angle = 45, hjust = 1),
      plot.title = element_text(size = 14, face = "bold"),
      plot.subtitle = element_text(size = 12),
      legend.position = "top"
    ) +
    coord_flip()
  
  return(p)
}

# 3. 创建SDI分层比较图
create_sdi_comparison_plot <- function(results_data) {
  # 选择几个主要危险因素进行比较
  major_risks <- c("高血压 (High blood pressure)", "高血糖 (High fasting plasma glucose)", 
                   "吸烟 (Smoking)", "空气污染 (Air pollution)")
  
  comparison_data <- results_data %>%
    filter(risk_factor %in% major_risks) %>%
    mutate(
      risk_factor_short = case_when(
        grepl("高血压", risk_factor) ~ "高血压",
        grepl("高血糖", risk_factor) ~ "高血糖",
        grepl("吸烟", risk_factor) ~ "吸烟",
        grepl("空气污染", risk_factor) ~ "空气污染"
      )
    )
  
  p <- ggplot(comparison_data, aes(x = sdi_level, y = aapc, color = risk_factor_short, group = risk_factor_short)) +
    geom_line(size = 1.2, alpha = 0.8) +
    geom_point(size = 3) +
    geom_ribbon(aes(ymin = aapc_lower, ymax = aapc_upper, fill = risk_factor_short), 
                alpha = 0.2, color = NA) +
    scale_color_brewer(type = "qual", palette = "Set1") +
    scale_fill_brewer(type = "qual", palette = "Set1") +
    geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.5) +
    labs(
      title = "主要危险因素AAPC在不同SDI分层的变化",
      subtitle = "脑卒中相关DALYs，1990-2019年，≥65岁人群",
      x = "社会人口发展指数 (SDI) 分层",
      y = "AAPC (%)",
      color = "危险因素",
      fill = "危险因素"
    ) +
    theme_minimal() +
    theme(
      axis.text.x = element_text(angle = 45, hjust = 1),
      plot.title = element_text(size = 14, face = "bold"),
      plot.subtitle = element_text(size = 12),
      legend.position = "bottom"
    ) +
    guides(color = guide_legend(title = "危险因素"), fill = guide_legend(title = "危险因素"))
  
  return(p)
}

# 4. 创建专业的Table 2格式
create_professional_table2 <- function(results_data) {
  # 准备表格数据
  table_data <- results_data %>%
    mutate(
      risk_factor_clean = case_when(
        grepl("高血压", risk_factor) ~ "高血压",
        grepl("高胆固醇", risk_factor) ~ "高胆固醇",
        grepl("高血糖", risk_factor) ~ "高血糖", 
        grepl("吸烟", risk_factor) ~ "吸烟",
        grepl("饮酒", risk_factor) ~ "饮酒",
        grepl("饮食", risk_factor) ~ "不健康饮食",
        grepl("空气污染", risk_factor) ~ "空气污染",
        grepl("低温", risk_factor) ~ "低温",
        grepl("高温", risk_factor) ~ "高温"
      ),
      dalys_1990_display = paste0(round(dalys_1990, 1), " (", round(dalys_1990_lower, 1), "-", round(dalys_1990_upper, 1), ")"),
      dalys_2019_display = paste0(round(dalys_2019, 1), " (", round(dalys_2019_lower, 1), "-", round(dalys_2019_upper, 1), ")"),
      aapc_display = paste0(round(aapc, 2), " (", round(aapc_lower, 2), " to ", round(aapc_upper, 2), ")")
    )
  
  # 创建分组表格
  table_sections <- list()
  
  # 按危险因素分组
  for (risk in unique(table_data$risk_factor_clean)) {
    risk_data <- table_data %>%
      filter(risk_factor_clean == risk) %>%
      select(sdi_level, dalys_1990_display, dalys_2019_display, aapc_display) %>%
      pivot_longer(cols = -sdi_level, names_to = "metric", values_to = "value") %>%
      pivot_wider(names_from = sdi_level, values_from = value)
    
    table_sections[[risk]] <- risk_data
  }
  
  return(table_sections)
}

# 5. 生成综合报告
generate_comprehensive_report <- function(results_data) {
  # 创建所有图表
  heatmap <- create_aapc_heatmap(results_data)
  barplot <- create_global_aapc_barplot(results_data)
  comparison <- create_sdi_comparison_plot(results_data)
  
  # 保存图表
  ggsave("stroke_dalys_aapc_heatmap.png", heatmap, width = 12, height = 8, dpi = 300)
  ggsave("stroke_dalys_global_aapc_barplot.png", barplot, width = 10, height = 8, dpi = 300)
  ggsave("stroke_dalys_sdi_comparison.png", comparison, width = 12, height = 8, dpi = 300)
  
  # 创建组合图
  combined_plot <- grid.arrange(heatmap, barplot, ncol = 1, heights = c(2, 1))
  ggsave("stroke_dalys_combined_analysis.png", combined_plot, width = 14, height = 12, dpi = 300)
  
  # 生成汇总统计
  summary_stats <- results_data %>%
    group_by(sdi_level) %>%
    summarise(
      avg_aapc = mean(aapc, na.rm = TRUE),
      median_aapc = median(aapc, na.rm = TRUE),
      min_aapc = min(aapc, na.rm = TRUE),
      max_aapc = max(aapc, na.rm = TRUE),
      .groups = 'drop'
    )
  
  # 识别关键发现
  key_findings <- list(
    highest_increase = results_data %>% slice_max(aapc, n = 3),
    highest_decrease = results_data %>% slice_min(aapc, n = 3),
    global_trends = results_data %>% filter(sdi_level == "Global") %>% arrange(desc(abs(aapc)))
  )
  
  return(list(
    summary_stats = summary_stats,
    key_findings = key_findings,
    plots_created = c("heatmap", "barplot", "comparison", "combined")
  ))
}

# 6. 主可视化函数
main_visualization <- function(results_file = "stroke_dalys_table2_detailed_results.csv") {
  cat("开始创建脑卒中DALYs分析可视化...\n")
  
  # 读取结果数据
  if (file.exists(results_file)) {
    results_data <- read.csv(results_file, stringsAsFactors = FALSE)
  } else {
    stop("未找到结果文件，请先运行 stroke_table2_reproduction.R")
  }
  
  # 生成综合报告
  report <- generate_comprehensive_report(results_data)
  cat("✓ 可视化图表创建完成\n")
  
  # 创建专业表格
  table_sections <- create_professional_table2(results_data)
  cat("✓ 专业表格创建完成\n")
  
  # 保存汇总统计
  write.csv(report$summary_stats, "stroke_dalys_summary_statistics.csv", row.names = FALSE)
  
  cat("可视化分析完成！\n")
  cat("生成的文件：\n")
  cat("- stroke_dalys_aapc_heatmap.png\n")
  cat("- stroke_dalys_global_aapc_barplot.png\n") 
  cat("- stroke_dalys_sdi_comparison.png\n")
  cat("- stroke_dalys_combined_analysis.png\n")
  cat("- stroke_dalys_summary_statistics.csv\n")
  
  return(report)
}

# 执行可视化（如果直接运行此脚本）
if (!interactive()) {
  report <- main_visualization()
}
