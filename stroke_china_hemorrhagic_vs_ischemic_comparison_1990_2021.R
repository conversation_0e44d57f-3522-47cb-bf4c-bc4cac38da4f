# 中国1990-2021年脑卒中出血与缺血关系比较图
# 基于现有数据分析中国35岁以上人群脑卒中出血与缺血的关系比较

# 加载必要的包
library(ggplot2)
library(dplyr)
library(tidyr)
library(gridExtra)
library(scales)
library(RColorBrewer)
library(grid)

# 设置中文字体
if (.Platform$OS.type == "windows") {
  windowsFonts(SimSun = windowsFont("SimSun"))
  par(family = "SimSun")
}

# 读取缺血性脑卒中数据
ischemic_data <- read.csv("stroke_china_ischemic_stroke_prevalence_35plus_1990_2021.csv", 
                         stringsAsFactors = FALSE, fileEncoding = "UTF-8")

# 读取出血性脑卒中数据
hemorrhagic_data <- read.csv("stroke_china_hemorrhagic_stroke_prevalence_35plus_1990_2021.csv", 
                            stringsAsFactors = FALSE, fileEncoding = "UTF-8")

# 数据清理和处理函数
clean_rate_data <- function(rate_str) {
  # 提取数值部分（去掉置信区间）
  rate_num <- as.numeric(gsub("\\s*\\(.*\\)", "", rate_str))
  return(rate_num)
}

clean_aapc_data <- function(aapc_str) {
  # 提取AAPC数值部分
  aapc_num <- as.numeric(gsub("\\s*\\(.*\\)", "", aapc_str))
  return(aapc_num)
}

# 处理缺血性脑卒中数据
ischemic_clean <- ischemic_data %>%
  filter(!is.na(Category) & Category != "" & 
         !grepl("Table|1990年|2021年", Category)) %>%
  mutate(
    Rate_1990 = clean_rate_data(Prevalence_1990_rate),
    Rate_2021 = clean_rate_data(Prevalence_2021_rate),
    AAPC = clean_aapc_data(AAPC),
    Type = "缺血性脑卒中"
  ) %>%
  select(Category, Rate_1990, Rate_2021, AAPC, Type)

# 处理出血性脑卒中数据
hemorrhagic_clean <- hemorrhagic_data %>%
  filter(!is.na(Category) & Category != "" & 
         !grepl("Table|1990年|2021年", Category)) %>%
  mutate(
    Rate_1990 = clean_rate_data(Prevalence_1990_rate),
    Rate_2021 = clean_rate_data(Prevalence_2021_rate),
    AAPC = clean_aapc_data(AAPC),
    Type = "出血性脑卒中"
  ) %>%
  select(Category, Rate_1990, Rate_2021, AAPC, Type)

# 合并数据
combined_data <- rbind(ischemic_clean, hemorrhagic_clean)

# 创建年龄组分类
age_groups <- c("35-39", "40-44", "45-49", "50-54", "55-59", 
                "60-64", "65-69", "70-74", "75-79", "80-84")

# 筛选不同类别的数据
overall_data <- combined_data %>% filter(Category == "中国总体")
sex_data <- combined_data %>% filter(Category %in% c("男", "女"))
age_data <- combined_data %>% filter(Category %in% age_groups) %>%
  mutate(Category = factor(Category, levels = age_groups))

# 1. 总体比较图
p1 <- ggplot(overall_data, aes(x = Type, y = Rate_1990, fill = Type)) +
  geom_col(position = "dodge", alpha = 0.8) +
  geom_text(aes(label = paste0(round(Rate_1990, 1), "/10万")), 
            vjust = -0.5, size = 3) +
  labs(title = "1990年中国35岁以上人群脑卒中患病率对比",
       x = "脑卒中类型", y = "年龄标准化患病率（每10万人）") +
  scale_fill_manual(values = c("缺血性脑卒中" = "#2E86AB", "出血性脑卒中" = "#A23B72")) +
  theme_minimal() +
  theme(legend.position = "none",
        plot.title = element_text(hjust = 0.5, size = 14, face = "bold"))

p2 <- ggplot(overall_data, aes(x = Type, y = Rate_2021, fill = Type)) +
  geom_col(position = "dodge", alpha = 0.8) +
  geom_text(aes(label = paste0(round(Rate_2021, 1), "/10万")), 
            vjust = -0.5, size = 3) +
  labs(title = "2021年中国35岁以上人群脑卒中患病率对比",
       x = "脑卒中类型", y = "年龄标准化患病率（每10万人）") +
  scale_fill_manual(values = c("缺血性脑卒中" = "#2E86AB", "出血性脑卒中" = "#A23B72")) +
  theme_minimal() +
  theme(legend.position = "none",
        plot.title = element_text(hjust = 0.5, size = 14, face = "bold"))

# 2. 性别比较图
p3 <- ggplot(sex_data, aes(x = Category, y = Rate_1990, fill = Type)) +
  geom_col(position = "dodge", alpha = 0.8) +
  geom_text(aes(label = paste0(round(Rate_1990, 1))), 
            position = position_dodge(width = 0.9), vjust = -0.5, size = 3) +
  labs(title = "1990年按性别分层的脑卒中患病率对比",
       x = "性别", y = "年龄标准化患病率（每10万人）") +
  scale_fill_manual(values = c("缺血性脑卒中" = "#2E86AB", "出血性脑卒中" = "#A23B72")) +
  theme_minimal() +
  theme(plot.title = element_text(hjust = 0.5, size = 12, face = "bold"),
        legend.position = "bottom")

p4 <- ggplot(sex_data, aes(x = Category, y = Rate_2021, fill = Type)) +
  geom_col(position = "dodge", alpha = 0.8) +
  geom_text(aes(label = paste0(round(Rate_2021, 1))), 
            position = position_dodge(width = 0.9), vjust = -0.5, size = 3) +
  labs(title = "2021年按性别分层的脑卒中患病率对比",
       x = "性别", y = "年龄标准化患病率（每10万人）") +
  scale_fill_manual(values = c("缺血性脑卒中" = "#2E86AB", "出血性脑卒中" = "#A23B72")) +
  theme_minimal() +
  theme(plot.title = element_text(hjust = 0.5, size = 12, face = "bold"),
        legend.position = "bottom")

# 3. 年龄组比较图
p5 <- ggplot(age_data, aes(x = Category, y = Rate_1990, fill = Type)) +
  geom_col(position = "dodge", alpha = 0.8) +
  labs(title = "1990年按年龄组分层的脑卒中患病率对比",
       x = "年龄组（岁）", y = "年龄标准化患病率（每10万人）") +
  scale_fill_manual(values = c("缺血性脑卒中" = "#2E86AB", "出血性脑卒中" = "#A23B72")) +
  theme_minimal() +
  theme(plot.title = element_text(hjust = 0.5, size = 12, face = "bold"),
        axis.text.x = element_text(angle = 45, hjust = 1),
        legend.position = "bottom")

p6 <- ggplot(age_data, aes(x = Category, y = Rate_2021, fill = Type)) +
  geom_col(position = "dodge", alpha = 0.8) +
  labs(title = "2021年按年龄组分层的脑卒中患病率对比",
       x = "年龄组（岁）", y = "年龄标准化患病率（每10万人）") +
  scale_fill_manual(values = c("缺血性脑卒中" = "#2E86AB", "出血性脑卒中" = "#A23B72")) +
  theme_minimal() +
  theme(plot.title = element_text(hjust = 0.5, size = 12, face = "bold"),
        axis.text.x = element_text(angle = 45, hjust = 1),
        legend.position = "bottom")

# 4. AAPC比较图
p7 <- ggplot(overall_data, aes(x = Type, y = AAPC, fill = Type)) +
  geom_col(alpha = 0.8) +
  geom_text(aes(label = paste0(round(AAPC, 2), "%")), 
            vjust = -0.5, size = 4) +
  labs(title = "1990-2021年脑卒中AAPC对比",
       x = "脑卒中类型", y = "平均年度百分比变化（%）") +
  scale_fill_manual(values = c("缺血性脑卒中" = "#2E86AB", "出血性脑卒中" = "#A23B72")) +
  theme_minimal() +
  theme(legend.position = "none",
        plot.title = element_text(hjust = 0.5, size = 14, face = "bold"))

# 5. 比值比较图（缺血性/出血性）
ratio_data <- data.frame(
  Year = c("1990", "2021"),
  Ratio = c(
    overall_data$Rate_1990[overall_data$Type == "缺血性脑卒中"] / 
    overall_data$Rate_1990[overall_data$Type == "出血性脑卒中"],
    overall_data$Rate_2021[overall_data$Type == "缺血性脑卒中"] / 
    overall_data$Rate_2021[overall_data$Type == "出血性脑卒中"]
  )
)

p8 <- ggplot(ratio_data, aes(x = Year, y = Ratio, group = 1)) +
  geom_line(color = "#F18F01", linewidth = 2) +
  geom_point(color = "#F18F01", size = 4) +
  geom_text(aes(label = paste0("比值: ", round(Ratio, 2))), 
            vjust = -1, size = 4) +
  labs(title = "缺血性与出血性脑卒中患病率比值变化",
       x = "年份", y = "缺血性/出血性患病率比值") +
  theme_minimal() +
  theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"))

# 创建综合布局
png("stroke_china_hemorrhagic_vs_ischemic_comprehensive_comparison_1990_2021.png", 
    width = 16, height = 20, units = "in", res = 300)

grid.arrange(
  arrangeGrob(p1, p2, ncol = 2),
  arrangeGrob(p3, p4, ncol = 2),
  arrangeGrob(p5, p6, ncol = 1),
  arrangeGrob(p7, p8, ncol = 2),
  ncol = 1,
  heights = c(1, 1, 1.5, 1),
  top = textGrob("中国1990-2021年脑卒中出血与缺血关系综合比较分析", 
                 gp = gpar(fontsize = 18, fontface = "bold"))
)

dev.off()

# 输出数据摘要
cat("=== 中国脑卒中出血与缺血关系比较分析摘要 ===\n")
cat("数据时间范围: 1990-2021年\n")
cat("研究人群: 中国35岁以上人群\n\n")

cat("1. 总体患病率对比:\n")
cat("1990年 - 缺血性:", round(overall_data$Rate_1990[overall_data$Type == "缺血性脑卒中"], 1), "/10万人\n")
cat("1990年 - 出血性:", round(overall_data$Rate_1990[overall_data$Type == "出血性脑卒中"], 1), "/10万人\n")
cat("2021年 - 缺血性:", round(overall_data$Rate_2021[overall_data$Type == "缺血性脑卒中"], 1), "/10万人\n")
cat("2021年 - 出血性:", round(overall_data$Rate_2021[overall_data$Type == "出血性脑卒中"], 1), "/10万人\n\n")

cat("2. 比值变化:\n")
cat("1990年缺血性/出血性比值:", round(ratio_data$Ratio[1], 2), "\n")
cat("2021年缺血性/出血性比值:", round(ratio_data$Ratio[2], 2), "\n\n")

cat("3. AAPC对比:\n")
cat("缺血性脑卒中AAPC:", round(overall_data$AAPC[overall_data$Type == "缺血性脑卒中"], 2), "%\n")
cat("出血性脑卒中AAPC:", round(overall_data$AAPC[overall_data$Type == "出血性脑卒中"], 2), "%\n\n")

cat("图表已保存为: stroke_china_hemorrhagic_vs_ischemic_comprehensive_comparison_1990_2021.png\n")
