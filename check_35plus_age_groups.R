# 检查35岁以上年龄组数据
library(dplyr)

cat("=== 检查35岁以上年龄组数据 ===\n")

# 加载DALYs数据文件
dalys_files <- c(
  '死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-3/IHME-GBD_2021_DATA-336925ca-3.csv',
  '死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-4/IHME-GBD_2021_DATA-336925ca-4.csv'
)

all_dalys_data <- data.frame()
for(file in dalys_files) {
  data <- read.csv(file)
  all_dalys_data <- rbind(all_dalys_data, data)
}

cat("总DALYs记录数:", nrow(all_dalys_data), "\n")

# 检查所有年龄组
cat("\n所有可用年龄组:\n")
unique_ages <- unique(all_dalys_data$age_name)
print(sort(unique_ages))

# 筛选35岁以上相关年龄组
age_35plus <- all_dalys_data %>%
  filter(grepl("35|40|45|50|55|60|65|70|75|80|85|全部|年龄标准化", age_name)) %>%
  select(age_name) %>%
  distinct() %>%
  arrange(age_name)

cat("\n35岁以上相关年龄组:\n")
print(age_35plus$age_name)

# 检查具体的35岁以上年龄组数据
stroke_35plus_check <- all_dalys_data %>%
  filter(
    cause_name == "脑卒中",
    measure_name == "伤残调整生命年",
    age_name %in% c("35-39岁", "40-44岁", "45-49岁", "50-54岁", "55-59岁", 
                   "60-64岁", "65-69岁", "70-74岁", "75-79岁", "80-84岁", "85岁以上"),
    sex_name == "合计",
    metric_name == "率"
  )

cat("\n35岁以上脑卒中DALYs数据检查:\n")
cat("符合条件的记录数:", nrow(stroke_35plus_check), "\n")
cat("国家数量:", length(unique(stroke_35plus_check$location_name)), "\n")
cat("年份范围:", min(stroke_35plus_check$year), "-", max(stroke_35plus_check$year), "\n")

# 按年龄组统计
age_summary <- stroke_35plus_check %>%
  group_by(age_name) %>%
  summarise(
    records = n(),
    countries = length(unique(location_name)),
    years = length(unique(year)),
    .groups = 'drop'
  ) %>%
  arrange(age_name)

cat("\n按年龄组统计:\n")
print(age_summary)

# 检查数据完整性
completeness_check <- stroke_35plus_check %>%
  group_by(location_name, year) %>%
  summarise(
    age_groups_count = n_distinct(age_name),
    total_dalys = sum(val, na.rm = TRUE),
    .groups = 'drop'
  ) %>%
  filter(age_groups_count >= 10)  # 至少有10个年龄组的数据

cat("\n数据完整性检查:\n")
cat("有完整35岁以上年龄组数据的国家-年份组合:", nrow(completeness_check), "\n")
cat("涉及国家数:", length(unique(completeness_check$location_name)), "\n")

# 显示前10个国家的示例
cat("\n前10个国家的35岁以上DALYs数据示例:\n")
sample_countries <- unique(stroke_35plus_check$location_name)[1:10]
sample_data <- stroke_35plus_check %>%
  filter(location_name %in% sample_countries, year %in% c(1990, 2000, 2010, 2021)) %>%
  select(location_name, year, age_name, val) %>%
  arrange(location_name, year, age_name)

print(head(sample_data, 20))

cat("\n=== 35岁以上数据检查完成 ===\n")
cat("可以进行35岁以上人群的AAPC分析\n")
