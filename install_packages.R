# 安装脑卒中分析所需的R包

# 设置CRAN镜像
options(repos = c(CRAN = "https://cran.rstudio.com/"))

# 需要安装的包列表
required_packages <- c(
  "dplyr",
  "readr", 
  "tidyr",
  "ggplot2",
  "knitr",
  "kableExtra",
  "RColorBrewer",
  "gridExtra",
  "purrr",
  "stringr"
)

# 检查并安装缺失的包
install_if_missing <- function(packages) {
  for (pkg in packages) {
    if (!require(pkg, character.only = TRUE, quietly = TRUE)) {
      cat("正在安装包:", pkg, "\n")
      install.packages(pkg, dependencies = TRUE)
      
      # 验证安装
      if (require(pkg, character.only = TRUE, quietly = TRUE)) {
        cat("✓ 成功安装:", pkg, "\n")
      } else {
        cat("✗ 安装失败:", pkg, "\n")
      }
    } else {
      cat("✓ 包已存在:", pkg, "\n")
    }
  }
}

# 执行安装
cat("开始检查和安装R包...\n")
install_if_missing(required_packages)
cat("包安装检查完成！\n")
