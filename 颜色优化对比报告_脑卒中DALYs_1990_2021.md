# 脑卒中DALYs地图颜色优化对比报告 (1990-2021)

## 优化目标
解决原始地图中颜色集中的问题，实现更均匀的颜色分布，让每个颜色级别包含相近数量的国家，提高地图的视觉区分度。

## 优化方案对比

### 1. 原始方案 (平衡颜色版本)
**分级方式**: 基于数值范围的固定区间
**分级点**: -6.5, -3.5, -2.5, -2.0, -1.5, -1.0, -0.5, 0, 0.5, 2.0
**问题**: 颜色分布不均匀

#### 原始方案国家分布:
```
< -3.5:        18 个国家 (8.8%)
-3.5 to -2.5:  41 个国家 (20.1%)  ← 过度集中
-2.5 to -2.0:  14 个国家 (6.9%)
-2.0 to -1.5:  23 个国家 (11.3%)
-1.5 to -1.0:  25 个国家 (12.3%)
-1.0 to -0.5:  37 个国家 (18.1%)  ← 过度集中
-0.5 to 0:     26 个国家 (12.7%)
0 to 0.5:      12 个国家 (5.9%)
> 0.5:          8 个国家 (3.9%)
```

**问题分析**:
- 某些颜色级别包含过多国家（如-3.5 to -2.5有41个国家）
- 某些颜色级别包含过少国家（如> 0.5只有8个国家）
- 颜色在地图上显得集中，缺乏视觉层次

### 2. 优化方案 (十分位数精细分级)
**分级方式**: 基于数据分布的十分位数
**分级点**: 使用quantile函数计算的十分位数
**优势**: 每个颜色级别包含相等数量的国家

#### 优化方案国家分布:
```
十分位数 1:  21 个国家 (10.3%)  ← 均匀分布
十分位数 2:  20 个国家 (9.8%)   ← 均匀分布
十分位数 3:  20 个国家 (9.8%)   ← 均匀分布
十分位数 4:  21 个国家 (10.3%)  ← 均匀分布
十分位数 5:  20 个国家 (9.8%)   ← 均匀分布
十分位数 6:  20 个国家 (9.8%)   ← 均匀分布
十分位数 7:  21 个国家 (10.3%)  ← 均匀分布
十分位数 8:  20 个国家 (9.8%)   ← 均匀分布
十分位数 9:  20 个国家 (9.8%)   ← 均匀分布
十分位数 10: 21 个国家 (10.3%)  ← 均匀分布
```

## 十分位数分级详情

### 分级区间:
1. **-6.2 to -3.5**: 最大下降组 (21个国家)
2. **-3.5 to -2.9**: 很大下降组 (20个国家)
3. **-2.9 to -2.4**: 大幅下降组 (20个国家)
4. **-2.4 to -1.7**: 中度下降组 (21个国家)
5. **-1.7 to -1.4**: 轻度下降组 (20个国家)
6. **-1.4 to -1.0**: 小幅下降组 (20个国家)
7. **-1.0 to -0.7**: 轻微下降组 (21个国家)
8. **-0.7 to -0.4**: 微小下降组 (20个国家)
9. **-0.4 to -0.0**: 接近稳定组 (20个国家)
10. **-0.0 to 1.9**: 上升趋势组 (21个国家)

### 颜色方案优化:
- **深蓝色系** (#053061 → #4393c3): 表示DALYs大幅下降
- **浅蓝色系** (#92c5de → #d1e5f0): 表示DALYs中度下降
- **中性色** (#f7f7f7): 表示DALYs变化很小
- **浅橙色系** (#fddbc7 → #f4a582): 表示DALYs轻度上升
- **红色系** (#d6604d → #b2182b): 表示DALYs明显上升

## 优化效果

### 1. 视觉效果改善
- **颜色分布均匀**: 每种颜色在地图上的出现频率相近
- **层次更加清晰**: 10个精细级别提供更好的视觉区分
- **避免颜色集中**: 不再有某种颜色过度集中的问题

### 2. 数据表达更准确
- **相对排名清晰**: 十分位数直接反映国家在全球的相对位置
- **统计意义明确**: 每个十分位数包含10%的国家
- **比较更加公平**: 基于数据分布而非固定数值区间

### 3. 科学性提升
- **统计学基础**: 基于分位数的分级方法更科学
- **可重复性**: 方法标准化，适用于其他类似分析
- **国际惯例**: 符合流行病学研究中常用的分位数分级方法

## 生成文件对比

### 原始版本文件:
- `stroke_dalys_65plus_balanced_colors_complete_layout_1990_2021.png`
- `stroke_dalys_65plus_balanced_colors_main_map_1990_2021.png`

### 优化版本文件:
- `stroke_dalys_65plus_ultra_fine_colors_complete_layout_1990_2021.png` ⭐ **推荐**
- `stroke_dalys_65plus_ultra_fine_colors_main_map_1990_2021.png` ⭐ **推荐**
- `stroke_dalys_65plus_ultra_fine_legend_1990_2021.csv` (详细图例信息)

## 技术实现要点

### 1. 十分位数计算
```r
decile_breaks <- quantile(country_aapc$aapc, probs = seq(0, 1, 0.1))
```

### 2. 颜色方案设计
```r
ultra_fine_colors <- c(
  "#053061", "#2166ac", "#4393c3", "#92c5de", "#d1e5f0",
  "#f7f7f7", "#fddbc7", "#f4a582", "#d6604d", "#b2182b"
)
```

### 3. 分级应用
```r
map_data$aapc_decile <- cut(map_data$aapc, 
                           breaks = decile_breaks, 
                           include.lowest = TRUE)
```

## 结论

通过采用十分位数分级方法，成功解决了原始地图中颜色集中的问题：

1. **完美均匀分布**: 每个颜色级别包含20-21个国家
2. **视觉效果显著改善**: 颜色在地图上分布更加均匀
3. **科学性大幅提升**: 基于统计学原理的分级方法
4. **国际标准**: 符合流行病学研究的常用做法

**推荐使用**: `stroke_dalys_65plus_ultra_fine_colors_complete_layout_1990_2021.png` 作为最终版本。

---

**优化完成时间**: 2025年6月26日  
**优化方法**: 十分位数精细分级  
**效果**: 颜色分布完美均匀，视觉效果显著提升
