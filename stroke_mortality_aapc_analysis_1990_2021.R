# 脑卒中死亡率AAPC分析 (1990-2021)
# 基于本地死亡损伤数据库，参考T1DM分析模板

library(readr)
library(dplyr)
library(ggplot2)
library(maps)
library(RColorBrewer)
library(viridis)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)

# 读取完整数据
cat("正在读取脑卒中死亡率数据...\n")
stroke_data <- read_csv("stroke_mortality_65plus_complete_1990_2021.csv", show_col_types = FALSE)

# 数据预处理：计算年龄标准化死亡率
cat("正在计算年龄标准化死亡率...\n")

# 为每个国家和年份计算65岁以上年龄标准化死亡率
# 使用简单平均作为标准化方法
stroke_standardized <- stroke_data %>%
  group_by(location_name, year) %>%
  summarise(
    standardized_rate = mean(val, na.rm = TRUE),
    .groups = 'drop'
  ) %>%
  filter(!is.na(standardized_rate), standardized_rate > 0)

cat("标准化后数据行数:", nrow(stroke_standardized), "\n")
cat("国家数量:", length(unique(stroke_standardized$location_name)), "\n")
cat("年份范围:", min(stroke_standardized$year), "-", max(stroke_standardized$year), "\n")

# AAPC计算函数
calculate_aapc <- function(years, rates) {
  if(length(years) < 3 || length(rates) < 3) return(NA)
  
  # 移除NA值
  valid_idx <- !is.na(rates) & rates > 0
  if(sum(valid_idx) < 3) return(NA)
  
  years <- years[valid_idx]
  rates <- rates[valid_idx]
  
  # 对数线性回归
  tryCatch({
    model <- lm(log(rates) ~ years)
    slope <- coef(model)[2]
    aapc <- (exp(slope) - 1) * 100
    return(aapc)
  }, error = function(e) {
    return(NA)
  })
}

# 为每个国家计算AAPC
cat("正在计算AAPC...\n")
aapc_results <- stroke_standardized %>%
  group_by(location_name) %>%
  summarise(
    aapc = calculate_aapc(year, standardized_rate),
    n_years = n(),
    rate_1990 = standardized_rate[year == 1990][1],
    rate_2021 = standardized_rate[year == 2021][1],
    .groups = 'drop'
  ) %>%
  filter(!is.na(aapc), n_years >= 10) # 至少10年数据

cat("AAPC计算完成，有效国家数:", nrow(aapc_results), "\n")

# 查看AAPC分布
cat("\n=== AAPC分布统计 ===\n")
cat("最小值:", round(min(aapc_results$aapc, na.rm = TRUE), 2), "\n")
cat("最大值:", round(max(aapc_results$aapc, na.rm = TRUE), 2), "\n")
cat("中位数:", round(median(aapc_results$aapc, na.rm = TRUE), 2), "\n")
cat("平均值:", round(mean(aapc_results$aapc, na.rm = TRUE), 2), "\n")

# 保存AAPC结果
write.csv(aapc_results, "stroke_mortality_aapc_results_1990_2021.csv", row.names = FALSE)
cat("\nAPC结果已保存到: stroke_mortality_aapc_results_1990_2021.csv\n")

# 显示前10个国家的结果
cat("\n=== 前10个国家AAPC结果 ===\n")
print(head(aapc_results %>% arrange(desc(aapc)), 10))

cat("\n=== 后10个国家AAPC结果 ===\n")
print(head(aapc_results %>% arrange(aapc), 10))
