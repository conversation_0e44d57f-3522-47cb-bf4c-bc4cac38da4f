# 中国与世界卒中死亡率与发病率综合对比分析
# 基于死亡损伤数据库 1990-2021年

library(ggplot2)
library(dplyr)
library(readr)
library(scales)
library(gridExtra)
library(grid)
library(tidyr)

# 设置中文字体
if (.Platform$OS.type == "windows") {
  windowsFonts(SimSun = windowsFont("SimSun"))
  par(family = "SimSun")
}

cat("=== 中国与世界卒中死亡率与发病率综合对比分析 ===\n")

# 1. 读取死亡率数据
cat("1. 读取死亡率数据...\n")
mortality_data_china <- read_csv("global_china_stroke_mortality_trend_data_1990_2021.csv", show_col_types = FALSE)

# 2. 读取发病率数据
cat("2. 读取发病率数据...\n")
incidence_data_china <- read_csv("global_china_stroke_incidence_35plus_trend_data_1990_2021.csv", show_col_types = FALSE)

# 检查数据
cat("死亡率数据行数:", nrow(mortality_data_china), "\n")
cat("发病率数据行数:", nrow(incidence_data_china), "\n")

# 3. 数据预处理和合并
cat("3. 数据预处理和合并...\n")

# 重命名列以区分死亡率和发病率
mortality_clean <- mortality_data_china %>%
  select(location_name, year, mortality_rate = mortality_rate, 
         mortality_upper = upper_ci, mortality_lower = lower_ci)

incidence_clean <- incidence_data_china %>%
  select(location_name, year, incidence_rate = incidence_rate,
         incidence_upper = upper_ci, incidence_lower = lower_ci)

# 合并数据
combined_data <- mortality_clean %>%
  left_join(incidence_clean, by = c("location_name", "year"))

cat("合并后数据行数:", nrow(combined_data), "\n")

# 4. 计算死亡率/发病率比值（病死率指标）
combined_data <- combined_data %>%
  mutate(
    mortality_incidence_ratio = mortality_rate / incidence_rate,
    # 计算相对风险指标
    china_vs_global_mortality = ifelse(location_name == "中国", 
                                      mortality_rate / mortality_rate[location_name == "全球" & year == year[1]], 
                                      NA),
    china_vs_global_incidence = ifelse(location_name == "中国",
                                      incidence_rate / incidence_rate[location_name == "全球" & year == year[1]],
                                      NA)
  )

# 5. 计算AAPC（平均年度百分比变化）
calculate_aapc <- function(data, location, measure) {
  location_data <- data %>% 
    filter(location_name == location) %>%
    arrange(year)
  
  if(nrow(location_data) < 2) return(NA)
  
  if(measure == "mortality") {
    values <- location_data$mortality_rate
  } else if(measure == "incidence") {
    values <- location_data$incidence_rate
  } else if(measure == "ratio") {
    values <- location_data$mortality_incidence_ratio
  }
  
  # 使用线性回归计算AAPC
  model <- lm(log(values) ~ location_data$year)
  aapc <- (exp(coef(model)[2]) - 1) * 100
  
  return(aapc)
}

# 计算各项指标的AAPC
aapc_results <- data.frame(
  location = c("中国", "全球"),
  mortality_aapc = c(
    calculate_aapc(combined_data, "中国", "mortality"),
    calculate_aapc(combined_data, "全球", "mortality")
  ),
  incidence_aapc = c(
    calculate_aapc(combined_data, "中国", "incidence"),
    calculate_aapc(combined_data, "全球", "incidence")
  ),
  ratio_aapc = c(
    calculate_aapc(combined_data, "中国", "ratio"),
    calculate_aapc(combined_data, "全球", "ratio")
  )
)

# 6. 创建综合对比图表

# 6.1 死亡率vs发病率趋势对比
p1 <- ggplot(combined_data, aes(x = year)) +
  geom_line(aes(y = mortality_rate, color = location_name, linetype = "死亡率"), size = 1.2) +
  geom_line(aes(y = incidence_rate, color = location_name, linetype = "发病率"), size = 1.2) +
  geom_point(aes(y = mortality_rate, color = location_name), size = 1.5, alpha = 0.7) +
  geom_point(aes(y = incidence_rate, color = location_name), size = 1.5, alpha = 0.7) +
  labs(
    title = "中国与世界卒中死亡率与发病率趋势对比 (1990-2021)",
    subtitle = "35岁以上人群年龄标准化率（每10万人）",
    x = "年份",
    y = "率（每10万人）",
    color = "地区",
    linetype = "指标类型"
  ) +
  scale_color_manual(values = c("中国" = "#E31A1C", "全球" = "#1F78B4")) +
  scale_linetype_manual(values = c("死亡率" = "solid", "发病率" = "dashed")) +
  scale_x_continuous(breaks = seq(1990, 2021, 5)) +
  scale_y_continuous(labels = comma_format()) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 16, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5, size = 12),
    legend.position = "bottom",
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 11),
    axis.title = element_text(size = 12, face = "bold"),
    axis.text = element_text(size = 10)
  )

# 6.2 死亡率/发病率比值趋势（病死率指标）
p2 <- ggplot(combined_data, aes(x = year, y = mortality_incidence_ratio, color = location_name)) +
  geom_line(size = 1.2) +
  geom_point(size = 2, alpha = 0.8) +
  labs(
    title = "死亡率/发病率比值趋势",
    subtitle = "反映疾病严重程度和医疗水平",
    x = "年份",
    y = "死亡率/发病率比值",
    color = "地区"
  ) +
  scale_color_manual(values = c("中国" = "#E31A1C", "全球" = "#1F78B4")) +
  scale_x_continuous(breaks = seq(1990, 2021, 5)) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5, size = 11),
    legend.position = "bottom",
    axis.title = element_text(size = 12, face = "bold"),
    axis.text = element_text(size = 10)
  )

# 6.3 AAPC对比图
aapc_long <- aapc_results %>%
  pivot_longer(cols = c(mortality_aapc, incidence_aapc, ratio_aapc),
               names_to = "measure", values_to = "aapc") %>%
  mutate(
    measure_label = case_when(
      measure == "mortality_aapc" ~ "死亡率",
      measure == "incidence_aapc" ~ "发病率",
      measure == "ratio_aapc" ~ "死亡率/发病率比值"
    )
  )

p3 <- ggplot(aapc_long, aes(x = measure_label, y = aapc, fill = location)) +
  geom_col(position = "dodge", alpha = 0.8, width = 0.7) +
  geom_text(aes(label = paste0(round(aapc, 2), "%/年")), 
            position = position_dodge(width = 0.7), vjust = -0.5, size = 4) +
  labs(
    title = "平均年度百分比变化对比 (1990-2021)",
    x = "指标",
    y = "AAPC (%/年)",
    fill = "地区"
  ) +
  scale_fill_manual(values = c("中国" = "#E31A1C", "全球" = "#1F78B4")) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    legend.position = "bottom",
    axis.title = element_text(size = 12, face = "bold"),
    axis.text = element_text(size = 11),
    axis.text.x = element_text(angle = 45, hjust = 1)
  )

# 6.4 散点图：死亡率 vs 发病率关系
p4 <- ggplot(combined_data, aes(x = incidence_rate, y = mortality_rate, color = location_name)) +
  geom_point(aes(size = year), alpha = 0.7) +
  geom_smooth(method = "lm", se = TRUE, alpha = 0.3) +
  labs(
    title = "死亡率与发病率关系",
    subtitle = "点的大小表示年份（越大越近期）",
    x = "发病率（每10万人）",
    y = "死亡率（每10万人）",
    color = "地区",
    size = "年份"
  ) +
  scale_color_manual(values = c("中国" = "#E31A1C", "全球" = "#1F78B4")) +
  scale_size_continuous(range = c(1, 4)) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5, size = 11),
    legend.position = "bottom",
    axis.title = element_text(size = 12, face = "bold"),
    axis.text = element_text(size = 10)
  )

# 保存综合图表
png("china_world_stroke_comprehensive_analysis_1990_2021.png", 
    width = 20, height = 16, units = "in", res = 300)

grid.arrange(
  p1,
  arrangeGrob(p2, p3, ncol = 2),
  p4,
  ncol = 1,
  heights = c(2, 1.5, 1.5)
)

dev.off()

# 7. 创建数据摘要表
summary_stats <- combined_data %>%
  group_by(location_name) %>%
  summarise(
    # 1990年数据
    mortality_1990 = mortality_rate[year == 1990],
    incidence_1990 = incidence_rate[year == 1990],
    ratio_1990 = mortality_incidence_ratio[year == 1990],
    
    # 2021年数据
    mortality_2021 = mortality_rate[year == 2021],
    incidence_2021 = incidence_rate[year == 2021],
    ratio_2021 = mortality_incidence_ratio[year == 2021],
    
    # 绝对变化
    mortality_change = mortality_2021 - mortality_1990,
    incidence_change = incidence_2021 - incidence_1990,
    ratio_change = ratio_2021 - ratio_1990,
    
    # 相对变化
    mortality_pct_change = (mortality_2021 / mortality_1990 - 1) * 100,
    incidence_pct_change = (incidence_2021 / incidence_1990 - 1) * 100,
    ratio_pct_change = (ratio_2021 / ratio_1990 - 1) * 100,
    
    .groups = 'drop'
  ) %>%
  left_join(aapc_results, by = c("location_name" = "location"))

# 保存数据
write.csv(combined_data, "china_world_stroke_comprehensive_data_1990_2021.csv", 
          row.names = FALSE, fileEncoding = "UTF-8")
write.csv(summary_stats, "china_world_stroke_summary_statistics_1990_2021.csv", 
          row.names = FALSE, fileEncoding = "UTF-8")
write.csv(aapc_results, "china_world_stroke_aapc_results_1990_2021.csv", 
          row.names = FALSE, fileEncoding = "UTF-8")

cat("\n=== 中国与世界卒中死亡率与发病率综合分析摘要 ===\n")
cat("数据时间范围: 1990-2021年\n")
cat("研究人群: 35岁以上人群\n")
cat("数据类型: 年龄标准化率（每10万人）\n\n")

# 输出主要发现
cat("=== 主要发现 ===\n\n")

cat("1. 死亡率对比:\n")
cat("1990年 - 中国:", round(summary_stats$mortality_1990[summary_stats$location_name == "中国"], 1), 
    "/10万人, 全球:", round(summary_stats$mortality_1990[summary_stats$location_name == "全球"], 1), "/10万人\n")
cat("2021年 - 中国:", round(summary_stats$mortality_2021[summary_stats$location_name == "中国"], 1), 
    "/10万人, 全球:", round(summary_stats$mortality_2021[summary_stats$location_name == "全球"], 1), "/10万人\n")
cat("AAPC - 中国:", round(aapc_results$mortality_aapc[aapc_results$location == "中国"], 2), 
    "%/年, 全球:", round(aapc_results$mortality_aapc[aapc_results$location == "全球"], 2), "%/年\n\n")

cat("2. 发病率对比:\n")
cat("1990年 - 中国:", round(summary_stats$incidence_1990[summary_stats$location_name == "中国"], 1), 
    "/10万人, 全球:", round(summary_stats$incidence_1990[summary_stats$location_name == "全球"], 1), "/10万人\n")
cat("2021年 - 中国:", round(summary_stats$incidence_2021[summary_stats$location_name == "中国"], 1), 
    "/10万人, 全球:", round(summary_stats$incidence_2021[summary_stats$location_name == "全球"], 1), "/10万人\n")
cat("AAPC - 中国:", round(aapc_results$incidence_aapc[aapc_results$location == "中国"], 2), 
    "%/年, 全球:", round(aapc_results$incidence_aapc[aapc_results$location == "全球"], 2), "%/年\n\n")

cat("3. 死亡率/发病率比值对比:\n")
cat("1990年 - 中国:", round(summary_stats$ratio_1990[summary_stats$location_name == "中国"], 3), 
    ", 全球:", round(summary_stats$ratio_1990[summary_stats$location_name == "全球"], 3), "\n")
cat("2021年 - 中国:", round(summary_stats$ratio_2021[summary_stats$location_name == "中国"], 3), 
    ", 全球:", round(summary_stats$ratio_2021[summary_stats$location_name == "全球"], 3), "\n")
cat("AAPC - 中国:", round(aapc_results$ratio_aapc[aapc_results$location == "中国"], 2), 
    "%/年, 全球:", round(aapc_results$ratio_aapc[aapc_results$location == "全球"], 2), "%/年\n\n")

cat("图表已保存为: china_world_stroke_comprehensive_analysis_1990_2021.png\n")
cat("数据已保存为: china_world_stroke_comprehensive_data_1990_2021.csv\n")
cat("摘要统计已保存为: china_world_stroke_summary_statistics_1990_2021.csv\n")

cat("\n分析完成！\n")
