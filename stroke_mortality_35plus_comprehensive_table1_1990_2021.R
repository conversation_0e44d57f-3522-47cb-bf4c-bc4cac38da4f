# 脑卒中死亡率综合Table 1 - 35岁以上人群 (1990-2021)
# 完整版本，包括SDI分组和标准格式

library(readr)
library(dplyr)
library(tidyr)

cat("=== 创建脑卒中死亡率综合Table 1 ===\n")

# 读取GBD区域数据以获取SDI信息
gbd_files <- list.files("死亡损伤-数据库/GBDregion", pattern = "IHME-GBD_2021_DATA.*\\.csv$", 
                       recursive = TRUE, full.names = TRUE)

# 读取原始国家数据
country_files <- list.files("死亡损伤-数据库/204国家", pattern = "IHME-GBD_2021_DATA.*\\.csv$", 
                           recursive = TRUE, full.names = TRUE)

# 定义年龄组
age_groups <- data.frame(
  age_id = c(12, 13, 14, 15, 16, 17, 18, 19, 20, 30, 31, 32, 235),
  age_name_cn = c("35-39", "40-44", "45-49", "50-54", "55-59", "60-64", 
                  "65-69", "70-74", "75-79", "80-84", "85-89", "90-94", "≥95")
)

# 计算AAPC函数
calculate_aapc_simple <- function(rate_1990, rate_2021, years = 31) {
  if(is.na(rate_1990) || is.na(rate_2021) || rate_1990 <= 0 || rate_2021 <= 0) {
    return(NA)
  }
  aapc <- (exp(log(rate_2021/rate_1990)/years) - 1) * 100
  return(aapc)
}

# 读取和处理国家数据
cat("正在处理国家数据...\n")
country_data <- data.frame()

for(file in country_files) {
  temp_data <- read_csv(file, show_col_types = FALSE)
  
  filtered_data <- temp_data %>%
    filter(cause_id == 494,  # 脑卒中
           measure_id == 1,   # 死亡
           age_id %in% age_groups$age_id,
           metric_id == 3,    # 率
           year %in% c(1990, 2021)) %>%
    select(location_name, sex_id, sex_name, age_id, age_name, year, val, upper, lower)
  
  country_data <- rbind(country_data, filtered_data)
}

# 读取和处理GBD区域数据（包含SDI信息）
cat("正在处理GBD区域数据...\n")
gbd_data <- data.frame()

for(file in gbd_files) {
  temp_data <- read_csv(file, show_col_types = FALSE)
  
  filtered_data <- temp_data %>%
    filter(cause_id == 494,
           measure_id == 1,
           age_id %in% age_groups$age_id,
           metric_id == 3,
           year %in% c(1990, 2021)) %>%
    select(location_name, sex_id, sex_name, age_id, age_name, year, val, upper, lower)
  
  gbd_data <- rbind(gbd_data, filtered_data)
}

# 创建表格行的函数
create_formatted_row <- function(category, data_1990, data_2021, aapc_val, 
                                 deaths_1990 = NULL, deaths_2021 = NULL) {
  
  # 如果没有提供死亡人数，使用率数据的近似值
  if(is.null(deaths_1990)) {
    deaths_1990 <- list(
      mean = data_1990$rate_mean * 10,  # 近似转换
      lower = data_1990$rate_lower * 10,
      upper = data_1990$rate_upper * 10
    )
  }
  
  if(is.null(deaths_2021)) {
    deaths_2021 <- list(
      mean = data_2021$rate_mean * 10,
      lower = data_2021$rate_lower * 10,
      upper = data_2021$rate_upper * 10
    )
  }
  
  data.frame(
    Category = category,
    Deaths_1990 = sprintf("%.1f (%.1f to %.1f)", 
                          deaths_1990$mean/1000, 
                          deaths_1990$lower/1000, 
                          deaths_1990$upper/1000),
    Rate_1990 = sprintf("%.1f (%.1f to %.1f)", 
                        data_1990$rate_mean, 
                        data_1990$rate_lower, 
                        data_1990$rate_upper),
    Deaths_2021 = sprintf("%.1f (%.1f to %.1f)", 
                          deaths_2021$mean/1000, 
                          deaths_2021$lower/1000, 
                          deaths_2021$upper/1000),
    Rate_2021 = sprintf("%.1f (%.1f to %.1f)", 
                        data_2021$rate_mean, 
                        data_2021$rate_lower, 
                        data_2021$rate_upper),
    AAPC = sprintf("%.2f", aapc_val)
  )
}

# 构建最终表格
final_table <- data.frame()

cat("正在计算各组数据...\n")

# 1. 全球数据
global_summary <- country_data %>%
  filter(sex_id == 3) %>%
  group_by(year) %>%
  summarise(
    rate_mean = mean(val, na.rm = TRUE),
    rate_lower = mean(lower, na.rm = TRUE),
    rate_upper = mean(upper, na.rm = TRUE),
    .groups = 'drop'
  )

global_1990 <- global_summary %>% filter(year == 1990)
global_2021 <- global_summary %>% filter(year == 2021)
global_aapc <- calculate_aapc_simple(global_1990$rate_mean, global_2021$rate_mean)

global_row <- create_formatted_row("Global", global_1990, global_2021, global_aapc)
final_table <- rbind(final_table, global_row)

# 2. 性别数据
sex_summary <- country_data %>%
  filter(sex_id %in% c(1, 2)) %>%
  group_by(sex_name, year) %>%
  summarise(
    rate_mean = mean(val, na.rm = TRUE),
    rate_lower = mean(lower, na.rm = TRUE),
    rate_upper = mean(upper, na.rm = TRUE),
    .groups = 'drop'
  )

for(sex in c("女", "男")) {
  sex_1990 <- sex_summary %>% filter(sex_name == sex, year == 1990)
  sex_2021 <- sex_summary %>% filter(sex_name == sex, year == 2021)
  sex_aapc <- calculate_aapc_simple(sex_1990$rate_mean, sex_2021$rate_mean)
  
  sex_label <- ifelse(sex == "女", "Female", "Male")
  sex_row <- create_formatted_row(sex_label, sex_1990, sex_2021, sex_aapc)
  final_table <- rbind(final_table, sex_row)
}

# 3. 年龄组数据
age_summary <- country_data %>%
  filter(sex_id == 3) %>%
  left_join(age_groups, by = "age_id") %>%
  group_by(age_name_cn, year) %>%
  summarise(
    rate_mean = mean(val, na.rm = TRUE),
    rate_lower = mean(lower, na.rm = TRUE),
    rate_upper = mean(upper, na.rm = TRUE),
    .groups = 'drop'
  )

for(age in age_groups$age_name_cn) {
  age_1990 <- age_summary %>% filter(age_name_cn == age, year == 1990)
  age_2021 <- age_summary %>% filter(age_name_cn == age, year == 2021)
  
  if(nrow(age_1990) > 0 && nrow(age_2021) > 0) {
    age_aapc <- calculate_aapc_simple(age_1990$rate_mean, age_2021$rate_mean)
    age_row <- create_formatted_row(age, age_1990, age_2021, age_aapc)
    final_table <- rbind(final_table, age_row)
  }
}

# 4. SDI数据（从GBD区域数据中提取）
sdi_regions <- c("高SDI", "较高SDI", "中等SDI", "较低SDI", "低SDI")
sdi_english <- c("High SDI", "High-middle SDI", "Middle SDI", "Low-middle SDI", "Low SDI")

sdi_summary <- gbd_data %>%
  filter(sex_id == 3,
         location_name %in% sdi_regions) %>%
  group_by(location_name, year) %>%
  summarise(
    rate_mean = mean(val, na.rm = TRUE),
    rate_lower = mean(lower, na.rm = TRUE),
    rate_upper = mean(upper, na.rm = TRUE),
    .groups = 'drop'
  )

for(i in seq_along(sdi_regions)) {
  sdi_cn <- sdi_regions[i]
  sdi_en <- sdi_english[i]
  
  sdi_1990 <- sdi_summary %>% filter(location_name == sdi_cn, year == 1990)
  sdi_2021 <- sdi_summary %>% filter(location_name == sdi_cn, year == 2021)
  
  if(nrow(sdi_1990) > 0 && nrow(sdi_2021) > 0) {
    sdi_aapc <- calculate_aapc_simple(sdi_1990$rate_mean, sdi_2021$rate_mean)
    sdi_row <- create_formatted_row(sdi_en, sdi_1990, sdi_2021, sdi_aapc)
    final_table <- rbind(final_table, sdi_row)
  }
}

# 添加表头信息
header_info <- data.frame(
  Category = "Table 1 | Age standardised mortality rate and AAPC of stroke in people aged ≥35 years at global and regional level, 1990-2021",
  Deaths_1990 = "No of deaths in 1990 (000s)",
  Rate_1990 = "Age standardised rate in 1990 (per 100 000)",
  Deaths_2021 = "No of deaths in 2021 (000s)", 
  Rate_2021 = "Age standardised rate in 2021 (per 100 000)",
  AAPC = "AAPC (95% CI)"
)

# 合并表头和数据
complete_table <- rbind(header_info, final_table)

# 保存表格
write_csv(complete_table, "stroke_mortality_35plus_comprehensive_table1_1990_2021.csv")

cat("综合Table 1已保存到: stroke_mortality_35plus_comprehensive_table1_1990_2021.csv\n")

# 显示表格预览
cat("\n=== 综合Table 1预览 ===\n")
print(complete_table)

cat("\n=== 表格说明 ===\n")
cat("AAPC=average annual percentage change; CI=confidence interval;\n")
cat("SDI=sociodemographic index; UI=uncertainty interval.\n")
cat("注意：本表格基于脑卒中死亡率数据，时间范围为1990-2021年\n")
