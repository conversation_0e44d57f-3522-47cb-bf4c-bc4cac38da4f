# 脑卒中65岁以上人群死亡率AAPC全球地图 - 改进图例版本
# 确保所有分组都在图例中显示

library(ggplot2)
library(dplyr)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(RColorBrewer)
library(countrycode)
library(gridExtra)
library(grid)
library(scales)

# 读取数据
data <- read.csv("脑卒中65岁以上人群患病率分析表.csv", 
                 stringsAsFactors = FALSE, 
                 fileEncoding = "UTF-8")

# 提取地区AAPC数据
region_aapc <- data %>%
  filter(分类 %in% c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", 
                    "高收入", "中欧、东欧和中亚", "拉丁美洲和加勒比海", 
                    "北非和中东")) %>%
  select(分类, AAPC = `AAPC...`)

# 创建国家到地区映射
country_region_mapping <- data.frame(
  country = c(
    # 撒哈拉以南非洲
    "Nigeria", "Ethiopia", "South Africa", "Kenya", "Uganda", "Tanzania", 
    "Ghana", "Mozambique", "Madagascar", "Cameroon", "Angola", "Niger", 
    "Burkina Faso", "Mali", "Malawi", "Zambia", "Somalia", "Senegal", 
    "Chad", "Zimbabwe", "Guinea", "Rwanda", "Benin", "Burundi", 
    "South Sudan", "Togo", "Sierra Leone", "Liberia", "Botswana", 
    "Namibia", "Gabon", "Lesotho", "Guinea-Bissau", "Equatorial Guinea", 
    "Mauritius", "Eswatini", "Djibouti", "Comoros", "Cape Verde", 
    "Sao Tome and Principe", "Central African Republic", "Mauritania", 
    "Eritrea", "Gambia",
    
    # 东南亚、东亚和大洋洲
    "China", "Indonesia", "Japan", "Philippines", "Vietnam", "Thailand", 
    "Myanmar", "South Korea", "Malaysia", "Cambodia", "Laos", "Singapore", 
    "Mongolia", "Brunei", "Timor-Leste", "Australia", "Papua New Guinea", 
    "New Zealand", "Fiji", "Solomon Islands", "Vanuatu", "Samoa", 
    "Kiribati", "Tonga", "Micronesia", "Palau", "Marshall Islands", 
    "Nauru", "Tuvalu",
    
    # 南亚
    "India", "Pakistan", "Bangladesh", "Afghanistan", "Nepal", "Sri Lanka", 
    "Bhutan", "Maldives",
    
    # 高收入国家
    "United States", "Germany", "United Kingdom", "France", "Italy", 
    "Canada", "Spain", "Netherlands", "Belgium", "Switzerland", "Austria", 
    "Sweden", "Norway", "Denmark", "Finland", "Ireland", "Portugal", 
    "Greece", "Israel", "Luxembourg", "Iceland", "Malta", "Cyprus",
    
    # 中欧、东欧和中亚
    "Russia", "Poland", "Ukraine", "Romania", "Czech Republic", "Hungary", 
    "Belarus", "Bulgaria", "Serbia", "Slovakia", "Croatia", 
    "Bosnia and Herzegovina", "Albania", "Lithuania", "Slovenia", 
    "Latvia", "Estonia", "North Macedonia", "Moldova", "Montenegro", 
    "Kazakhstan", "Uzbekistan", "Tajikistan", "Kyrgyzstan", "Turkmenistan", 
    "Georgia", "Armenia", "Azerbaijan",
    
    # 拉丁美洲和加勒比海
    "Brazil", "Mexico", "Colombia", "Argentina", "Peru", "Venezuela", 
    "Chile", "Ecuador", "Guatemala", "Cuba", "Bolivia", "Haiti", 
    "Dominican Republic", "Honduras", "Paraguay", "Nicaragua", 
    "El Salvador", "Costa Rica", "Panama", "Uruguay", "Jamaica", 
    "Trinidad and Tobago", "Guyana", "Suriname", "Belize", "Barbados", 
    "Saint Vincent and the Grenadines", "Grenada", "Saint Lucia", 
    "Antigua and Barbuda", "Dominica", "Saint Kitts and Nevis",
    
    # 北非和中东
    "Egypt", "Iran", "Turkey", "Iraq", "Saudi Arabia", "Yemen", "Syria", 
    "Jordan", "Lebanon", "United Arab Emirates", "Oman", "Kuwait", "Qatar", 
    "Bahrain", "Algeria", "Morocco", "Sudan", "Tunisia", "Libya"
  ),
  region = c(
    rep("撒哈拉以南非洲", 44),
    rep("东南亚、东亚和大洋洲", 29),
    rep("南亚", 8),
    rep("高收入", 23),
    rep("中欧、东欧和中亚", 28),
    rep("拉丁美洲和加勒比海", 32),
    rep("北非和中东", 19)
  )
)

# 合并数据
country_data <- country_region_mapping %>%
  left_join(region_aapc, by = c("region" = "分类"))

# 获取世界地图数据
world <- ne_countries(scale = "medium", returnclass = "sf")

# 标准化国家名称
world$name_clean <- countrycode(world$name, "country.name", "country.name")
country_data$country_clean <- countrycode(country_data$country, 
                                         "country.name", "country.name")

# 合并地图数据和AAPC数据
world_data <- world %>%
  left_join(country_data, by = c("name_clean" = "country_clean"))

# 基于实际数据范围创建AAPC分组，并确保覆盖所有可能的值
world_data <- world_data %>%
  mutate(
    aapc_group = case_when(
      is.na(AAPC) ~ "No data",
      AAPC < -0.75 ~ "< -0.75",
      AAPC >= -0.75 & AAPC < -0.5 ~ "-0.75 to < -0.5",
      AAPC >= -0.5 & AAPC < -0.25 ~ "-0.5 to < -0.25",
      AAPC >= -0.25 & AAPC < 0 ~ "-0.25 to < 0",
      AAPC >= 0 & AAPC < 0.25 ~ "0 to < 0.25",
      AAPC >= 0.25 & AAPC < 0.5 ~ "0.25 to < 0.5",
      AAPC >= 0.5 & AAPC < 0.75 ~ "0.5 to < 0.75",
      AAPC >= 0.75 & AAPC < 1.0 ~ "0.75 to < 1.0",
      AAPC >= 1.0 & AAPC < 1.25 ~ "1.0 to < 1.25",
      AAPC >= 1.25 ~ "≥ 1.25"
    )
  )

# 定义所有可能的分组级别
all_levels <- c("< -0.75", "-0.75 to < -0.5", "-0.5 to < -0.25", 
               "-0.25 to < 0", "0 to < 0.25", "0.25 to < 0.5",
               "0.5 to < 0.75", "0.75 to < 1.0", "1.0 to < 1.25", 
               "≥ 1.25", "No data")

# 将aapc_group转换为因子，包含所有级别
world_data$aapc_group <- factor(world_data$aapc_group, levels = all_levels)

# 创建渐变颜色调色板（蓝色表示改善，红色表示恶化）
colors <- c("#08306b", "#08519c", "#2171b5", "#4292c6", "#6baed6",
           "#fee0d2", "#fcbba1", "#fc9272", "#fb6a4a", "#de2d26", "#cccccc")
names(colors) <- all_levels

# 创建主地图，强制显示所有图例项
main_map <- ggplot(world_data) +
  geom_sf(aes(fill = aapc_group), color = "white", linewidth = 0.1) +
  scale_fill_manual(values = colors, name = "AAPC (%)", 
                   drop = FALSE, na.value = "#cccccc") +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 14, face = "bold"),
    legend.text = element_text(size = 10),
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 13, hjust = 0.5),
    legend.key.size = unit(0.8, "cm"),
    legend.key.width = unit(1.0, "cm"),
    plot.margin = margin(10, 10, 10, 10)
  ) +
  labs(
    title = "Average Annual Percentage Change in Stroke Mortality",
    subtitle = "Among people aged ≥65 years, 1990-2019"
  ) +
  guides(fill = guide_legend(nrow = 3, byrow = TRUE, 
                            override.aes = list(color = "black", linewidth = 0.5)))

# 保存主地图
ggsave("stroke_improved_legend_main_map.png", main_map, 
       width = 18, height = 12, dpi = 300, bg = "white")

print("改进版主地图已保存为: stroke_improved_legend_main_map.png")
print(main_map)

# 创建区域地图函数
create_regional_map <- function(world_data, xlim, ylim, title, colors) {
  ggplot(world_data) +
    geom_sf(aes(fill = aapc_group), color = "white", linewidth = 0.2) +
    scale_fill_manual(values = colors, name = "AAPC (%)",
                     drop = FALSE, na.value = "#cccccc") +
    coord_sf(xlim = xlim, ylim = ylim, expand = FALSE) +
    theme_void() +
    theme(
      legend.position = "none",
      plot.title = element_text(size = 12, face = "bold", hjust = 0.5),
      plot.margin = margin(5, 5, 5, 5),
      panel.border = element_rect(color = "black", fill = NA, linewidth = 1)
    ) +
    labs(title = title)
}

# 定义关键区域
regions <- list(
  caribbean = list(xlim = c(-90, -55), ylim = c(10, 30),
                  title = "Caribbean"),
  persian_gulf = list(xlim = c(45, 60), ylim = c(22, 32),
                     title = "Persian Gulf"),
  balkans = list(xlim = c(12, 30), ylim = c(40, 48),
                title = "Balkans"),
  southeast_asia = list(xlim = c(90, 140), ylim = c(-10, 25),
                       title = "Southeast Asia"),
  west_africa = list(xlim = c(-20, 20), ylim = c(0, 20),
                    title = "West Africa"),
  northern_europe = list(xlim = c(-10, 35), ylim = c(55, 72),
                        title = "Northern Europe")
)

# 创建区域地图
regional_maps <- list()
for(i in 1:length(regions)) {
  region_name <- names(regions)[i]
  region_info <- regions[[i]]

  regional_maps[[region_name]] <- create_regional_map(
    world_data,
    region_info$xlim,
    region_info$ylim,
    region_info$title,
    colors
  )
}

# 组合区域地图
combined_regional <- arrangeGrob(
  regional_maps$caribbean, regional_maps$persian_gulf,
  regional_maps$balkans, regional_maps$southeast_asia,
  regional_maps$west_africa, regional_maps$northern_europe,
  ncol = 3, nrow = 2,
  top = textGrob("Regional Focus Areas: Stroke Mortality AAPC (1990-2019)",
                 gp = gpar(fontsize = 14, fontface = "bold"))
)

# 保存区域地图
ggsave("stroke_improved_legend_regional_maps.png", combined_regional,
       width = 16, height = 10, dpi = 300, bg = "white")

print("改进版区域地图已保存为: stroke_improved_legend_regional_maps.png")

# 打印分组统计
cat("\n改进版AAPC分组统计:\n")
print(table(world_data$aapc_group, useNA = "ifany"))

cat("\n实际AAPC值分布:\n")
print(summary(world_data$AAPC))

cat("\n图例说明:\n")
cat("• 深蓝色: 死亡率显著下降 (< -0.75%)\n")
cat("• 浅蓝色: 死亡率轻微下降 (-0.75% to 0%)\n")
cat("• 浅红色: 死亡率轻微上升 (0% to 0.75%)\n")
cat("• 深红色: 死亡率显著上升 (≥ 0.75%)\n")
cat("• 灰色: 无数据\n")
