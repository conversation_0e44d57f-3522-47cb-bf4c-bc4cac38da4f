# 检查死亡损伤数据库中是否有详细的脑卒中分类数据
# 特别查找出血性和缺血性脑卒中的具体分类

library(dplyr)
library(readr)

# 检查GBD super region数据库
file_path <- "死亡损伤-数据库/GBD super region/IHME-GBD_2021_DATA-26cc51e1-1.csv"

cat("检查文件:", file_path, "\n")

# 读取数据
data <- read_csv(file_path, show_col_types = FALSE)

cat("数据总行数:", nrow(data), "\n")
cat("数据列名:", paste(colnames(data), collapse = ", "), "\n\n")

# 检查所有独特的疾病分类
unique_causes <- unique(data$cause_name)
cat("发现", length(unique_causes), "种疾病分类\n\n")

# 查找所有脑卒中相关的疾病
stroke_causes <- unique_causes[grepl("脑卒中|stroke|出血|hemorrhagic|缺血|ischemic|intracerebral|subarachnoid", 
                                    unique_causes, ignore.case = TRUE)]

cat("脑卒中相关疾病分类:\n")
for(i in 1:length(stroke_causes)) {
  cat(i, ":", stroke_causes[i], "\n")
}

# 检查是否有东南亚、东亚和大洋洲的数据
asia_data <- data %>% 
  filter(grepl("东南亚、东亚和大洋洲|East Asia", location_name, ignore.case = TRUE))

if(nrow(asia_data) > 0) {
  cat("\n发现东南亚、东亚和大洋洲数据:", nrow(asia_data), "行\n")
  
  # 检查这个地区的脑卒中数据
  asia_stroke_data <- asia_data %>%
    filter(grepl("脑卒中|stroke", cause_name, ignore.case = TRUE))
  
  if(nrow(asia_stroke_data) > 0) {
    cat("东南亚、东亚和大洋洲脑卒中数据:", nrow(asia_stroke_data), "行\n")
    
    # 检查脑卒中的具体分类
    asia_stroke_causes <- unique(asia_stroke_data$cause_name)
    cat("东南亚、东亚和大洋洲脑卒中疾病分类:\n")
    for(cause in asia_stroke_causes) {
      count <- sum(asia_stroke_data$cause_name == cause)
      cat("  -", cause, "(", count, "条记录)\n")
    }
    
    # 检查年份范围
    years <- range(asia_stroke_data$year, na.rm = TRUE)
    cat("年份范围:", years[1], "-", years[2], "\n")
    
    # 检查年龄组
    age_groups <- unique(asia_stroke_data$age_name)
    cat("年龄组数量:", length(age_groups), "\n")
    cat("年龄组:", paste(age_groups, collapse = ", "), "\n")
    
    # 检查测量类型
    measures <- unique(asia_stroke_data$measure_name)
    cat("测量类型:", paste(measures, collapse = ", "), "\n")
    
    # 检查指标类型
    metrics <- unique(asia_stroke_data$metric_name)
    cat("指标类型:", paste(metrics, collapse = ", "), "\n")
    
    # 检查是否有35岁以上的数据
    adult_ages <- age_groups[!grepl("0-|1-|5-|10-|15-|20-|25-|30-", age_groups)]
    if(length(adult_ages) > 0) {
      cat("35岁以上年龄组:", length(adult_ages), "个\n")
      cat("35岁以上年龄组详情:", paste(adult_ages, collapse = ", "), "\n")
    }
    
    # 显示一些样本数据
    cat("\n样本数据（前5行）:\n")
    sample_data <- asia_stroke_data %>% 
      select(location_name, cause_name, measure_name, age_name, year, val) %>%
      head(5)
    print(sample_data)
  }
}

# 检查是否有其他可能包含中国数据的地区
cat("\n\n=== 检查所有地区名称 ===\n")
unique_locations <- unique(data$location_name)
cat("发现", length(unique_locations), "个地区\n")

# 查找可能包含中国的地区
china_related <- unique_locations[grepl("中国|China|东亚|East Asia|亚洲|Asia", 
                                       unique_locations, ignore.case = TRUE)]

if(length(china_related) > 0) {
  cat("可能包含中国的地区:\n")
  for(i in 1:length(china_related)) {
    cat(i, ":", china_related[i], "\n")
  }
} else {
  cat("未发现明确包含中国的地区名称\n")
}

# 检查cause_id，看是否有更详细的脑卒中分类
cat("\n\n=== 检查cause_id ===\n")
stroke_data_all <- data %>%
  filter(grepl("脑卒中|stroke", cause_name, ignore.case = TRUE))

if(nrow(stroke_data_all) > 0) {
  unique_cause_ids <- unique(stroke_data_all$cause_id)
  cat("脑卒中相关的cause_id:", paste(unique_cause_ids, collapse = ", "), "\n")
  
  # 检查每个cause_id对应的疾病名称
  cause_id_mapping <- stroke_data_all %>%
    select(cause_id, cause_name) %>%
    distinct() %>%
    arrange(cause_id)
  
  cat("cause_id与疾病名称对应关系:\n")
  for(i in 1:nrow(cause_id_mapping)) {
    cat("  cause_id", cause_id_mapping$cause_id[i], ":", cause_id_mapping$cause_name[i], "\n")
  }
}

cat("\n检查完成！\n")

# 结论
cat("\n=== 结论 ===\n")
if(length(stroke_causes) == 1 && stroke_causes[1] == "脑卒中") {
  cat("❌ 数据库中只有总体'脑卒中'分类，没有出血性和缺血性的详细分类\n")
  cat("❌ 无法获取真实的出血性脑卒中数据，需要继续使用估值计算方法\n")
} else {
  cat("✅ 数据库中包含详细的脑卒中分类\n")
  cat("✅ 可以使用真实数据替代估值计算\n")
}
