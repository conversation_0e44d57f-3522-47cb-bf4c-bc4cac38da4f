# 基于更新数据的脑卒中65岁以上人群发病率可视化
# 使用1990-2021年完整数据

library(ggplot2)
library(dplyr)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(RColorBrewer)
library(gridExtra)
library(grid)
library(scales)

cat("=== 脑卒中65岁以上人群发病率可视化 (更新版) ===\n")

# 读取更新的分析表
stroke_data <- read.csv("脑卒中65岁以上人群发病率分析表_更新版.csv", 
                       stringsAsFactors = FALSE, 
                       fileEncoding = "UTF-8")

cat("数据读取完成，共", nrow(stroke_data), "行\n")

# 提取地区AAPC数据
region_aapc <- stroke_data %>%
  filter(分类 %in% c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", 
                    "高收入", "中欧、东欧和中亚", "拉丁美洲和加勒比海", 
                    "北非和中东")) %>%
  select(
    地区 = 分类,
    AAPC = `AAPC...`
  )

cat("地区AAPC数据提取完成，共", nrow(region_aapc), "个地区\n")

# 获取世界地图
world <- ne_countries(scale = "medium", returnclass = "sf")

# 创建国家映射（使用更新的地区分类）
create_country_mapping <- function(world_data) {
  country_names <- world_data$name_en
  
  mapping <- data.frame(
    country = country_names,
    region = case_when(
      # 高收入地区
      country_names %in% c("United States of America", "Canada", "Germany", "France", "United Kingdom", 
                          "Italy", "Spain", "Netherlands", "Belgium", "Switzerland", 
                          "Austria", "Sweden", "Norway", "Denmark", "Finland", "Ireland", 
                          "Portugal", "Greece", "Australia", "New Zealand", "Japan", 
                          "South Korea", "Israel", "Luxembourg", "Iceland", "Czech Republic",
                          "Slovenia", "Estonia", "Latvia", "Lithuania", "Slovakia", "Poland",
                          "Hungary", "Croatia") ~ "高收入",
      
      # 撒哈拉以南非洲
      country_names %in% c("Nigeria", "Ethiopia", "South Africa", "Kenya", "Uganda", "Tanzania", 
                          "Ghana", "Mozambique", "Madagascar", "Cameroon", "Angola", "Niger", 
                          "Burkina Faso", "Mali", "Malawi", "Zambia", "Somalia", "Senegal", 
                          "Chad", "Zimbabwe", "Guinea", "Rwanda", "Benin", "Burundi", 
                          "South Sudan", "Togo", "Sierra Leone", "Liberia", "Botswana", 
                          "Namibia", "Gabon", "Lesotho", "Gambia", "Guinea-Bissau", 
                          "Equatorial Guinea", "Mauritius", "Eswatini", "Djibouti", 
                          "Comoros", "Cape Verde", "São Tomé and Príncipe", "Seychelles",
                          "Central African Republic", "Republic of the Congo", 
                          "Democratic Republic of the Congo", "Ivory Coast") ~ "撒哈拉以南非洲",
      
      # 东南亚、东亚和大洋洲
      country_names %in% c("China", "Indonesia", "Philippines", "Vietnam", "Thailand", 
                          "Myanmar", "Malaysia", "Cambodia", "Laos", "Singapore", 
                          "Brunei", "East Timor", "Papua New Guinea", "Fiji", 
                          "Solomon Islands", "Vanuatu", "Samoa", "Tonga", "Kiribati", 
                          "Tuvalu", "Nauru", "Palau", "Marshall Islands", 
                          "Federated States of Micronesia", "Mongolia", "North Korea") ~ "东南亚、东亚和大洋洲",
      
      # 南亚
      country_names %in% c("India", "Pakistan", "Bangladesh", "Afghanistan", "Nepal", 
                          "Sri Lanka", "Bhutan", "Maldives") ~ "南亚",
      
      # 中欧、东欧和中亚
      country_names %in% c("Russia", "Ukraine", "Belarus", "Moldova", "Romania", "Bulgaria", 
                          "Serbia", "Bosnia and Herzegovina", "Montenegro", "Albania", 
                          "North Macedonia", "Kosovo", "Kazakhstan", "Uzbekistan", 
                          "Turkmenistan", "Kyrgyzstan", "Tajikistan", "Armenia", 
                          "Azerbaijan", "Georgia") ~ "中欧、东欧和中亚",
      
      # 拉丁美洲和加勒比海
      country_names %in% c("Brazil", "Mexico", "Argentina", "Colombia", "Peru", "Venezuela", 
                          "Chile", "Ecuador", "Guatemala", "Cuba", "Bolivia", "Haiti", 
                          "Dominican Republic", "Honduras", "Paraguay", "Nicaragua", 
                          "Costa Rica", "Panama", "Uruguay", "Jamaica", "Trinidad and Tobago", 
                          "Bahamas", "Belize", "Barbados", "Saint Lucia", "Grenada", 
                          "Saint Vincent and the Grenadines", "Antigua and Barbuda", 
                          "Dominica", "Saint Kitts and Nevis", "Suriname", "Guyana") ~ "拉丁美洲和加勒比海",
      
      # 北非和中东
      country_names %in% c("Egypt", "Iran", "Turkey", "Iraq", "Saudi Arabia", "Yemen", 
                          "Syria", "Jordan", "Lebanon", "Libya", "Tunisia", "Algeria", 
                          "Morocco", "Sudan", "Oman", "Kuwait", "United Arab Emirates", 
                          "Qatar", "Bahrain", "Cyprus", "Malta") ~ "北非和中东",
      
      TRUE ~ "其他"
    ),
    stringsAsFactors = FALSE
  )
  
  return(mapping)
}

# 创建映射
country_mapping <- create_country_mapping(world)

# 合并地图数据和AAPC数据
world_with_aapc <- world %>%
  left_join(country_mapping, by = c("name_en" = "country")) %>%
  left_join(region_aapc, by = c("region" = "地区"))

cat("地图数据合并完成\n")

# 创建颜色分级
create_color_breaks <- function(aapc_values) {
  # 移除NA值
  valid_values <- aapc_values[!is.na(aapc_values)]
  
  if(length(valid_values) == 0) {
    return(list(breaks = c(-2, -1, 0), colors = c("#2166AC", "#F7F7F7", "#B2182B")))
  }
  
  # 创建10个分级
  min_val <- min(valid_values)
  max_val <- max(valid_values)
  
  breaks <- seq(min_val, max_val, length.out = 11)
  
  # 创建颜色渐变（蓝色到红色）
  colors <- colorRampPalette(c("#2166AC", "#4393C3", "#92C5DE", "#D1E5F0", "#F7F7F7",
                              "#FDBF6F", "#FD8D3C", "#E31A1C", "#B10026"))(10)
  
  return(list(breaks = breaks, colors = colors))
}

color_scheme <- create_color_breaks(world_with_aapc$AAPC)

cat("颜色方案创建完成\n")

# 创建主地图
main_map <- ggplot(world_with_aapc) +
  geom_sf(aes(fill = AAPC), color = "white", size = 0.1) +
  scale_fill_gradientn(
    colors = color_scheme$colors,
    values = scales::rescale(color_scheme$breaks),
    name = "AAPC (%)",
    na.value = "grey90",
    guide = guide_colorbar(
      title.position = "top",
      title.hjust = 0.5,
      barwidth = 15,
      barheight = 1
    )
  ) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 10),
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 12, hjust = 0.5)
  ) +
  labs(
    title = "脑卒中65岁以上人群发病率变化趋势 (1990-2021)",
    subtitle = "年均百分比变化 (AAPC) 按地区分布"
  )

# 保存主地图
ggsave("stroke_incidence_updated_main_map.png", main_map, 
       width = 16, height = 10, dpi = 300, bg = "white")

cat("主地图已保存: stroke_incidence_updated_main_map.png\n")

cat("=== 可视化完成 ===\n")
