# 创建蓝-红色带的详细图例说明

library(ggplot2)
library(dplyr)
library(gridExtra)
library(grid)

cat("=== 创建蓝-红色带详细图例说明 ===\n")

# 定义8级颜色和对应的AAPC区间
legend_data <- data.frame(
  level = 1:8,
  color = c(
    "#08306B",  # 深蓝
    "#2171B5",  # 蓝色
    "#4292C6",  # 中蓝
    "#6BAED6",  # 浅蓝
    "#9ECAE1",  # 很浅蓝
    "#FFFFCC",  # 浅黄
    "#FD8D3C",  # 橙色
    "#D94701"   # 深红
  ),
  range_text = c(
    "≤ -2.0%",
    "-2.0% to -1.5%",
    "-1.5% to -1.0%",
    "-1.0% to -0.75%",
    "-0.75% to -0.5%",
    "-0.5% to -0.25%",
    "-0.25% to 0%",
    "> 0%"
  ),
  description = c(
    "快速下降",
    "较快下降",
    "中等下降",
    "缓慢下降",
    "轻微下降",
    "极轻微下降",
    "接近稳定",
    "上升趋势"
  ),
  interpretation = c(
    "预防效果显著",
    "预防效果良好",
    "有一定改善",
    "缓慢改善",
    "轻微改善",
    "变化很小",
    "基本稳定",
    "需要关注"
  ),
  stringsAsFactors = FALSE
)

# 创建颜色条图例
color_bar_plot <- ggplot(legend_data, aes(x = level, y = 1, fill = color)) +
  geom_tile(color = "black", size = 0.5, width = 0.9, height = 0.8) +
  scale_fill_identity() +
  scale_x_continuous(breaks = 1:8, labels = legend_data$range_text, 
                     expand = c(0.02, 0.02)) +
  theme_void() +
  theme(
    axis.text.x = element_text(size = 10, angle = 45, hjust = 1, vjust = 1),
    plot.title = element_text(size = 14, face = "bold", hjust = 0.5),
    plot.margin = margin(10, 10, 10, 10)
  ) +
  labs(title = "AAPC 8级颜色分级标准")

# 创建详细说明表
detail_table_plot <- ggplot() +
  theme_void() +
  theme(plot.margin = margin(10, 10, 10, 10)) +
  coord_cartesian(xlim = c(0, 10), ylim = c(0, 10))

# 添加表格内容
for(i in 1:8) {
  # 颜色方块
  detail_table_plot <- detail_table_plot +
    annotate("rect", xmin = 0.5, xmax = 1.5, ymin = 9.5-i, ymax = 10.5-i, 
             fill = legend_data$color[i], color = "black", size = 0.5)
  
  # AAPC范围
  detail_table_plot <- detail_table_plot +
    annotate("text", x = 2.5, y = 10-i, label = legend_data$range_text[i], 
             size = 4, hjust = 0, fontface = "bold")
  
  # 变化描述
  detail_table_plot <- detail_table_plot +
    annotate("text", x = 4.5, y = 10-i, label = legend_data$description[i], 
             size = 4, hjust = 0)
  
  # 临床意义
  detail_table_plot <- detail_table_plot +
    annotate("text", x = 6.5, y = 10-i, label = legend_data$interpretation[i], 
             size = 4, hjust = 0, color = "darkblue")
}

# 添加表头
detail_table_plot <- detail_table_plot +
  annotate("text", x = 1, y = 9, label = "颜色", size = 5, fontface = "bold") +
  annotate("text", x = 2.5, y = 9, label = "AAPC范围", size = 5, fontface = "bold") +
  annotate("text", x = 4.5, y = 9, label = "变化程度", size = 5, fontface = "bold") +
  annotate("text", x = 6.5, y = 9, label = "临床意义", size = 5, fontface = "bold") +
  
  # 添加标题
  annotate("text", x = 4, y = 9.7, label = "AAPC颜色编码详细说明", 
           size = 6, fontface = "bold", hjust = 0.5)

# 创建概念解释图
concept_plot <- ggplot() +
  theme_void() +
  theme(plot.margin = margin(15, 15, 15, 15)) +
  coord_cartesian(xlim = c(0, 10), ylim = c(0, 8)) +
  
  # 标题
  annotate("text", x = 5, y = 7.5, label = "AAPC (年均百分比变化) 概念解释", 
           size = 7, fontface = "bold", hjust = 0.5) +
  
  # 定义
  annotate("text", x = 0.5, y = 6.8, 
           label = "定义: AAPC表示某指标每年平均变化的百分比", 
           size = 5, hjust = 0, fontface = "bold") +
  
  # 计算方法
  annotate("text", x = 0.5, y = 6.2, 
           label = "计算: 基于1990-2021年32年完整数据的对数线性回归", 
           size = 4.5, hjust = 0) +
  
  # 解释
  annotate("text", x = 0.5, y = 5.6, 
           label = "• 负值 (蓝色): 发病率下降，数值越小下降越快", 
           size = 4.5, hjust = 0, color = "#08306B") +
  
  annotate("text", x = 0.5, y = 5.1, 
           label = "• 零值 (黄色): 发病率保持稳定", 
           size = 4.5, hjust = 0, color = "#FFFFCC") +
  
  annotate("text", x = 0.5, y = 4.6, 
           label = "• 正值 (红色): 发病率上升，数值越大上升越快", 
           size = 4.5, hjust = 0, color = "#D94701") +
  
  # 示例
  annotate("text", x = 0.5, y = 3.8, 
           label = "示例解读:", 
           size = 5, hjust = 0, fontface = "bold") +
  
  annotate("text", x = 0.5, y = 3.3, 
           label = "• AAPC = -2.0%: 每年平均下降2%，32年累计下降约48%", 
           size = 4, hjust = 0, color = "darkgreen") +
  
  annotate("text", x = 0.5, y = 2.9, 
           label = "• AAPC = -1.0%: 每年平均下降1%，32年累计下降约27%", 
           size = 4, hjust = 0, color = "darkgreen") +
  
  annotate("text", x = 0.5, y = 2.5, 
           label = "• AAPC = -0.5%: 每年平均下降0.5%，32年累计下降约15%", 
           size = 4, hjust = 0, color = "darkgreen") +
  
  # 数据来源
  annotate("text", x = 0.5, y = 1.8, 
           label = "数据来源: GBD 2021 | 研究对象: 全球65岁以上人群 | 指标: 脑卒中年龄标准化发病率", 
           size = 4, hjust = 0, color = "grey40", fontface = "italic") +
  
  annotate("text", x = 0.5, y = 1.4, 
           label = "统计方法: 对数线性回归 | 所有趋势均具有统计学显著性 (P < 0.001)", 
           size = 4, hjust = 0, color = "grey40", fontface = "italic")

# 组合所有图
combined_legend_guide <- grid.arrange(
  color_bar_plot,
  detail_table_plot,
  concept_plot,
  heights = c(1, 2, 2.5),
  ncol = 1
)

# 保存详细图例说明
ggsave("stroke_blue_red_detailed_legend_guide.png", 
       combined_legend_guide, 
       width = 12, height = 16, dpi = 300, bg = "white")

cat("详细图例说明已保存: stroke_blue_red_detailed_legend_guide.png\n")

# 创建简化版颜色条（用于其他用途）
simple_color_bar <- ggplot(legend_data, aes(x = level, y = 1)) +
  geom_tile(aes(fill = color), color = "black", size = 0.8, width = 0.95, height = 1) +
  geom_text(aes(label = range_text), y = 0.5, size = 3.5, angle = 45, hjust = 1) +
  geom_text(aes(label = description), y = 1.5, size = 3.5, fontface = "bold") +
  scale_fill_identity() +
  scale_x_continuous(breaks = 1:8, expand = c(0.02, 0.02)) +
  scale_y_continuous(limits = c(0, 2)) +
  theme_void() +
  theme(
    plot.title = element_text(size = 14, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 12, hjust = 0.5),
    plot.margin = margin(15, 15, 15, 15)
  ) +
  labs(
    title = "脑卒中发病率AAPC颜色编码",
    subtitle = "蓝色 = 下降趋势 | 黄色 = 稳定 | 红色 = 上升趋势"
  )

ggsave("stroke_blue_red_simple_color_bar.png", 
       simple_color_bar, 
       width = 14, height = 6, dpi = 300, bg = "white")

cat("简化版颜色条已保存: stroke_blue_red_simple_color_bar.png\n")

cat("\n=== 蓝-红色带图例说明创建完成 ===\n")
cat("生成的文件:\n")
cat("1. stroke_blue_red_detailed_legend_guide.png - 详细图例说明\n")
cat("2. stroke_blue_red_simple_color_bar.png - 简化版颜色条\n")
