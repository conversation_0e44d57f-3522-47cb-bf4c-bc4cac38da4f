# 创建完整的脑卒中发病率地图布局 - 主地图 + 六个局部放大视图

library(ggplot2)
library(dplyr)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(RColorBrewer)
library(gridExtra)
library(grid)
library(scales)

cat("=== 创建完整布局的脑卒中发病率地图 ===\n")

# 读取数据
stroke_data <- read.csv("脑卒中65岁以上人群发病率最终分析表_1990_2021.csv", 
                       stringsAsFactors = FALSE, 
                       fileEncoding = "UTF-8")

region_aapc <- stroke_data %>%
  filter(分类 %in% c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", 
                    "高收入", "中欧、东欧和中亚", "拉丁美洲和加勒比海", 
                    "北非和中东")) %>%
  select(地区 = 分类, AAPC = `AAPC...`)

# 获取世界地图
world <- ne_countries(scale = "medium", returnclass = "sf")

# 创建国家映射
create_detailed_country_mapping <- function(world_data) {
  country_names <- world_data$name_en
  
  mapping <- data.frame(
    country = country_names,
    region = case_when(
      country_names %in% c("United States of America", "Canada", "Germany", "France", "United Kingdom", 
                          "Italy", "Spain", "Netherlands", "Belgium", "Switzerland", 
                          "Austria", "Sweden", "Norway", "Denmark", "Finland", "Ireland", 
                          "Portugal", "Greece", "Australia", "New Zealand", "Japan", 
                          "South Korea", "Israel", "Luxembourg", "Iceland", "Czech Republic",
                          "Slovenia", "Estonia", "Latvia", "Lithuania", "Slovakia", "Poland",
                          "Hungary", "Croatia", "Cyprus", "Malta") ~ "高收入",
      
      country_names %in% c("Nigeria", "Ethiopia", "South Africa", "Kenya", "Uganda", "Tanzania", 
                          "Ghana", "Mozambique", "Madagascar", "Cameroon", "Angola", "Niger", 
                          "Burkina Faso", "Mali", "Malawi", "Zambia", "Somalia", "Senegal", 
                          "Chad", "Zimbabwe", "Guinea", "Rwanda", "Benin", "Burundi", 
                          "South Sudan", "Togo", "Sierra Leone", "Liberia", "Botswana", 
                          "Namibia", "Gabon", "Lesotho", "Gambia", "Guinea-Bissau", 
                          "Equatorial Guinea", "Mauritius", "Eswatini", "Djibouti", 
                          "Comoros", "Cape Verde", "São Tomé and Príncipe", "Seychelles",
                          "Central African Republic", "Republic of the Congo", 
                          "Democratic Republic of the Congo", "Ivory Coast", "Mauritania") ~ "撒哈拉以南非洲",
      
      country_names %in% c("China", "Indonesia", "Philippines", "Vietnam", "Thailand", 
                          "Myanmar", "Malaysia", "Cambodia", "Laos", "Singapore", 
                          "Brunei", "East Timor", "Papua New Guinea", "Fiji", 
                          "Solomon Islands", "Vanuatu", "Samoa", "Tonga", "Kiribati", 
                          "Tuvalu", "Nauru", "Palau", "Marshall Islands", 
                          "Federated States of Micronesia", "Mongolia", "North Korea") ~ "东南亚、东亚和大洋洲",
      
      country_names %in% c("India", "Pakistan", "Bangladesh", "Afghanistan", "Nepal", 
                          "Sri Lanka", "Bhutan", "Maldives") ~ "南亚",
      
      country_names %in% c("Russia", "Ukraine", "Belarus", "Moldova", "Romania", "Bulgaria", 
                          "Serbia", "Bosnia and Herzegovina", "Montenegro", "Albania", 
                          "North Macedonia", "Kosovo", "Kazakhstan", "Uzbekistan", 
                          "Turkmenistan", "Kyrgyzstan", "Tajikistan", "Armenia", 
                          "Azerbaijan", "Georgia") ~ "中欧、东欧和中亚",
      
      country_names %in% c("Brazil", "Mexico", "Argentina", "Colombia", "Peru", "Venezuela", 
                          "Chile", "Ecuador", "Guatemala", "Cuba", "Bolivia", "Haiti", 
                          "Dominican Republic", "Honduras", "Paraguay", "Nicaragua", 
                          "Costa Rica", "Panama", "Uruguay", "Jamaica", "Trinidad and Tobago", 
                          "Bahamas", "Belize", "Barbados", "Saint Lucia", "Grenada", 
                          "Saint Vincent and the Grenadines", "Antigua and Barbuda", 
                          "Dominica", "Saint Kitts and Nevis", "Suriname", "Guyana",
                          "El Salvador") ~ "拉丁美洲和加勒比海",
      
      country_names %in% c("Egypt", "Iran", "Turkey", "Iraq", "Saudi Arabia", "Yemen", 
                          "Syria", "Jordan", "Lebanon", "Libya", "Tunisia", "Algeria", 
                          "Morocco", "Sudan", "Oman", "Kuwait", "United Arab Emirates", 
                          "Qatar", "Bahrain") ~ "北非和中东",
      
      TRUE ~ "其他"
    ),
    stringsAsFactors = FALSE
  )
  
  return(mapping)
}

country_mapping <- create_detailed_country_mapping(world)
world_with_aapc <- world %>%
  left_join(country_mapping, by = c("name_en" = "country")) %>%
  left_join(region_aapc, by = c("region" = "地区"))

# 创建颜色方案
aapc_range <- range(region_aapc$AAPC, na.rm = TRUE)
breaks <- seq(aapc_range[1], aapc_range[2], length.out = 11)
colors <- colorRampPalette(c("#2166AC", "#4393C3", "#92C5DE", "#D1E5F0", "#F7F7F7",
                            "#FDBF6F", "#FD8D3C", "#E31A1C", "#B10026"))(10)

# 创建主地图（较小版本，为局部地图留出空间）
main_map <- ggplot(world_with_aapc) +
  geom_sf(aes(fill = AAPC), color = "white", size = 0.1) +
  scale_fill_gradientn(
    colors = colors,
    values = scales::rescale(breaks),
    name = "AAPC (%)",
    na.value = "grey90",
    breaks = pretty(aapc_range, n = 5),
    guide = guide_colorbar(
      title.position = "top",
      title.hjust = 0.5,
      barwidth = 12,
      barheight = 0.8
    )
  ) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 10, face = "bold"),
    legend.text = element_text(size = 8),
    plot.title = element_text(size = 14, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 10, hjust = 0.5),
    plot.margin = margin(5, 5, 5, 5)
  ) +
  labs(
    title = "脑卒中65岁以上人群发病率变化趋势 (1990-2021)",
    subtitle = "年均百分比变化 (AAPC) 按地区分布"
  )

# 定义六个局部放大区域
regions <- list(
  list(name = "Caribbean and\nCentral America", xlim = c(-95, -55), ylim = c(5, 30)),
  list(name = "Persian Gulf", xlim = c(45, 60), ylim = c(22, 32)),
  list(name = "Balkan Peninsula", xlim = c(12, 30), ylim = c(38, 48)),
  list(name = "South East Asia", xlim = c(90, 145), ylim = c(-15, 25)),
  list(name = "West Africa &\nEastern Mediterranean", xlim = c(-20, 45), ylim = c(10, 40)),
  list(name = "Northern Europe", xlim = c(-10, 35), ylim = c(50, 72))
)

# 创建局部放大地图
create_zoom_map <- function(region_info) {
  ggplot(world_with_aapc) +
    geom_sf(aes(fill = AAPC), color = "white", size = 0.15) +
    scale_fill_gradientn(
      colors = colors,
      values = scales::rescale(breaks),
      na.value = "grey90",
      guide = "none"
    ) +
    coord_sf(
      xlim = region_info$xlim,
      ylim = region_info$ylim,
      expand = FALSE
    ) +
    theme_void() +
    theme(
      plot.title = element_text(size = 8, face = "bold", hjust = 0.5),
      plot.margin = margin(2, 2, 2, 2),
      panel.border = element_rect(color = "black", fill = NA, size = 0.5)
    ) +
    labs(title = region_info$name)
}

zoom_maps <- lapply(regions, create_zoom_map)

cat("地图创建完成\n")

# 使用grid布局创建完整的图像
create_complete_layout <- function() {
  # 创建新的绘图设备
  png("stroke_incidence_complete_layout_with_zoom_1990_2021.png", 
      width = 20, height = 16, units = "in", res = 300, bg = "white")
  
  # 设置布局
  layout_matrix <- matrix(c(
    1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1,
    2, 2, 3, 3, 4, 4,
    5, 5, 6, 6, 7, 7
  ), nrow = 5, byrow = TRUE)
  
  layout(layout_matrix)
  
  # 绘制主地图
  print(main_map)
  
  # 绘制六个局部地图
  for(i in 1:6) {
    print(zoom_maps[[i]])
  }
  
  dev.off()
}

# 使用gridExtra创建布局
complete_layout <- grid.arrange(
  main_map,
  arrangeGrob(
    zoom_maps[[1]], zoom_maps[[2]], zoom_maps[[3]],
    zoom_maps[[4]], zoom_maps[[5]], zoom_maps[[6]],
    ncol = 3, nrow = 2
  ),
  heights = c(3, 2),
  ncol = 1
)

# 保存完整布局
ggsave("stroke_incidence_complete_layout_with_zoom_1990_2021.png", 
       complete_layout, 
       width = 20, height = 16, dpi = 300, bg = "white")

cat("完整布局地图已保存: stroke_incidence_complete_layout_with_zoom_1990_2021.png\n")

# 添加说明文本
cat("\n=== 地图说明 ===\n")
cat("主地图显示全球脑卒中65岁以上人群发病率AAPC (1990-2021)\n")
cat("六个局部放大视图展示重点区域的详细情况:\n")
cat("1. Caribbean and Central America (加勒比海和中美洲)\n")
cat("2. Persian Gulf (波斯湾地区)\n")
cat("3. Balkan Peninsula (巴尔干半岛)\n")
cat("4. South East Asia (东南亚)\n")
cat("5. West Africa & Eastern Mediterranean (西非和东地中海)\n")
cat("6. Northern Europe (北欧)\n")
cat("\n基于GBD 2021数据库，使用1990-2021年完整32年时间序列数据\n")

cat("=== 完整布局地图创建完成 ===\n")
