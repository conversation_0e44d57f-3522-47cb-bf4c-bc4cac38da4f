# 脑卒中死亡率AAPC均匀色彩可视化 - 35岁以上人群 (1990-2021)
# 按国家数量均匀分布颜色，提高区分度

library(readr)
library(dplyr)
library(ggplot2)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(RColorBrewer)
library(viridis)
library(scales)
library(gridExtra)
library(grid)

# 读取35岁以上人群的AAPC结果
aapc_data <- read_csv("stroke_mortality_35plus_aapc_results_1990_2021.csv", show_col_types = FALSE)

cat("=== 35岁以上人群脑卒中死亡率AAPC数据概况 ===\n")
cat(paste("总国家数量:", nrow(aapc_data), "\n"))
cat(paste("AAPC范围:", round(min(aapc_data$aapc, na.rm = TRUE), 3), 
          "到", round(max(aapc_data$aapc, na.rm = TRUE), 3), "\n"))
cat(paste("中位数:", round(median(aapc_data$aapc, na.rm = TRUE), 3), "\n"))

# 获取世界地图数据
world <- ne_countries(scale = "medium", returnclass = "sf")

# 创建完整的中英文国家名称映射表（参考stroke_blue_to_red_visualization.R）
create_country_name_mapping <- function() {
  mapping <- data.frame(
    chinese_name = c(
      "中国", "美利坚合众国", "印度", "俄罗斯", "日本", "德国", "英国", "法国", "意大利", "巴西",
      "加拿大", "大韩民国", "西班牙", "澳大利亚", "墨西哥", "印度尼西亚", "荷兰", "沙特阿拉伯",
      "土耳其", "瑞士", "比利时", "爱尔兰", "以色列", "奥地利", "尼日利亚", "阿根廷",
      "南非", "埃及", "孟加拉国", "越南", "智利", "芬兰", "罗马尼亚", "捷克共和国",
      "新西兰", "秘鲁", "希腊", "葡萄牙", "伊拉克", "阿尔及利亚", "卡塔尔", "哈萨克斯坦",
      "匈牙利", "科威特", "摩洛哥", "斯洛伐克", "厄瓜多尔", "古巴", "阿拉伯联合酋长国", "白俄罗斯",
      "阿塞拜疆", "斯里兰卡", "缅甸", "乌兹别克斯坦", "多米尼加共和国", "乌拉圭", "哥斯达黎加",
      "斯洛文尼亚", "立陶宛", "巴拿马", "保加利亚", "克罗地亚", "约旦", "塞尔维亚",
      "黎巴嫩", "新加坡", "挪威", "丹麦", "瑞典", "阿富汗", "乌干达", "乌克兰",
      "阿尔巴尼亚", "巴林岛", "亚美尼亚", "蒙古", "牙买加", "纳米比亚", "博茨瓦纳",
      "加蓬", "莱索托", "毛里求斯", "斯威士兰", "冰岛", "马尔他", "塞浦路斯",
      "文莱达鲁萨兰国", "巴巴多斯", "马尔代夫", "卢森堡公国", "不丹", "东帝汶民主共和国",
      "中非共和国", "乍得", "也门", "伊朗伊斯兰共和国", "伯利兹城", "佛得角", "津巴布韦",
      "洪都拉斯", "莫桑比克", "黑山共和国", "利比亚", "肯尼亚", "台湾", "爱沙尼亚",
      "拉脱维亚", "波兰", "阿曼", "巴基斯坦", "尼泊尔", "马来西亚", "泰国", "菲律宾",
      "老挝人民民主共和国", "柬埔寨", "巴布亚新几内亚", "斐济", "所罗门群岛", "瓦努阿图", "萨摩亚",
      "汤加", "基里巴斯", "图瓦卢", "瑙鲁共和国", "帕劳共和国", "马绍尔群岛", "密克罗尼西亚联邦",
      "委内瑞拉玻利瓦尔共和国", "哥伦比亚", "玻利维亚国", "巴拉圭", "苏里南", "圭亚那", "海地",
      "格鲁吉亚", "摩尔多瓦共和国", "塔吉克斯坦", "吉尔吉斯斯坦", "土库曼斯坦", "埃塞俄比亚", "加纳",
      "冈比亚", "几内亚", "几内亚比绍", "利比里亚", "塞拉利昂", "塞内加尔", "马里", "布基纳法索",
      "尼日尔", "贝宁", "多哥", "科特廸亚", "刚果", "刚果民主共和国", "安哥拉", "赞比亚",
      "马拉维", "坦桑尼亚联合共和国", "卢旺达", "布隆迪", "乌干达", "南苏丹", "苏丹", "索马里",
      "吉布提", "厄立特里亚国", "突尼斯", "毛里塔尼亚", "马达加斯加", "科摩罗", "塞舌尔",
      "朝鲜民主主义人民共和国", "格林纳达", "安提瓜和巴布达", "圣基茨和尼维斯联邦", "圣卢西亚岛",
      "圣文森特和格林纳丁斯", "多米尼加岛", "特立尼达拉岛和多巴哥", "巴哈马", "萨尔瓦多", "危地马拉",
      "尼加拉瓜", "波斯尼亚和黑塞哥维那", "北马其顿", "阿拉伯叙利亚共和国", "巴勒斯坦", "赤道几内亚",
      "圣多美和普林西比民主共和国", "安道尔共和国", "摩纳哥公国", "圣马力诺共和国"
    ),
    english_name = c(
      "China", "United States of America", "India", "Russia", "Japan", "Germany", "United Kingdom", "France", "Italy", "Brazil",
      "Canada", "South Korea", "Spain", "Australia", "Mexico", "Indonesia", "Netherlands", "Saudi Arabia",
      "Turkey", "Switzerland", "Belgium", "Ireland", "Israel", "Austria", "Nigeria", "Argentina",
      "South Africa", "Egypt", "Bangladesh", "Vietnam", "Chile", "Finland", "Romania", "Czech Republic",
      "New Zealand", "Peru", "Greece", "Portugal", "Iraq", "Algeria", "Qatar", "Kazakhstan",
      "Hungary", "Kuwait", "Morocco", "Slovakia", "Ecuador", "Cuba", "United Arab Emirates", "Belarus",
      "Azerbaijan", "Sri Lanka", "Myanmar", "Uzbekistan", "Dominican Republic", "Uruguay", "Costa Rica",
      "Slovenia", "Lithuania", "Panama", "Bulgaria", "Croatia", "Jordan", "Serbia",
      "Lebanon", "Singapore", "Norway", "Denmark", "Sweden", "Afghanistan", "Uganda", "Ukraine",
      "Albania", "Bahrain", "Armenia", "Mongolia", "Jamaica", "Namibia", "Botswana",
      "Gabon", "Lesotho", "Mauritius", "Eswatini", "Iceland", "Malta", "Cyprus",
      "Brunei", "Barbados", "Maldives", "Luxembourg", "Bhutan", "East Timor",
      "Central African Republic", "Chad", "Yemen", "Iran", "Belize", "Cape Verde", "Zimbabwe",
      "Honduras", "Mozambique", "Montenegro", "Libya", "Kenya", "Taiwan", "Estonia",
      "Latvia", "Poland", "Oman", "Pakistan", "Nepal", "Malaysia", "Thailand", "Philippines",
      "Laos", "Cambodia", "Papua New Guinea", "Fiji", "Solomon Islands", "Vanuatu", "Samoa",
      "Tonga", "Kiribati", "Tuvalu", "Nauru", "Palau", "Marshall Islands", "Micronesia",
      "Venezuela", "Colombia", "Bolivia", "Paraguay", "Suriname", "Guyana", "Haiti",
      "Georgia", "Moldova", "Tajikistan", "Kyrgyzstan", "Turkmenistan", "Ethiopia", "Ghana",
      "Gambia", "Guinea", "Guinea-Bissau", "Liberia", "Sierra Leone", "Senegal", "Mali", "Burkina Faso",
      "Niger", "Benin", "Togo", "Ivory Coast", "Republic of the Congo", "Democratic Republic of the Congo", "Angola", "Zambia",
      "Malawi", "Tanzania", "Rwanda", "Burundi", "Uganda", "South Sudan", "Sudan", "Somalia",
      "Djibouti", "Eritrea", "Tunisia", "Mauritania", "Madagascar", "Comoros", "Seychelles",
      "North Korea", "Grenada", "Antigua and Barbuda", "Saint Kitts and Nevis", "Saint Lucia",
      "Saint Vincent and the Grenadines", "Dominica", "Trinidad and Tobago", "Bahamas", "El Salvador", "Guatemala",
      "Nicaragua", "Bosnia and Herzegovina", "North Macedonia", "Syria", "Palestine", "Equatorial Guinea",
      "São Tomé and Príncipe", "Andorra", "Monaco", "San Marino"
    ),
    stringsAsFactors = FALSE
  )
  return(mapping)
}

# 应用国家名称映射
name_mapping <- create_country_name_mapping()
aapc_data$location_name_en <- aapc_data$location_name

# 替换已知的中文名称为英文名称
for(i in 1:nrow(name_mapping)) {
  aapc_data$location_name_en[aapc_data$location_name == name_mapping$chinese_name[i]] <- name_mapping$english_name[i]
}

# 处理台湾数据：实现一个中国原则的可视化表达
taiwan_data <- aapc_data[aapc_data$location_name == "台湾", ]
china_data <- aapc_data[aapc_data$location_name == "中国", ]

if(nrow(taiwan_data) > 0 && nrow(china_data) > 0) {
  # 计算中国大陆和台湾的加权平均AAPC（这里简单使用算术平均）
  combined_aapc <- mean(c(china_data$aapc, taiwan_data$aapc), na.rm = TRUE)

  # 更新中国的AAPC值
  aapc_data$aapc[aapc_data$location_name == "中国"] <- combined_aapc
  aapc_data$location_name_en[aapc_data$location_name == "中国"] <- "China"

  # 保留台湾条目但使用与中国相同的AAPC值，实现统一颜色显示
  aapc_data$aapc[aapc_data$location_name == "台湾"] <- combined_aapc
  aapc_data$location_name_en[aapc_data$location_name == "台湾"] <- "Taiwan"

  cat("一个中国原则实施：中国大陆和台湾地区使用统一AAPC:", round(combined_aapc, 2), "%\n")
  cat("地图上中国大陆和台湾地区将显示相同颜色\n")
} else {
  cat("未找到台湾或中国数据进行统一着色\n")
}

cat("映射后的国家数量:", sum(aapc_data$location_name_en != aapc_data$location_name), "\n")

# 检查国家级别AAPC数据范围
aapc_range <- range(aapc_data$aapc, na.rm = TRUE)
cat("国家级别AAPC范围:", round(aapc_range[1], 2), "到", round(aapc_range[2], 2), "\n")

# 基于实际国家数据重新设计10级色阶
# 使用分位数来确保每个颜色级别都有数据
quantiles <- quantile(aapc_data$aapc, probs = seq(0, 1, 0.1), na.rm = TRUE)
breaks_10_level <- as.numeric(quantiles)
cat("10级分位数断点:", paste(round(breaks_10_level, 2), collapse = ", "), "\n")

# 创建AAPC分类（基于分位数）
aapc_data$aapc_category <- cut(aapc_data$aapc,
                               breaks = breaks_10_level,
                               labels = paste0("Q", 1:10),
                               include.lowest = TRUE)

# 创建更详细的标签
decile_labels <- c()
for(i in 1:9) {
  decile_labels[i] <- paste0("Q", i, ": ", round(breaks_10_level[i], 2), " to ", round(breaks_10_level[i+1], 2), "%")
}
decile_labels[10] <- paste0("Q10: ", round(breaks_10_level[10], 2), " to ", round(breaks_10_level[11], 2), "%")

# 更新分类标签
levels(aapc_data$aapc_category) <- decile_labels

# 创建10级颜色方案（蓝到红渐变）
colors_10_level <- c(
  "#08306B",  # 深蓝 (最低AAPC)
  "#2171B5",  # 蓝色
  "#4292C6",  # 中蓝
  "#6BAED6",  # 浅蓝
  "#9ECAE1",  # 很浅蓝
  "#FFFFCC",  # 浅黄
  "#A1DAB4",  # 浅绿
  "#41B6C4",  # 青色
  "#FD8D3C",  # 橙色
  "#E31A1C"   # 红色 (最高AAPC)
)

cat("创建10级分位数颜色分类\n")

# 检查world数据的字段名称
cat("检查世界地图数据字段...\n")
name_fields <- colnames(world)[grep("name", colnames(world), ignore.case = TRUE)]
cat("可用的名称字段:", paste(name_fields[1:5], collapse = ", "), "...\n")

# 检查中国在world数据中的名称
china_entries <- world[grep("China|china", world$name, ignore.case = TRUE), ]
cat("世界地图中的中国条目:", china_entries$name, "\n")

# 合并世界地图与国家AAPC数据（使用正确的字段名称）
world_data <- world %>%
  left_join(aapc_data, by = c("name" = "location_name_en"))

# 检查匹配情况
matched_count <- sum(!is.na(world_data$aapc))
total_count <- nrow(world_data)
cat("成功匹配的国家:", matched_count, "/", total_count, "\n")

cat("正在创建10级分位数颜色地图可视化...\n")

# 创建主地图（10级色阶）
main_map <- ggplot(world_data) +
  geom_sf(aes(fill = aapc_category), color = "white", size = 0.1) +
  scale_fill_manual(
    values = colors_10_level,
    name = "AAPC (% per year)",
    na.value = "grey85",
    drop = FALSE
  ) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 14, face = "bold"),
    legend.text = element_text(size = 9),
    legend.key.size = unit(0.8, "cm"),
    legend.key.width = unit(1.2, "cm"),
    plot.margin = margin(5, 5, 10, 5, "mm"),
    legend.margin = margin(t = 10),
    plot.title = element_text(size = 18, face = "bold", hjust = 0.5, margin = margin(b = 8)),
    plot.subtitle = element_text(size = 14, hjust = 0.5, margin = margin(b = 12)),
    plot.caption = element_text(size = 11, hjust = 0.5, margin = margin(t = 20),
                               lineheight = 1.2)
  ) +
  guides(fill = guide_legend(nrow = 2, byrow = TRUE,
                            title.position = "top", title.hjust = 0.5)) +
  labs(
    title = "Global Stroke Mortality Average Annual Percentage Change (1990-2021)",
    subtitle = "Population Aged ≥35 Years | 10-Level Quantile Classification | Blue=Decline, Red=Increase",
    caption = paste(
      "Study Population: Global population aged ≥35 years | Indicator: Stroke Mortality | Period: 1990-2021",
      paste0("AAPC Range: ", round(min(aapc_data$aapc, na.rm = TRUE), 2), "% to ",
             round(max(aapc_data$aapc, na.rm = TRUE), 2), "% | ", nrow(aapc_data), " countries | Data Source: GBD 2021"),
      "Note: Taiwan and Mainland China display unified color following One-China principle",
      sep = "\n"
    )
  )

# 定义六个局部放大区域
regions <- list(
  list(name = "Caribbean and\nCentral America", xlim = c(-95, -55), ylim = c(5, 30)),
  list(name = "Persian Gulf", xlim = c(45, 60), ylim = c(22, 32)),
  list(name = "Balkan Peninsula", xlim = c(12, 30), ylim = c(38, 48)),
  list(name = "South East Asia", xlim = c(90, 145), ylim = c(-15, 25)),
  list(name = "West Africa &\nEastern Mediterranean", xlim = c(-20, 45), ylim = c(10, 40)),
  list(name = "Northern Europe", xlim = c(-10, 35), ylim = c(50, 72))
)

# 创建局部放大地图（10级色阶）
create_zoom_map <- function(region_info) {
  ggplot(world_data) +
    geom_sf(aes(fill = aapc_category), color = "white", size = 0.15) +
    scale_fill_manual(
      values = colors_10_level,
      na.value = "grey85",
      guide = "none"
    ) +
    coord_sf(
      xlim = region_info$xlim,
      ylim = region_info$ylim,
      expand = FALSE
    ) +
    theme_void() +
    theme(
      plot.title = element_text(size = 9, face = "bold", hjust = 0.5),
      plot.margin = margin(2, 2, 2, 2),
      panel.border = element_rect(color = "black", fill = NA, linewidth = 0.8)
    ) +
    labs(title = region_info$name)
}

zoom_maps <- lapply(regions, create_zoom_map)

cat("局部放大地图创建完成\n")

# 保存平衡色阶主地图
ggsave("stroke_mortality_35plus_uniform_colors_main_map_1990_2021.png",
       main_map,
       width = 20, height = 14, dpi = 300, bg = "white")

cat("平衡色阶主地图已保存\n")

# 创建完整布局（主地图 + 局部放大视图，10级色阶）
main_map_for_layout <- ggplot(world_data) +
  geom_sf(aes(fill = aapc_category), color = "white", size = 0.1) +
  scale_fill_manual(
    values = colors_10_level,
    name = "AAPC (% per year)",
    na.value = "grey85",
    drop = FALSE
  ) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 14, face = "bold"),
    legend.text = element_text(size = 9),
    legend.key.size = unit(0.8, "cm"),
    legend.key.width = unit(1.2, "cm"),
    plot.margin = margin(5, 5, 10, 5, "mm"),
    legend.margin = margin(t = 10),
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 12, hjust = 0.5)
  ) +
  guides(fill = guide_legend(nrow = 2, byrow = TRUE,
                            title.position = "top", title.hjust = 0.5)) +
  labs(
    title = "Global Stroke Mortality Average Annual Percentage Change (1990-2021)",
    subtitle = "Country-Level Quantile Classification | Blue=Decline, Red=Increase | 6 Regional Zoom Views"
  )

# 创建完整布局
complete_layout <- grid.arrange(
  main_map_for_layout,
  arrangeGrob(
    zoom_maps[[1]], zoom_maps[[2]], zoom_maps[[3]],
    zoom_maps[[4]], zoom_maps[[5]], zoom_maps[[6]],
    ncol = 3, nrow = 2
  ),
  heights = c(3, 2),
  ncol = 1
)

# 保存完整布局
ggsave("stroke_mortality_35plus_uniform_colors_complete_layout_1990_2021.png",
       complete_layout,
       width = 24, height = 20, dpi = 300, bg = "white")

cat("平衡色阶完整布局已保存\n")

cat("\n=== Country-Level Stroke Mortality AAPC Map Creation Completed ===\n")
cat("Key Features:\n")
cat("1. Country-level data:", nrow(aapc_data), "countries with AAPC data\n")
cat("2. 10-level quantile classification: Equal number of countries per color level\n")
cat("3. AAPC range:", round(min(aapc_data$aapc, na.rm = TRUE), 2), "% to",
    round(max(aapc_data$aapc, na.rm = TRUE), 2), "%\n")
cat("4. Blue = Rapid mortality decline, Red = mortality increase\n")
cat("5. Includes six regional zoom-in views\n")
cat("6. Based on GBD 2021 database, population aged ≥35 years stroke mortality\n")
cat("7. One China Principle: Taiwan and Mainland China display unified color\n")

cat("\nGenerated Files:\n")
cat("• stroke_mortality_35plus_uniform_colors_main_map_1990_2021.png (Main Map)\n")
cat("• stroke_mortality_35plus_uniform_colors_complete_layout_1990_2021.png (Complete Layout)\n")

# Output AAPC Data Summary
cat("\n=== Stroke Mortality Country-Level AAPC Data Summary ===\n")
cat("Total countries:", nrow(aapc_data), "\n")
cat("AAPC range:", round(min(aapc_data$aapc, na.rm = TRUE), 2), "% to",
    round(max(aapc_data$aapc, na.rm = TRUE), 2), "%\n")

# Show top 10 and bottom 10 countries
top_countries <- aapc_data[order(aapc_data$aapc), ]
cat("\nTop 10 countries with fastest decline:\n")
for(i in 1:min(10, nrow(top_countries))) {
  cat(sprintf("%s: %+.2f%%\n", top_countries$location_name_en[i], top_countries$aapc[i]))
}

cat("\nTop 10 countries with slowest decline/increase:\n")
bottom_countries <- aapc_data[order(-aapc_data$aapc), ]
for(i in 1:min(10, nrow(bottom_countries))) {
  cat(sprintf("%s: %+.2f%%\n", bottom_countries$location_name_en[i], bottom_countries$aapc[i]))
}
