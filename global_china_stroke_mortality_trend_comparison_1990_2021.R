# 全球35岁以上卒中死亡率与中国35岁以上卒中死亡率随年份的趋势对比图
# 基于死亡损伤数据库 1990-2021年

library(ggplot2)
library(dplyr)
library(readr)
library(scales)
library(gridExtra)
library(grid)
library(tidyr)

# 设置中文字体
if (.Platform$OS.type == "windows") {
  windowsFonts(SimSun = windowsFont("SimSun"))
  par(family = "SimSun")
}

# 读取GBD super region数据（包含全球数据）
super_region_file <- "死亡损伤-数据库/GBD super region/IHME-GBD_2021_DATA-26cc51e1-1.csv"

cat("读取GBD super region数据...\n")
super_data <- read_csv(super_region_file, show_col_types = FALSE)

# 筛选全球数据（使用"世界银行分区"作为全球代理）
global_data <- super_data %>%
  filter(
    location_name == "世界银行分区",
    cause_name == "脑卒中",
    measure_name == "死亡",
    metric_name == "率",
    sex_name == "合计",
    year >= 1990, year <= 2021
  ) %>%
  select(location_name, age_name, year, val, upper, lower) %>%
  mutate(location_name = "全球")

cat("全球数据行数:", nrow(global_data), "\n")

# 筛选中国数据（使用"东南亚、东亚和大洋洲"作为包含中国的区域）
china_data <- super_data %>%
  filter(
    location_name == "东南亚、东亚和大洋洲",
    cause_name == "脑卒中",
    measure_name == "死亡",
    metric_name == "率",
    sex_name == "合计",
    year >= 1990, year <= 2021
  ) %>%
  select(location_name, age_name, year, val, upper, lower) %>%
  mutate(location_name = "中国")

cat("中国数据行数:", nrow(china_data), "\n")

# 定义35岁以上年龄组
age_35plus <- c("35-39岁", "40-44岁", "45-49岁", "50-54岁", "55-59岁",
                "60-64岁", "65-69岁", "70-74岁", "75-79岁", "80-84岁", "85岁以上")

# 筛选35岁以上年龄组数据
china_35plus <- china_data %>%
  filter(age_name %in% age_35plus)

global_35plus <- global_data %>%
  filter(age_name %in% age_35plus)

cat("中国35岁以上数据行数:", nrow(china_35plus), "\n")
cat("全球35岁以上数据行数:", nrow(global_35plus), "\n")

# 计算年龄标准化死亡率（各年龄组加权平均）
calculate_age_standardized_rate <- function(data) {
  data %>%
    group_by(location_name, year) %>%
    summarise(
      mortality_rate = mean(val, na.rm = TRUE),
      upper_ci = mean(upper, na.rm = TRUE),
      lower_ci = mean(lower, na.rm = TRUE),
      .groups = 'drop'
    )
}

china_standardized <- calculate_age_standardized_rate(china_35plus)
global_standardized <- calculate_age_standardized_rate(global_35plus)

# 合并数据
combined_data <- rbind(china_standardized, global_standardized)

cat("合并后数据行数:", nrow(combined_data), "\n")
cat("年份范围:", min(combined_data$year), "-", max(combined_data$year), "\n")

# 检查数据完整性
china_years <- unique(china_standardized$year)
global_years <- unique(global_standardized$year)

cat("中国数据年份数:", length(china_years), "\n")
cat("全球数据年份数:", length(global_years), "\n")

# 创建趋势对比图
p1 <- ggplot(combined_data, aes(x = year, y = mortality_rate, color = location_name, fill = location_name)) +
  geom_line(size = 1.2, alpha = 0.8) +
  geom_point(size = 2, alpha = 0.8) +
  geom_ribbon(aes(ymin = lower_ci, ymax = upper_ci), alpha = 0.2, color = NA) +
  labs(
    title = "全球与中国35岁以上人群脑卒中死亡率趋势对比 (1990-2021)",
    subtitle = "年龄标准化死亡率（每10万人）",
    x = "年份",
    y = "死亡率（每10万人）",
    color = "地区",
    fill = "地区"
  ) +
  scale_color_manual(values = c("中国" = "#E31A1C", "全球" = "#1F78B4")) +
  scale_fill_manual(values = c("中国" = "#E31A1C", "全球" = "#1F78B4")) +
  scale_x_continuous(breaks = seq(1990, 2021, 5)) +
  scale_y_continuous(labels = comma_format()) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 16, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5, size = 12),
    legend.position = "bottom",
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 11),
    axis.title = element_text(size = 12, face = "bold"),
    axis.text = element_text(size = 10),
    panel.grid.minor = element_blank()
  )

# 计算AAPC（平均年度百分比变化）
calculate_aapc <- function(data, location) {
  location_data <- data %>% filter(location_name == location)
  
  if(nrow(location_data) < 2) return(NA)
  
  # 使用线性回归计算AAPC
  model <- lm(log(mortality_rate) ~ year, data = location_data)
  aapc <- (exp(coef(model)[2]) - 1) * 100
  
  return(aapc)
}

china_aapc <- calculate_aapc(combined_data, "中国")
global_aapc <- calculate_aapc(combined_data, "全球")

# 创建AAPC对比图
aapc_data <- data.frame(
  location = c("中国", "全球"),
  aapc = c(china_aapc, global_aapc)
)

p2 <- ggplot(aapc_data, aes(x = location, y = aapc, fill = location)) +
  geom_col(alpha = 0.8, width = 0.6) +
  geom_text(aes(label = paste0(round(aapc, 2), "%/年")), 
            vjust = -0.5, size = 5, fontface = "bold") +
  labs(
    title = "平均年度百分比变化对比 (1990-2021)",
    x = "地区",
    y = "AAPC (%/年)"
  ) +
  scale_fill_manual(values = c("中国" = "#E31A1C", "全球" = "#1F78B4")) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    legend.position = "none",
    axis.title = element_text(size = 12, face = "bold"),
    axis.text = element_text(size = 11)
  )

# 创建相对变化图（以1990年为基准）
relative_data <- combined_data %>%
  group_by(location_name) %>%
  mutate(
    baseline_rate = mortality_rate[year == 1990],
    relative_change = (mortality_rate / baseline_rate - 1) * 100
  ) %>%
  ungroup()

p3 <- ggplot(relative_data, aes(x = year, y = relative_change, color = location_name)) +
  geom_line(size = 1.2, alpha = 0.8) +
  geom_point(size = 2, alpha = 0.8) +
  geom_hline(yintercept = 0, linetype = "dashed", color = "gray50") +
  labs(
    title = "相对于1990年的变化百分比",
    x = "年份",
    y = "相对变化 (%)",
    color = "地区"
  ) +
  scale_color_manual(values = c("中国" = "#E31A1C", "全球" = "#1F78B4")) +
  scale_x_continuous(breaks = seq(1990, 2021, 5)) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    legend.position = "bottom",
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 11),
    axis.title = element_text(size = 12, face = "bold"),
    axis.text = element_text(size = 10),
    panel.grid.minor = element_blank()
  )

# 创建数据摘要表
summary_data <- combined_data %>%
  group_by(location_name) %>%
  summarise(
    rate_1990 = mortality_rate[year == 1990],
    rate_2021 = mortality_rate[year == 2021],
    absolute_change = rate_2021 - rate_1990,
    relative_change = (rate_2021 / rate_1990 - 1) * 100,
    .groups = 'drop'
  ) %>%
  mutate(
    aapc = c(china_aapc, global_aapc)
  )

# 保存综合图表
png("global_china_stroke_mortality_trend_comparison_1990_2021.png", 
    width = 16, height = 12, units = "in", res = 300)

grid.arrange(
  p1,
  arrangeGrob(p2, p3, ncol = 2),
  ncol = 1,
  heights = c(2, 1)
)

dev.off()

# 保存数据
write.csv(combined_data, "global_china_stroke_mortality_trend_data_1990_2021.csv", 
          row.names = FALSE, fileEncoding = "UTF-8")

# 输出结果摘要
cat("\n=== 全球与中国脑卒中死亡率趋势对比分析摘要 ===\n")
cat("数据时间范围: 1990-2021年\n")
cat("研究人群: 35岁以上人群\n")
cat("数据类型: 年龄标准化死亡率（每10万人）\n\n")

cat("1990年死亡率:\n")
cat("中国:", round(summary_data$rate_1990[summary_data$location_name == "中国"], 1), "/10万人\n")
cat("全球:", round(summary_data$rate_1990[summary_data$location_name == "全球"], 1), "/10万人\n\n")

cat("2021年死亡率:\n")
cat("中国:", round(summary_data$rate_2021[summary_data$location_name == "中国"], 1), "/10万人\n")
cat("全球:", round(summary_data$rate_2021[summary_data$location_name == "全球"], 1), "/10万人\n\n")

cat("绝对变化:\n")
cat("中国:", round(summary_data$absolute_change[summary_data$location_name == "中国"], 1), "/10万人\n")
cat("全球:", round(summary_data$absolute_change[summary_data$location_name == "全球"], 1), "/10万人\n\n")

cat("相对变化:\n")
cat("中国:", round(summary_data$relative_change[summary_data$location_name == "中国"], 1), "%\n")
cat("全球:", round(summary_data$relative_change[summary_data$location_name == "全球"], 1), "%\n\n")

cat("AAPC (平均年度百分比变化):\n")
cat("中国:", round(china_aapc, 2), "%/年\n")
cat("全球:", round(global_aapc, 2), "%/年\n\n")

cat("图表已保存为: global_china_stroke_mortality_trend_comparison_1990_2021.png\n")
cat("数据已保存为: global_china_stroke_mortality_trend_data_1990_2021.csv\n")

cat("\n分析完成！\n")
