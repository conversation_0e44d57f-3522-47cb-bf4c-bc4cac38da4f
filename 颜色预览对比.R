# 脑卒中AAPC地图颜色方案对比预览

library(ggplot2)
library(dplyr)
library(gridExtra)

# 原始色阶（蓝色主导）
original_colors <- c(
  "#08306B",  # 深蓝
  "#1C5A8A",  # 深蓝2
  "#2E7FB5",  # 蓝色
  "#4292C6",  # 中蓝
  "#6BAED6",  # 浅蓝
  "#9ECAE1",  # 很浅蓝
  "#C6DBEF",  # 极浅蓝
  "#DEEBF7",  # 浅蓝白
  "#FEE5D9",  # 浅橙白
  "#FCAE91"   # 浅橙
)

# 新的平衡色阶
balanced_colors <- c(
  "#08306B",  # 深蓝
  "#2171B5",  # 蓝色
  "#4292C6",  # 中蓝
  "#6BAED6",  # 浅蓝
  "#9ECAE1",  # 很浅蓝
  "#FFFFCC",  # 浅黄
  "#A1DAB4",  # 浅绿
  "#41B6C4",  # 青色
  "#FD8D3C",  # 橙色
  "#E31A1C"   # 红色
)

# AAPC值和对应标签
aapc_values <- c(-2.0, -1.8, -1.6, -1.4, -1.2, -1.0, -0.8, -0.6, -0.4, -0.2)
aapc_labels <- c("≤-2.0%", "-1.8%", "-1.6%", "-1.4%", "-1.2%", 
                 "-1.0%", "-0.8%", "-0.6%", "-0.4%", "≥-0.2%")

# 地区示例
region_examples <- c(
  "高收入地区", "", "", "拉丁美洲", "南亚/中欧", 
  "", "北非中东", "", "", "撒哈拉以南非洲"
)

# 创建数据框
color_data <- data.frame(
  level = 1:10,
  aapc = aapc_values,
  labels = aapc_labels,
  regions = region_examples,
  original = original_colors,
  balanced = balanced_colors
)

# 创建原始色阶预览
plot_original <- ggplot(color_data, aes(x = level, y = 1)) +
  geom_tile(aes(fill = original), color = "white", linewidth = 1) +
  scale_fill_identity() +
  geom_text(aes(label = labels), vjust = -0.5, size = 3, fontface = "bold") +
  geom_text(aes(label = regions), vjust = 1.5, size = 2.5, color = "black") +
  scale_x_continuous(breaks = 1:10, labels = 1:10) +
  labs(
    title = "原始色阶（蓝色主导）",
    subtitle = "问题：前8级都是蓝色系，对比度不足",
    x = "色阶级别",
    y = ""
  ) +
  theme_minimal() +
  theme(
    axis.text.y = element_blank(),
    axis.ticks.y = element_blank(),
    panel.grid = element_blank(),
    plot.title = element_text(size = 14, face = "bold"),
    plot.subtitle = element_text(size = 12, color = "red")
  )

# 创建新的平衡色阶预览
plot_balanced <- ggplot(color_data, aes(x = level, y = 1)) +
  geom_tile(aes(fill = balanced), color = "white", linewidth = 1) +
  scale_fill_identity() +
  geom_text(aes(label = labels), vjust = -0.5, size = 3, fontface = "bold") +
  geom_text(aes(label = regions), vjust = 1.5, size = 2.5, color = "black") +
  scale_x_continuous(breaks = 1:10, labels = 1:10) +
  labs(
    title = "新的平衡色阶",
    subtitle = "改进：使用完整色彩光谱，蓝→绿→黄→青→橙→红",
    x = "色阶级别",
    y = ""
  ) +
  theme_minimal() +
  theme(
    axis.text.y = element_blank(),
    axis.ticks.y = element_blank(),
    panel.grid = element_blank(),
    plot.title = element_text(size = 14, face = "bold"),
    plot.subtitle = element_text(size = 12, color = "darkgreen")
  )

# 组合两个主要图表
combined_plot <- grid.arrange(
  plot_original,
  plot_balanced,
  ncol = 1,
  heights = c(1, 1),
  top = textGrob("脑卒中AAPC地图颜色方案对比",
                 gp = gpar(fontsize = 16, fontface = "bold"))
)

# 保存对比图
ggsave("stroke_color_scheme_comparison.png", 
       combined_plot, 
       width = 14, height = 10, dpi = 300, bg = "white")

cat("颜色方案对比图已保存: stroke_color_scheme_comparison.png\n")

# 输出颜色代码对比表
cat("\n=== 颜色代码对比表 ===\n")
cat("级别\tAAPC范围\t\t原始颜色\t新颜色\t\t描述\n")
cat("----\t--------\t\t--------\t------\t\t----\n")
for (i in 1:10) {
  cat(sprintf("%d\t%s\t\t%s\t%s\t%s\n",
    i,
    color_data$labels[i],
    color_data$original[i],
    color_data$balanced[i],
    ifelse(color_data$regions[i] != "", color_data$regions[i], "")
  ))
}

cat("\n=== 主要改进点 ===\n")
cat("1. 颜色分布: 从单一蓝色系 → 完整色彩光谱\n")
cat("2. 对比度: 相邻级别区分度显著提升\n")
cat("3. 视觉效果: 更丰富的色彩变化\n")
cat("4. 认知友好: 颜色含义更符合直觉\n")
cat("5. 印刷适配: 在不同媒介上表现更好\n")
