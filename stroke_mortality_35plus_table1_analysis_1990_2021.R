# 脑卒中死亡率Table 1分析 - 35岁以上人群 (1990-2021)
# 参照T1DM患病率表格结构创建脑卒中死亡率综合分析表

library(readr)
library(dplyr)
library(tidyr)

cat("=== 脑卒中死亡率Table 1分析开始 ===\n")

# 读取原始数据文件
data_files <- list.files("死亡损伤-数据库/204国家", pattern = "IHME-GBD_2021_DATA.*\\.csv$", 
                        recursive = TRUE, full.names = TRUE)

# 定义35岁以上的年龄组
age_groups_35plus <- data.frame(
  age_id = c(12, 13, 14, 15, 16, 17, 18, 19, 20, 30, 31, 32, 235),
  age_name = c("35-39", "40-44", "45-49", "50-54", "55-59", "60-64", 
               "65-69", "70-74", "75-79", "80-84", "85-89", "90-94", "≥95"),
  age_group_table = c("35-39", "40-44", "45-49", "50-54", "55-59", "60-64",
                      "65-69", "70-74", "75-79", "80-84", "85-89", "90-94", "≥95")
)

# 处理所有数据文件
cat("正在读取和处理数据文件...\n")
all_data <- data.frame()

for(file in data_files) {
  cat(paste("处理文件:", basename(file), "\n"))
  temp_data <- read_csv(file, show_col_types = FALSE)
  
  # 筛选脑卒中死亡数据
  filtered_data <- temp_data %>%
    filter(cause_id == 494,  # 脑卒中
           measure_id == 1,   # 死亡
           age_id %in% age_groups_35plus$age_id,  # 35岁以上年龄组
           metric_id == 3,    # 率
           year %in% c(1990, 2021)) %>%  # 起始和结束年份
    select(location_name, sex_id, sex_name, age_id, age_name, year, val, upper, lower)
  
  all_data <- rbind(all_data, filtered_data)
}

cat("数据读取完成，正在进行分组分析...\n")

# 添加年龄组信息
all_data <- all_data %>%
  left_join(age_groups_35plus, by = "age_id")

# 计算AAPC的函数
calculate_aapc <- function(rate_1990, rate_2021, years = 31) {
  # 处理向量化输入
  result <- numeric(length(rate_1990))

  for(i in seq_along(rate_1990)) {
    r1990 <- rate_1990[i]
    r2021 <- rate_2021[i]

    if(is.na(r1990) || is.na(r2021) || r1990 <= 0 || r2021 <= 0) {
      result[i] <- NA
    } else {
      result[i] <- (exp(log(r2021/r1990)/years) - 1) * 100
    }
  }

  return(result)
}

# 1. 全球总体数据（所有性别合计）
cat("计算全球总体数据...\n")
global_data <- all_data %>%
  filter(sex_id == 3) %>%  # 合计
  group_by(year) %>%
  summarise(
    deaths_mean = sum(val, na.rm = TRUE),
    deaths_lower = sum(lower, na.rm = TRUE),
    deaths_upper = sum(upper, na.rm = TRUE),
    # 计算年龄标准化率（简化为平均值）
    rate_mean = mean(val, na.rm = TRUE),
    rate_lower = mean(lower, na.rm = TRUE),
    rate_upper = mean(upper, na.rm = TRUE),
    .groups = 'drop'
  )

global_1990 <- global_data %>% filter(year == 1990)
global_2021 <- global_data %>% filter(year == 2021)

global_aapc <- calculate_aapc(global_1990$rate_mean, global_2021$rate_mean)

# 2. 按性别分层数据
cat("计算性别分层数据...\n")
sex_data <- all_data %>%
  filter(sex_id %in% c(1, 2)) %>%  # 男性和女性
  group_by(sex_name, year) %>%
  summarise(
    deaths_mean = sum(val, na.rm = TRUE),
    deaths_lower = sum(lower, na.rm = TRUE),
    deaths_upper = sum(upper, na.rm = TRUE),
    rate_mean = mean(val, na.rm = TRUE),
    rate_lower = mean(lower, na.rm = TRUE),
    rate_upper = mean(upper, na.rm = TRUE),
    .groups = 'drop'
  )

# 计算性别AAPC
sex_aapc <- sex_data %>%
  select(sex_name, year, rate_mean) %>%
  pivot_wider(names_from = year, values_from = rate_mean, names_prefix = "rate_") %>%
  mutate(aapc = calculate_aapc(rate_1990, rate_2021))

# 3. 按年龄组分层数据
cat("计算年龄组分层数据...\n")
age_data <- all_data %>%
  filter(sex_id == 3) %>%  # 合计
  group_by(age_group_table, year) %>%
  summarise(
    deaths_mean = sum(val, na.rm = TRUE),
    deaths_lower = sum(lower, na.rm = TRUE),
    deaths_upper = sum(upper, na.rm = TRUE),
    rate_mean = mean(val, na.rm = TRUE),
    rate_lower = mean(lower, na.rm = TRUE),
    rate_upper = mean(upper, na.rm = TRUE),
    .groups = 'drop'
  )

# 计算年龄组AAPC
age_aapc <- age_data %>%
  select(age_group_table, year, rate_mean) %>%
  pivot_wider(names_from = year, values_from = rate_mean, names_prefix = "rate_") %>%
  mutate(aapc = calculate_aapc(rate_1990, rate_2021))

cat("正在创建最终表格...\n")

# 创建最终表格结构
create_table_row <- function(category, data_1990, data_2021, aapc_val) {
  data.frame(
    Category = category,
    Deaths_1990_thousands = sprintf("%.1f (%.1f to %.1f)", 
                                   data_1990$deaths_mean/1000, 
                                   data_1990$deaths_lower/1000, 
                                   data_1990$deaths_upper/1000),
    Rate_1990_per100k = sprintf("%.1f (%.1f to %.1f)", 
                                data_1990$rate_mean, 
                                data_1990$rate_lower, 
                                data_1990$rate_upper),
    Deaths_2021_thousands = sprintf("%.1f (%.1f to %.1f)", 
                                   data_2021$deaths_mean/1000, 
                                   data_2021$deaths_lower/1000, 
                                   data_2021$deaths_upper/1000),
    Rate_2021_per100k = sprintf("%.1f (%.1f to %.1f)", 
                                data_2021$rate_mean, 
                                data_2021$rate_lower, 
                                data_2021$rate_upper),
    AAPC_95CI = sprintf("%.2f", aapc_val)
  )
}

# 构建完整表格
final_table <- data.frame()

# 1. 全球数据
global_row <- create_table_row("Global", global_1990, global_2021, global_aapc)
final_table <- rbind(final_table, global_row)

# 2. 性别数据
for(sex in c("女", "男")) {
  sex_1990 <- sex_data %>% filter(sex_name == sex, year == 1990)
  sex_2021 <- sex_data %>% filter(sex_name == sex, year == 2021)
  sex_aapc_val <- sex_aapc %>% filter(sex_name == sex) %>% pull(aapc)
  
  sex_label <- ifelse(sex == "女", "Female", "Male")
  sex_row <- create_table_row(sex_label, sex_1990, sex_2021, sex_aapc_val)
  final_table <- rbind(final_table, sex_row)
}

# 3. 年龄组数据
for(age in age_groups_35plus$age_group_table) {
  age_1990 <- age_data %>% filter(age_group_table == age, year == 1990)
  age_2021 <- age_data %>% filter(age_group_table == age, year == 2021)
  age_aapc_val <- age_aapc %>% filter(age_group_table == age) %>% pull(aapc)
  
  if(nrow(age_1990) > 0 && nrow(age_2021) > 0) {
    age_row <- create_table_row(age, age_1990, age_2021, age_aapc_val)
    final_table <- rbind(final_table, age_row)
  }
}

# 保存表格
write_csv(final_table, "stroke_mortality_35plus_table1_1990_2021.csv")

cat("Table 1已保存到: stroke_mortality_35plus_table1_1990_2021.csv\n")

# 显示表格预览
cat("\n=== Table 1预览 ===\n")
print(final_table)

cat("\n=== 分析完成 ===\n")
cat("注意：由于数据结构限制，本表格使用死亡率数据而非患病率数据\n")
cat("AAPC计算基于1990年和2021年的数据点\n")
