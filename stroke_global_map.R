# 脑卒中65岁以上人群死亡率AAPC全球地图可视化
# 基于1990-2019年数据 - 参考1型糖尿病分析方法

# 加载必要的包
library(ggplot2)
library(dplyr)
library(maps)
library(viridis)
library(RColorBrewer)
library(countrycode)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(gridExtra)
library(grid)
library(cowplot)

# 读取数据
data <- read.csv("脑卒中65岁以上人群患病率分析表.csv", stringsAsFactors = FALSE, fileEncoding = "UTF-8")

# 检查列名
print("列名:")
print(colnames(data))

# 创建国家/地区与AAPC的映射数据
# 由于原始数据是按地区分组的，我们需要将地区映射到具体国家
region_aapc <- data %>%
  filter(分类 %in% c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", "高收入",
                    "中欧、东欧和中亚", "拉丁美洲和加勒比海", "北非和中东")) %>%
  select(分类, AAPC = colnames(data)[14])  # 使用列索引而不是列名

# 创建国家到地区的映射
country_region_mapping <- data.frame(
  country = c(
    # 撒哈拉以南非洲
    "Nigeria", "Ethiopia", "South Africa", "Kenya", "Uganda", "Tanzania", "Ghana", "Mozambique", 
    "Madagascar", "Cameroon", "Angola", "Niger", "Burkina Faso", "Mali", "Malawi", "Zambia", 
    "Somalia", "Senegal", "Chad", "Zimbabwe", "Guinea", "Rwanda", "Benin", "Burundi", "Tunisia",
    "South Sudan", "Togo", "Sierra Leone", "Libya", "Liberia", "Central African Republic",
    "Mauritania", "Eritrea", "Gambia", "Botswana", "Namibia", "Gabon", "Lesotho", "Guinea-Bissau",
    "Equatorial Guinea", "Mauritius", "Eswatini", "Djibouti", "Comoros", "Cape Verde", "Sao Tome and Principe",
    
    # 东南亚、东亚和大洋洲
    "China", "Indonesia", "Japan", "Philippines", "Vietnam", "Thailand", "Myanmar", "South Korea",
    "Malaysia", "Cambodia", "Laos", "Singapore", "Mongolia", "Brunei", "Timor-Leste", "Australia",
    "Papua New Guinea", "New Zealand", "Fiji", "Solomon Islands", "Vanuatu", "Samoa", "Kiribati",
    "Tonga", "Micronesia", "Palau", "Marshall Islands", "Nauru", "Tuvalu",
    
    # 南亚
    "India", "Pakistan", "Bangladesh", "Afghanistan", "Nepal", "Sri Lanka", "Bhutan", "Maldives",
    
    # 高收入（主要发达国家）
    "United States", "Germany", "United Kingdom", "France", "Italy", "Canada", "Spain", "Netherlands",
    "Belgium", "Switzerland", "Austria", "Sweden", "Norway", "Denmark", "Finland", "Ireland",
    "Portugal", "Greece", "Israel", "Luxembourg", "Iceland", "Malta", "Cyprus",
    
    # 中欧、东欧和中亚
    "Russia", "Poland", "Ukraine", "Romania", "Czech Republic", "Hungary", "Belarus", "Bulgaria",
    "Serbia", "Slovakia", "Croatia", "Bosnia and Herzegovina", "Albania", "Lithuania", "Slovenia",
    "Latvia", "Estonia", "Macedonia", "Moldova", "Montenegro", "Kazakhstan", "Uzbekistan",
    "Tajikistan", "Kyrgyzstan", "Turkmenistan", "Georgia", "Armenia", "Azerbaijan",
    
    # 拉丁美洲和加勒比海
    "Brazil", "Mexico", "Colombia", "Argentina", "Peru", "Venezuela", "Chile", "Ecuador",
    "Guatemala", "Cuba", "Bolivia", "Haiti", "Dominican Republic", "Honduras", "Paraguay",
    "Nicaragua", "El Salvador", "Costa Rica", "Panama", "Uruguay", "Jamaica", "Trinidad and Tobago",
    "Guyana", "Suriname", "Belize", "Barbados", "Saint Vincent and the Grenadines", "Grenada",
    "Saint Lucia", "Antigua and Barbuda", "Dominica", "Saint Kitts and Nevis",
    
    # 北非和中东
    "Egypt", "Iran", "Turkey", "Iraq", "Saudi Arabia", "Yemen", "Syria", "Jordan", "Lebanon",
    "United Arab Emirates", "Oman", "Kuwait", "Qatar", "Bahrain", "Algeria", "Morocco", "Sudan"
  ),
  region = c(
    # 撒哈拉以南非洲
    rep("撒哈拉以南非洲", 46),
    # 东南亚、东亚和大洋洲
    rep("东南亚、东亚和大洋洲", 29),
    # 南亚
    rep("南亚", 8),
    # 高收入
    rep("高收入", 23),
    # 中欧、东欧和中亚
    rep("中欧、东欧和中亚", 28),
    # 拉丁美洲和加勒比海
    rep("拉丁美洲和加勒比海", 32),
    # 北非和中东
    rep("北非和中东", 17)
  )
)

# 合并数据
country_data <- country_region_mapping %>%
  left_join(region_aapc, by = c("region" = "分类"))

# 获取世界地图数据
world <- ne_countries(scale = "medium", returnclass = "sf")

# 标准化国家名称
world$name_clean <- countrycode(world$name, "country.name", "country.name")
country_data$country_clean <- countrycode(country_data$country, "country.name", "country.name")

# 合并地图数据和AAPC数据
world_data <- world %>%
  left_join(country_data, by = c("name_clean" = "country_clean"))

# 创建更细致的AAPC分组（基于实际数据范围-0.85到1.35）
world_data <- world_data %>%
  mutate(
    aapc_group = case_when(
      is.na(AAPC) ~ "No data",
      AAPC < -0.8 ~ "< -0.8",
      AAPC >= -0.8 & AAPC < -0.6 ~ "-0.8 to < -0.6",
      AAPC >= -0.6 & AAPC < -0.4 ~ "-0.6 to < -0.4",
      AAPC >= -0.4 & AAPC < 0 ~ "-0.4 to < 0",
      AAPC >= 0 & AAPC < 0.4 ~ "0 to < 0.4",
      AAPC >= 0.4 & AAPC < 0.8 ~ "0.4 to < 0.8",
      AAPC >= 0.8 & AAPC < 1.2 ~ "0.8 to < 1.2",
      AAPC >= 1.2 ~ "≥ 1.2"
    ),
    aapc_group = factor(aapc_group, levels = c("< -0.8", "-0.8 to < -0.6", "-0.6 to < -0.4",
                                               "-0.4 to < 0", "0 to < 0.4", "0.4 to < 0.8",
                                               "0.8 to < 1.2", "≥ 1.2", "No data"))
  )

# 创建更细致的颜色调色板（蓝色表示改善，红色表示恶化）
colors <- c("#08519c", "#2171b5", "#4292c6", "#6baed6",
           "#fee0d2", "#fcbba1", "#fc9272", "#de2d26", "#cccccc")
names(colors) <- levels(world_data$aapc_group)

# 创建主地图
main_map <- ggplot(world_data) +
  geom_sf(aes(fill = aapc_group), color = "white", size = 0.1) +
  scale_fill_manual(values = colors, name = "AAPC (%)", drop = FALSE) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 10),
    plot.title = element_text(size = 14, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 12, hjust = 0.5),
    legend.key.size = unit(0.8, "cm")
  ) +
  labs(
    title = "Average Annual Percentage Change in Stroke Prevalence",
    subtitle = "Among people aged ≥65 years, 1990-2019"
  )

# 保存地图
ggsave("stroke_aapc_global_map.png", main_map, width = 16, height = 10, dpi = 300, bg = "white")

# 显示地图
print(main_map)

# 打印数据摘要
cat("脑卒中65岁以上人群AAPC数据摘要:\n")
print(region_aapc)

cat("\nAAPC分组统计:\n")
print(table(world_data$aapc_group, useNA = "ifany"))
