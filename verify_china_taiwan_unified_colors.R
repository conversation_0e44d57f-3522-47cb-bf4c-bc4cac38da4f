# 验证中国和台湾统一颜色显示

library(dplyr)
library(rnaturalearth)
library(sf)
library(ggplot2)

cat("=== 验证中国和台湾统一颜色显示 ===\n")

# 1. 读取和处理数据（与主脚本相同的步骤）
aapc_data <- read.csv("stroke_mortality_35plus_aapc_results_1990_2021.csv", 
                     stringsAsFactors = FALSE, fileEncoding = "UTF-8")

# 应用名称映射（简化版本）
aapc_data$location_name_en <- aapc_data$location_name
aapc_data$location_name_en[aapc_data$location_name == "中国"] <- "China"
aapc_data$location_name_en[aapc_data$location_name == "台湾"] <- "Taiwan"

# 处理台湾和中国统一颜色
taiwan_data <- aapc_data[aapc_data$location_name == "台湾", ]
china_data <- aapc_data[aapc_data$location_name == "中国", ]

if(nrow(taiwan_data) > 0 && nrow(china_data) > 0) {
  combined_aapc <- mean(c(china_data$aapc, taiwan_data$aapc), na.rm = TRUE)
  
  # 更新两者的AAPC值为相同值
  aapc_data$aapc[aapc_data$location_name == "中国"] <- combined_aapc
  aapc_data$aapc[aapc_data$location_name == "台湾"] <- combined_aapc
  aapc_data$location_name_en[aapc_data$location_name == "中国"] <- "China"
  aapc_data$location_name_en[aapc_data$location_name == "台湾"] <- "Taiwan"
  
  cat("统一AAPC值:", round(combined_aapc, 2), "%\n")
}

# 2. 获取世界地图数据并合并
world <- ne_countries(scale = "medium", returnclass = "sf")
world_data <- world %>%
  left_join(aapc_data, by = c("name" = "location_name_en"))

# 3. 检查中国和台湾的最终状态
china_final <- world_data[world_data$name == "China", ]
taiwan_final <- world_data[world_data$name == "Taiwan", ]

cat("\n=== 最终数据状态 ===\n")
cat("中国数据:\n")
print(data.frame(
  name = china_final$name,
  aapc = china_final$aapc,
  is_na = is.na(china_final$aapc)
))

cat("台湾数据:\n")
print(data.frame(
  name = taiwan_final$name,
  aapc = taiwan_final$aapc,
  is_na = is.na(taiwan_final$aapc)
))

# 4. 创建分位数分类
decile_breaks <- quantile(aapc_data$aapc, probs = seq(0, 1, 0.1))
decile_breaks[1] <- decile_breaks[1] - 0.01
decile_breaks[11] <- decile_breaks[11] + 0.01

world_data$aapc_category <- cut(world_data$aapc,
                               breaks = decile_breaks,
                               include.lowest = TRUE)

# 检查分类结果
china_classified <- world_data[world_data$name == "China", ]
taiwan_classified <- world_data[world_data$name == "Taiwan", ]

cat("\n=== 分类结果 ===\n")
cat("中国分类:\n")
print(data.frame(
  name = china_classified$name,
  aapc = china_classified$aapc,
  aapc_category = china_classified$aapc_category,
  is_na_category = is.na(china_classified$aapc_category)
))

cat("台湾分类:\n")
print(data.frame(
  name = taiwan_classified$name,
  aapc = taiwan_classified$aapc,
  aapc_category = taiwan_classified$aapc_category,
  is_na_category = is.na(taiwan_classified$aapc_category)
))

# 5. 验证颜色一致性
if(!is.na(china_classified$aapc_category) && !is.na(taiwan_classified$aapc_category)) {
  colors_match <- china_classified$aapc_category == taiwan_classified$aapc_category
  cat("\n=== 颜色一致性验证 ===\n")
  cat("中国和台湾颜色分类是否一致:", colors_match, "\n")
  if(colors_match) {
    cat("✅ 成功：中国和台湾将显示相同颜色\n")
    cat("共同的分类级别:", china_classified$aapc_category, "\n")
  } else {
    cat("❌ 失败：中国和台湾颜色分类不一致\n")
  }
} else {
  cat("❌ 失败：中国或台湾数据分类失败\n")
}

# 6. 创建简单的验证地图
cat("\n=== 创建验证地图 ===\n")

colors_10_level <- c(
  "#08306B", "#2171B5", "#4292C6", "#6BAED6", "#9ECAE1",
  "#FFFFCC", "#A1DAB4", "#41B6C4", "#FD8D3C", "#E31A1C"
)

verification_map <- ggplot(world_data) +
  geom_sf(aes(fill = aapc_category), color = "white", size = 0.1) +
  scale_fill_manual(values = colors_10_level,
                   name = "AAPC (% per year)",
                   na.value = "grey85",
                   drop = FALSE) +
  theme_void() +
  labs(title = "China-Taiwan Unified Color Verification Map",
       subtitle = "Both should display the same color") +
  theme(legend.position = "bottom")

ggsave("china_taiwan_unified_color_verification.png", verification_map, 
       width = 16, height = 10, dpi = 300, bg = "white")

cat("验证地图已保存为: china_taiwan_unified_color_verification.png\n")

cat("\n=== 验证完成 ===\n")
