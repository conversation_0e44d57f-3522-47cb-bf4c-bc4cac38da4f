# 最终验证中国数据是否正确显示

library(dplyr)
library(rnaturalearth)
library(sf)
library(ggplot2)

# 重现脚本的关键步骤
cat("=== 验证中国数据最终状态 ===\n")

# 1. 读取和处理数据（与主脚本相同的步骤）
country_aapc_35plus <- read.csv("stroke_dalys_35plus_aapc_results_1990_2021.csv", 
                               stringsAsFactors = FALSE, fileEncoding = "UTF-8")

# 应用名称映射
create_country_name_mapping <- function() {
  mapping <- data.frame(
    chinese_name = c("中国", "台湾", "美利坚合众国", "印度", "俄罗斯", "日本", "德国", "英国", "法国", "意大利"),
    english_name = c("China", "Taiwan", "United States of America", "India", "Russia", "Japan", "Germany", "United Kingdom", "France", "Italy"),
    stringsAsFactors = FALSE
  )
  return(mapping)
}

name_mapping <- create_country_name_mapping()
country_aapc_35plus$location_name_en <- country_aapc_35plus$location_name

for(i in 1:nrow(name_mapping)) {
  country_aapc_35plus$location_name_en[country_aapc_35plus$location_name == name_mapping$chinese_name[i]] <- name_mapping$english_name[i]
}

# 处理台湾合并
taiwan_data <- country_aapc_35plus[country_aapc_35plus$location_name == "台湾", ]
china_data <- country_aapc_35plus[country_aapc_35plus$location_name == "中国", ]

if(nrow(taiwan_data) > 0 && nrow(china_data) > 0) {
  combined_aapc <- mean(c(china_data$aapc, taiwan_data$aapc), na.rm = TRUE)
  country_aapc_35plus$aapc[country_aapc_35plus$location_name == "中国"] <- combined_aapc
  country_aapc_35plus$location_name_en[country_aapc_35plus$location_name == "中国"] <- "China"
  country_aapc_35plus <- country_aapc_35plus[country_aapc_35plus$location_name != "台湾", ]
}

# 2. 获取世界地图数据并合并
world <- ne_countries(scale = "medium", returnclass = "sf")
world_with_aapc <- world %>%
  left_join(country_aapc_35plus, by = c("name" = "location_name_en"))

# 3. 检查中国的最终状态
china_final <- world_with_aapc[world_with_aapc$name == "China", ]
cat("中国在最终地图中的数据:\n")
print(data.frame(
  name = china_final$name,
  aapc = china_final$aapc,
  location_name = china_final$location_name,
  is_na = is.na(china_final$aapc)
))

# 4. 创建十分位数分类
decile_breaks <- quantile(country_aapc_35plus$aapc, probs = seq(0, 1, 0.1))
decile_breaks[1] <- decile_breaks[1] - 0.01
decile_breaks[11] <- decile_breaks[11] + 0.01

world_with_aapc$aapc_decile <- cut(world_with_aapc$aapc,
                                  breaks = decile_breaks,
                                  include.lowest = TRUE)

# 检查中国的分类
china_classified <- world_with_aapc[world_with_aapc$name == "China", ]
cat("中国的十分位数分类:\n")
print(data.frame(
  name = china_classified$name,
  aapc = china_classified$aapc,
  aapc_decile = china_classified$aapc_decile,
  is_na_decile = is.na(china_classified$aapc_decile)
))

# 5. 创建简单的测试地图
cat("创建测试地图...\n")

ultra_fine_colors <- c(
  "#053061", "#2166ac", "#4393c3", "#92c5de", "#d1e5f0",
  "#f7f7f7", "#fddbc7", "#f4a582", "#d6604d", "#b2182b"
)

test_map <- ggplot(world_with_aapc) +
  geom_sf(aes(fill = aapc_decile), color = "white", size = 0.1) +
  scale_fill_manual(values = ultra_fine_colors,
                   name = "AAPC (% per year)",
                   na.value = "grey85",
                   drop = FALSE) +
  theme_void() +
  labs(title = "Test Map - China Data Verification")

ggsave("china_verification_test_map.png", test_map, 
       width = 16, height = 10, dpi = 300, bg = "white")

cat("测试地图已保存为: china_verification_test_map.png\n")

# 6. 总结
cat("\n=== 总结 ===\n")
if(!is.na(china_final$aapc)) {
  cat("✅ 中国数据成功匹配，AAPC =", round(china_final$aapc, 2), "%\n")
  if(!is.na(china_classified$aapc_decile)) {
    cat("✅ 中国数据成功分类，应该显示为有颜色\n")
    cat("中国的AAPC值", round(china_final$aapc, 2), "% 对应的十分位数:", china_classified$aapc_decile, "\n")
  } else {
    cat("❌ 中国数据分类失败，会显示为灰色\n")
  }
} else {
  cat("❌ 中国数据匹配失败，会显示为灰色\n")
}

cat("验证完成！\n")
