# 脑卒中35岁以上人群DALYs年均变化率分析 (1990-2021)

library(dplyr)
library(ggplot2)
library(maps)
library(viridis)
library(RColorBrewer)
library(gridExtra)
library(grid)

# 1. 数据加载和处理
cat("Loading stroke DALYs data for 35+ population...\n")

# 加载DALYs数据文件
dalys_files <- c(
  '死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-3/IHME-GBD_2021_DATA-336925ca-3.csv',
  '死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-4/IHME-GBD_2021_DATA-336925ca-4.csv'
)

all_dalys_data <- data.frame()
for(file in dalys_files) {
  data <- read.csv(file)
  all_dalys_data <- rbind(all_dalys_data, data)
}

cat("Total DALYs records:", nrow(all_dalys_data), "\n")

# 2. 筛选35岁以上人群数据
stroke_35plus <- all_dalys_data %>%
  filter(
    cause_name == "脑卒中",
    measure_name == "伤残调整生命年",
    age_name %in% c("35-39岁", "40-44岁", "45-49岁", "50-54岁", "55-59岁", 
                   "60-64岁", "65-69岁", "70-74岁", "75-79岁", "80-84岁", "85岁以上"),
    sex_name == "合计",
    metric_name == "率"  # 使用年龄标准化率
  ) %>%
  select(location_name, year, age_name, val) %>%
  group_by(location_name, year) %>%
  summarise(dalys_rate_35plus = sum(val, na.rm = TRUE), .groups = 'drop')

cat("Countries with 35+ DALYs data:", length(unique(stroke_35plus$location_name)), "\n")
cat("Year range:", min(stroke_35plus$year), "-", max(stroke_35plus$year), "\n")

# 3. 计算AAPC (Average Annual Percentage Change)
calculate_aapc <- function(data) {
  if(nrow(data) < 2) return(NA)
  
  # 对数线性回归
  model <- lm(log(dalys_rate_35plus + 0.001) ~ year, data = data)  # 加小常数避免log(0)
  slope <- coef(model)[2]
  
  # AAPC = (e^slope - 1) * 100
  aapc <- (exp(slope) - 1) * 100
  return(aapc)
}

# 按国家计算AAPC
country_aapc_35plus <- stroke_35plus %>%
  group_by(location_name) %>%
  do(aapc = calculate_aapc(.)) %>%
  mutate(aapc = unlist(aapc)) %>%
  filter(!is.na(aapc))

cat("Countries with valid AAPC:", nrow(country_aapc_35plus), "\n")
cat("AAPC range:", round(min(country_aapc_35plus$aapc, na.rm=TRUE), 2), "to", 
    round(max(country_aapc_35plus$aapc, na.rm=TRUE), 2), "\n")

# 4. 基本统计分析
cat("\n=== 35岁以上人群脑卒中DALYs AAPC分析 ===\n")
cat("分析国家数量:", nrow(country_aapc_35plus), "\n")
cat("AAPC范围:", round(min(country_aapc_35plus$aapc), 2), "% 到", 
    round(max(country_aapc_35plus$aapc), 2), "%\n")
cat("平均AAPC:", round(mean(country_aapc_35plus$aapc), 2), "%\n")
cat("中位数AAPC:", round(median(country_aapc_35plus$aapc), 2), "%\n")
cat("标准差:", round(sd(country_aapc_35plus$aapc), 2), "%\n")

# 5. 趋势分析
negative_count <- sum(country_aapc_35plus$aapc < 0)
positive_count <- sum(country_aapc_35plus$aapc > 0)
zero_count <- sum(country_aapc_35plus$aapc == 0)

cat("\n趋势分布:\n")
cat("下降趋势国家 (AAPC < 0):", negative_count, "个 (", 
    round(negative_count/nrow(country_aapc_35plus)*100, 1), "%)\n")
cat("上升趋势国家 (AAPC > 0):", positive_count, "个 (", 
    round(positive_count/nrow(country_aapc_35plus)*100, 1), "%)\n")
cat("无变化国家 (AAPC = 0):", zero_count, "个\n")

# 6. 极值分析
cat("\n下降最快的10个国家:\n")
top_decreasing <- country_aapc_35plus %>% 
  arrange(aapc) %>% 
  head(10)
for(i in 1:nrow(top_decreasing)) {
  cat(sprintf("%2d. %s: %6.2f%%\n", i, top_decreasing$location_name[i], 
              top_decreasing$aapc[i]))
}

cat("\n上升最快的10个国家:\n")
top_increasing <- country_aapc_35plus %>% 
  arrange(desc(aapc)) %>% 
  head(10)
for(i in 1:nrow(top_increasing)) {
  cat(sprintf("%2d. %s: %6.2f%%\n", i, top_increasing$location_name[i], 
              top_increasing$aapc[i]))
}

# 7. 与65岁以上人群对比
if(file.exists("stroke_dalys_65plus_aapc_results_1990_2021.csv")) {
  country_aapc_65plus <- read.csv("stroke_dalys_65plus_aapc_results_1990_2021.csv")
  
  # 合并数据进行对比
  comparison <- merge(country_aapc_35plus, country_aapc_65plus, 
                     by = "location_name", suffixes = c("_35plus", "_65plus"))
  
  cat("\n=== 35岁以上 vs 65岁以上对比分析 ===\n")
  cat("可对比国家数:", nrow(comparison), "\n")
  cat("35岁以上平均AAPC:", round(mean(comparison$aapc_35plus), 2), "%\n")
  cat("65岁以上平均AAPC:", round(mean(comparison$aapc_65plus), 2), "%\n")
  cat("差异 (35+ - 65+):", round(mean(comparison$aapc_35plus - comparison$aapc_65plus), 2), "%\n")
  
  # 相关性分析
  correlation <- cor(comparison$aapc_35plus, comparison$aapc_65plus)
  cat("两个年龄组AAPC相关系数:", round(correlation, 3), "\n")
  
  # 保存对比数据
  write.csv(comparison, "stroke_dalys_35plus_vs_65plus_comparison_1990_2021.csv", row.names = FALSE)
}

# 8. 保存35岁以上分析结果
country_aapc_35plus$region <- country_aapc_35plus$location_name
write.csv(country_aapc_35plus, "stroke_dalys_35plus_aapc_results_1990_2021.csv", row.names = FALSE)

# 9. 按变化幅度分类
country_aapc_35plus$change_category <- cut(country_aapc_35plus$aapc, 
                                          breaks = c(-Inf, -3, -2, -1, 0, 1, Inf),
                                          labels = c("大幅下降(< -3%)", "中度下降(-3% to -2%)", 
                                                   "轻度下降(-2% to -1%)", "轻微下降(-1% to 0%)",
                                                   "轻微上升(0% to 1%)", "中度上升(> 1%)"))

cat("\n按变化幅度分类:\n")
category_summary <- table(country_aapc_35plus$change_category)
for(i in 1:length(category_summary)) {
  cat("", names(category_summary)[i], ":", category_summary[i], "个国家\n")
}

cat("\n=== 35岁以上人群分析完成 ===\n")
cat("结果已保存至: stroke_dalys_35plus_aapc_results_1990_2021.csv\n")
cat("对比分析已保存至: stroke_dalys_35plus_vs_65plus_comparison_1990_2021.csv\n")
cat("准备创建35岁以上人群的可视化地图...\n")
