# 35岁以上 vs 65岁以上脑卒中DALYs AAPC对比可视化

library(dplyr)
library(ggplot2)
library(gridExtra)
library(grid)

# 1. 加载对比数据
cat("Loading comparison data...\n")
comparison <- read.csv("stroke_dalys_35plus_vs_65plus_comparison_1990_2021.csv")

cat("Comparison analysis:\n")
cat("Countries:", nrow(comparison), "\n")
cat("35+ mean AAPC:", round(mean(comparison$aapc_35plus), 2), "%\n")
cat("65+ mean AAPC:", round(mean(comparison$aapc_65plus), 2), "%\n")
cat("Correlation:", round(cor(comparison$aapc_35plus, comparison$aapc_65plus), 3), "\n")

# 2. 创建散点图对比
scatter_plot <- ggplot(comparison, aes(x = aapc_65plus, y = aapc_35plus)) +
  geom_point(alpha = 0.6, size = 2, color = "#2166ac") +
  geom_abline(intercept = 0, slope = 1, linetype = "dashed", color = "red", size = 1) +
  geom_smooth(method = "lm", se = TRUE, color = "#d6604d", fill = "#fddbc7") +
  labs(
    title = "Stroke DALYs AAPC: 35+ vs 65+ Population (1990-2021)",
    subtitle = paste("Correlation =", round(cor(comparison$aapc_35plus, comparison$aapc_65plus), 3)),
    x = "65+ Population AAPC (%)",
    y = "35+ Population AAPC (%)"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(size = 14, face = "bold"),
    plot.subtitle = element_text(size = 12),
    axis.title = element_text(size = 12),
    axis.text = element_text(size = 10)
  ) +
  coord_fixed()

# 3. 创建差异分布直方图
comparison$difference <- comparison$aapc_35plus - comparison$aapc_65plus

hist_plot <- ggplot(comparison, aes(x = difference)) +
  geom_histogram(bins = 30, fill = "#4393c3", alpha = 0.7, color = "white") +
  geom_vline(xintercept = mean(comparison$difference), color = "red", linetype = "dashed", size = 1) +
  labs(
    title = "Distribution of AAPC Differences (35+ minus 65+)",
    subtitle = paste("Mean difference =", round(mean(comparison$difference), 3), "%"),
    x = "AAPC Difference (%)",
    y = "Number of Countries"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(size = 14, face = "bold"),
    plot.subtitle = element_text(size = 12),
    axis.title = element_text(size = 12),
    axis.text = element_text(size = 10)
  )

# 4. 创建箱线图对比
boxplot_data <- data.frame(
  AAPC = c(comparison$aapc_35plus, comparison$aapc_65plus),
  Age_Group = rep(c("35+ Population", "65+ Population"), each = nrow(comparison))
)

box_plot <- ggplot(boxplot_data, aes(x = Age_Group, y = AAPC, fill = Age_Group)) +
  geom_boxplot(alpha = 0.7) +
  geom_jitter(width = 0.2, alpha = 0.3, size = 1) +
  scale_fill_manual(values = c("#2166ac", "#d6604d")) +
  labs(
    title = "AAPC Distribution Comparison",
    subtitle = "Stroke DALYs AAPC by Age Group (1990-2021)",
    x = "Age Group",
    y = "AAPC (%)"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(size = 14, face = "bold"),
    plot.subtitle = element_text(size = 12),
    axis.title = element_text(size = 12),
    axis.text = element_text(size = 10),
    legend.position = "none"
  )

# 5. 识别差异最大的国家
comparison$abs_difference <- abs(comparison$difference)
top_differences <- comparison %>%
  arrange(desc(abs_difference)) %>%
  head(10)

cat("\nTop 10 countries with largest AAPC differences:\n")
for(i in 1:nrow(top_differences)) {
  cat(sprintf("%2d. %s: 35+=%5.2f%%, 65+=%5.2f%%, Diff=%5.2f%%\n", 
              i, top_differences$location_name[i], 
              top_differences$aapc_35plus[i], 
              top_differences$aapc_65plus[i],
              top_differences$difference[i]))
}

# 6. 创建差异最大国家的条形图
top_diff_plot <- ggplot(top_differences, aes(x = reorder(location_name, abs_difference))) +
  geom_col(aes(y = difference), fill = "#92c5de", alpha = 0.8) +
  coord_flip() +
  labs(
    title = "Top 10 Countries with Largest AAPC Differences",
    subtitle = "35+ Population AAPC minus 65+ Population AAPC",
    x = "Country",
    y = "AAPC Difference (%)"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(size = 12, face = "bold"),
    plot.subtitle = element_text(size = 10),
    axis.title = element_text(size = 10),
    axis.text = element_text(size = 9)
  )

# 7. 组合所有图表
cat("Creating combined comparison charts...\n")

# 上半部分：散点图和箱线图
top_row <- grid.arrange(scatter_plot, box_plot, ncol = 2)

# 下半部分：直方图和差异条形图
bottom_row <- grid.arrange(hist_plot, top_diff_plot, ncol = 2)

# 最终组合
final_comparison_plot <- grid.arrange(
  top_row, bottom_row,
  ncol = 1,
  heights = c(1, 1),
  top = textGrob("Stroke DALYs AAPC Comparison: 35+ vs 65+ Population (1990-2021)", 
                gp = gpar(fontsize = 16, fontface = "bold"))
)

# 8. 保存图表
ggsave("stroke_dalys_35plus_vs_65plus_comparison_charts_1990_2021.png", 
       final_comparison_plot, width = 16, height = 12, dpi = 300, bg = "white")

ggsave("stroke_dalys_35plus_vs_65plus_scatter_plot_1990_2021.png", 
       scatter_plot, width = 10, height = 8, dpi = 300, bg = "white")

# 9. 统计摘要
summary_stats <- data.frame(
  Statistic = c("Mean AAPC", "Median AAPC", "SD AAPC", "Min AAPC", "Max AAPC", 
                "Countries with decline (%)", "Countries with increase (%)"),
  Age_35_Plus = c(
    round(mean(comparison$aapc_35plus), 2),
    round(median(comparison$aapc_35plus), 2),
    round(sd(comparison$aapc_35plus), 2),
    round(min(comparison$aapc_35plus), 2),
    round(max(comparison$aapc_35plus), 2),
    round(sum(comparison$aapc_35plus < 0) / nrow(comparison) * 100, 1),
    round(sum(comparison$aapc_35plus > 0) / nrow(comparison) * 100, 1)
  ),
  Age_65_Plus = c(
    round(mean(comparison$aapc_65plus), 2),
    round(median(comparison$aapc_65plus), 2),
    round(sd(comparison$aapc_65plus), 2),
    round(min(comparison$aapc_65plus), 2),
    round(max(comparison$aapc_65plus), 2),
    round(sum(comparison$aapc_65plus < 0) / nrow(comparison) * 100, 1),
    round(sum(comparison$aapc_65plus > 0) / nrow(comparison) * 100, 1)
  )
)

write.csv(summary_stats, "stroke_dalys_35plus_vs_65plus_summary_stats_1990_2021.csv", row.names = FALSE)

cat("Comparison visualization completed successfully!\n")
cat("Files saved:\n")
cat("- stroke_dalys_35plus_vs_65plus_comparison_charts_1990_2021.png\n")
cat("- stroke_dalys_35plus_vs_65plus_scatter_plot_1990_2021.png\n")
cat("- stroke_dalys_35plus_vs_65plus_summary_stats_1990_2021.csv\n")
