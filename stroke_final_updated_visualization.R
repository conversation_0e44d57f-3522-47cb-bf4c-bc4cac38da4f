# 基于完整1990-2021年数据的脑卒中65岁以上人群发病率可视化

library(ggplot2)
library(dplyr)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(RColorBrewer)
library(gridExtra)
library(grid)
library(scales)

cat("=== 脑卒中65岁以上人群发病率最终可视化 ===\n")

# 读取最终分析表
stroke_data <- read.csv("脑卒中65岁以上人群发病率最终分析表_1990_2021.csv", 
                       stringsAsFactors = FALSE, 
                       fileEncoding = "UTF-8")

cat("数据读取完成，共", nrow(stroke_data), "行\n")

# 提取地区AAPC数据
region_aapc <- stroke_data %>%
  filter(分类 %in% c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", 
                    "高收入", "中欧、东欧和中亚", "拉丁美洲和加勒比海", 
                    "北非和中东")) %>%
  select(
    地区 = 分类,
    AAPC = `AAPC...`
  )

cat("地区AAPC数据提取完成，共", nrow(region_aapc), "个地区\n")
print(region_aapc)

# 获取世界地图
world <- ne_countries(scale = "medium", returnclass = "sf")

# 创建国家映射
create_country_mapping <- function(world_data) {
  country_names <- world_data$name_en
  
  mapping <- data.frame(
    country = country_names,
    region = case_when(
      # 高收入地区
      country_names %in% c("United States of America", "Canada", "Germany", "France", "United Kingdom", 
                          "Italy", "Spain", "Netherlands", "Belgium", "Switzerland", 
                          "Austria", "Sweden", "Norway", "Denmark", "Finland", "Ireland", 
                          "Portugal", "Greece", "Australia", "New Zealand", "Japan", 
                          "South Korea", "Israel", "Luxembourg", "Iceland", "Czech Republic",
                          "Slovenia", "Estonia", "Latvia", "Lithuania", "Slovakia", "Poland",
                          "Hungary", "Croatia") ~ "高收入",
      
      # 撒哈拉以南非洲
      country_names %in% c("Nigeria", "Ethiopia", "South Africa", "Kenya", "Uganda", "Tanzania", 
                          "Ghana", "Mozambique", "Madagascar", "Cameroon", "Angola", "Niger", 
                          "Burkina Faso", "Mali", "Malawi", "Zambia", "Somalia", "Senegal", 
                          "Chad", "Zimbabwe", "Guinea", "Rwanda", "Benin", "Burundi", 
                          "South Sudan", "Togo", "Sierra Leone", "Liberia", "Botswana", 
                          "Namibia", "Gabon", "Lesotho", "Gambia", "Guinea-Bissau", 
                          "Equatorial Guinea", "Mauritius", "Eswatini", "Djibouti", 
                          "Comoros", "Cape Verde", "São Tomé and Príncipe", "Seychelles",
                          "Central African Republic", "Republic of the Congo", 
                          "Democratic Republic of the Congo", "Ivory Coast") ~ "撒哈拉以南非洲",
      
      # 东南亚、东亚和大洋洲
      country_names %in% c("China", "Indonesia", "Philippines", "Vietnam", "Thailand", 
                          "Myanmar", "Malaysia", "Cambodia", "Laos", "Singapore", 
                          "Brunei", "East Timor", "Papua New Guinea", "Fiji", 
                          "Solomon Islands", "Vanuatu", "Samoa", "Tonga", "Kiribati", 
                          "Tuvalu", "Nauru", "Palau", "Marshall Islands", 
                          "Federated States of Micronesia", "Mongolia", "North Korea") ~ "东南亚、东亚和大洋洲",
      
      # 南亚
      country_names %in% c("India", "Pakistan", "Bangladesh", "Afghanistan", "Nepal", 
                          "Sri Lanka", "Bhutan", "Maldives") ~ "南亚",
      
      # 中欧、东欧和中亚
      country_names %in% c("Russia", "Ukraine", "Belarus", "Moldova", "Romania", "Bulgaria", 
                          "Serbia", "Bosnia and Herzegovina", "Montenegro", "Albania", 
                          "North Macedonia", "Kosovo", "Kazakhstan", "Uzbekistan", 
                          "Turkmenistan", "Kyrgyzstan", "Tajikistan", "Armenia", 
                          "Azerbaijan", "Georgia") ~ "中欧、东欧和中亚",
      
      # 拉丁美洲和加勒比海
      country_names %in% c("Brazil", "Mexico", "Argentina", "Colombia", "Peru", "Venezuela", 
                          "Chile", "Ecuador", "Guatemala", "Cuba", "Bolivia", "Haiti", 
                          "Dominican Republic", "Honduras", "Paraguay", "Nicaragua", 
                          "Costa Rica", "Panama", "Uruguay", "Jamaica", "Trinidad and Tobago", 
                          "Bahamas", "Belize", "Barbados", "Saint Lucia", "Grenada", 
                          "Saint Vincent and the Grenadines", "Antigua and Barbuda", 
                          "Dominica", "Saint Kitts and Nevis", "Suriname", "Guyana") ~ "拉丁美洲和加勒比海",
      
      # 北非和中东
      country_names %in% c("Egypt", "Iran", "Turkey", "Iraq", "Saudi Arabia", "Yemen", 
                          "Syria", "Jordan", "Lebanon", "Libya", "Tunisia", "Algeria", 
                          "Morocco", "Sudan", "Oman", "Kuwait", "United Arab Emirates", 
                          "Qatar", "Bahrain", "Cyprus", "Malta") ~ "北非和中东",
      
      TRUE ~ "其他"
    ),
    stringsAsFactors = FALSE
  )
  
  return(mapping)
}

# 创建映射
country_mapping <- create_country_mapping(world)

# 合并地图数据和AAPC数据
world_with_aapc <- world %>%
  left_join(country_mapping, by = c("name_en" = "country")) %>%
  left_join(region_aapc, by = c("region" = "地区"))

cat("地图数据合并完成\n")

# 创建颜色分级（基于实际AAPC值范围）
aapc_range <- range(region_aapc$AAPC, na.rm = TRUE)
cat("AAPC范围:", aapc_range[1], "到", aapc_range[2], "\n")

# 创建10级颜色分级
breaks <- seq(aapc_range[1], aapc_range[2], length.out = 11)
colors <- colorRampPalette(c("#2166AC", "#4393C3", "#92C5DE", "#D1E5F0", "#F7F7F7",
                            "#FDBF6F", "#FD8D3C", "#E31A1C", "#B10026"))(10)

# 创建主地图
main_map <- ggplot(world_with_aapc) +
  geom_sf(aes(fill = AAPC), color = "white", size = 0.1) +
  scale_fill_gradientn(
    colors = colors,
    values = scales::rescale(breaks),
    name = "AAPC (%)",
    na.value = "grey90",
    breaks = pretty(aapc_range, n = 5),
    guide = guide_colorbar(
      title.position = "top",
      title.hjust = 0.5,
      barwidth = 15,
      barheight = 1
    )
  ) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 10),
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 12, hjust = 0.5),
    plot.caption = element_text(size = 10, hjust = 0.5)
  ) +
  labs(
    title = "脑卒中65岁以上人群发病率变化趋势 (1990-2021)",
    subtitle = "年均百分比变化 (AAPC) 按地区分布",
    caption = "基于32年完整时间序列数据 | 数据来源: GBD 2021"
  )

# 保存主地图
ggsave("stroke_incidence_final_main_map_1990_2021.png", main_map, 
       width = 16, height = 10, dpi = 300, bg = "white")

cat("主地图已保存: stroke_incidence_final_main_map_1990_2021.png\n")

# 创建AAPC条形图
aapc_bar_data <- region_aapc %>%
  arrange(AAPC) %>%
  mutate(
    地区 = factor(地区, levels = 地区),
    颜色 = ifelse(AAPC < 0, "下降", "上升")
  )

aapc_bar_plot <- ggplot(aapc_bar_data, aes(x = 地区, y = AAPC, fill = 颜色)) +
  geom_col() +
  geom_hline(yintercept = 0, linetype = "dashed", color = "black") +
  scale_fill_manual(values = c("下降" = "#2166AC", "上升" = "#B2182B")) +
  coord_flip() +
  theme_minimal() +
  theme(
    legend.position = "bottom",
    legend.title = element_blank(),
    axis.title = element_text(size = 12),
    axis.text = element_text(size = 10),
    plot.title = element_text(size = 14, face = "bold"),
    plot.subtitle = element_text(size = 12),
    plot.caption = element_text(size = 10)
  ) +
  labs(
    title = "各地区脑卒中65岁以上人群发病率AAPC (1990-2021)",
    subtitle = "年均百分比变化",
    x = "地区",
    y = "AAPC (%)",
    caption = "负值表示发病率下降，正值表示发病率上升"
  )

# 保存条形图
ggsave("stroke_incidence_aapc_barplot_1990_2021.png", aapc_bar_plot, 
       width = 12, height = 8, dpi = 300, bg = "white")

cat("AAPC条形图已保存: stroke_incidence_aapc_barplot_1990_2021.png\n")

cat("=== 最终可视化完成 ===\n")
cat("所有分析均基于1990-2021年完整32年时间序列数据\n")
