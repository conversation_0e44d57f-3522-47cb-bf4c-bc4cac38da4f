# 创建改进图例的完整布局 - 主地图 + 六个局部放大视图

library(ggplot2)
library(dplyr)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(RColorBrewer)
library(gridExtra)
library(grid)
library(scales)

cat("=== 创建改进图例的完整布局地图 ===\n")

# 读取数据
stroke_data <- read.csv("脑卒中65岁以上人群发病率最终分析表_1990_2021.csv", 
                       stringsAsFactors = FALSE, 
                       fileEncoding = "UTF-8")

region_aapc <- stroke_data %>%
  filter(分类 %in% c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", 
                    "高收入", "中欧、东欧和中亚", "拉丁美洲和加勒比海", 
                    "北非和中东")) %>%
  select(地区 = 分类, AAPC = `AAPC...`)

# 获取世界地图
world <- ne_countries(scale = "medium", returnclass = "sf")

# 创建国家映射（简化版本以节省空间）
create_country_mapping <- function(world_data) {
  country_names <- world_data$name_en
  
  mapping <- data.frame(
    country = country_names,
    region = case_when(
      country_names %in% c("United States of America", "Canada", "Germany", "France", "United Kingdom", 
                          "Italy", "Spain", "Netherlands", "Belgium", "Switzerland", 
                          "Austria", "Sweden", "Norway", "Denmark", "Finland", "Ireland", 
                          "Portugal", "Greece", "Australia", "New Zealand", "Japan", 
                          "South Korea", "Israel", "Luxembourg", "Iceland", "Czech Republic",
                          "Slovenia", "Estonia", "Latvia", "Lithuania", "Slovakia", "Poland",
                          "Hungary", "Croatia", "Cyprus", "Malta") ~ "高收入",
      
      country_names %in% c("Nigeria", "Ethiopia", "South Africa", "Kenya", "Uganda", "Tanzania", 
                          "Ghana", "Mozambique", "Madagascar", "Cameroon", "Angola", "Niger", 
                          "Burkina Faso", "Mali", "Malawi", "Zambia", "Somalia", "Senegal", 
                          "Chad", "Zimbabwe", "Guinea", "Rwanda", "Benin", "Burundi", 
                          "South Sudan", "Togo", "Sierra Leone", "Liberia", "Botswana", 
                          "Namibia", "Gabon", "Lesotho", "Gambia", "Guinea-Bissau", 
                          "Equatorial Guinea", "Mauritius", "Eswatini", "Djibouti", 
                          "Comoros", "Cape Verde", "São Tomé and Príncipe", "Seychelles",
                          "Central African Republic", "Republic of the Congo", 
                          "Democratic Republic of the Congo", "Ivory Coast", "Mauritania") ~ "撒哈拉以南非洲",
      
      country_names %in% c("China", "Indonesia", "Philippines", "Vietnam", "Thailand", 
                          "Myanmar", "Malaysia", "Cambodia", "Laos", "Singapore", 
                          "Brunei", "East Timor", "Papua New Guinea", "Fiji", 
                          "Solomon Islands", "Vanuatu", "Samoa", "Tonga", "Kiribati", 
                          "Tuvalu", "Nauru", "Palau", "Marshall Islands", 
                          "Federated States of Micronesia", "Mongolia", "North Korea") ~ "东南亚、东亚和大洋洲",
      
      country_names %in% c("India", "Pakistan", "Bangladesh", "Afghanistan", "Nepal", 
                          "Sri Lanka", "Bhutan", "Maldives") ~ "南亚",
      
      country_names %in% c("Russia", "Ukraine", "Belarus", "Moldova", "Romania", "Bulgaria", 
                          "Serbia", "Bosnia and Herzegovina", "Montenegro", "Albania", 
                          "North Macedonia", "Kosovo", "Kazakhstan", "Uzbekistan", 
                          "Turkmenistan", "Kyrgyzstan", "Tajikistan", "Armenia", 
                          "Azerbaijan", "Georgia") ~ "中欧、东欧和中亚",
      
      country_names %in% c("Brazil", "Mexico", "Argentina", "Colombia", "Peru", "Venezuela", 
                          "Chile", "Ecuador", "Guatemala", "Cuba", "Bolivia", "Haiti", 
                          "Dominican Republic", "Honduras", "Paraguay", "Nicaragua", 
                          "Costa Rica", "Panama", "Uruguay", "Jamaica", "Trinidad and Tobago", 
                          "Bahamas", "Belize", "Barbados", "Saint Lucia", "Grenada", 
                          "Saint Vincent and the Grenadines", "Antigua and Barbuda", 
                          "Dominica", "Saint Kitts and Nevis", "Suriname", "Guyana",
                          "El Salvador") ~ "拉丁美洲和加勒比海",
      
      country_names %in% c("Egypt", "Iran", "Turkey", "Iraq", "Saudi Arabia", "Yemen", 
                          "Syria", "Jordan", "Lebanon", "Libya", "Tunisia", "Algeria", 
                          "Morocco", "Sudan", "Oman", "Kuwait", "United Arab Emirates", 
                          "Qatar", "Bahrain") ~ "北非和中东",
      
      TRUE ~ "其他"
    ),
    stringsAsFactors = FALSE
  )
  
  return(mapping)
}

country_mapping <- create_country_mapping(world)
world_with_aapc <- world %>%
  left_join(country_mapping, by = c("name_en" = "country")) %>%
  left_join(region_aapc, by = c("region" = "地区"))

# 创建改进的颜色方案
aapc_range <- range(region_aapc$AAPC, na.rm = TRUE)
breaks <- c(-2.5, -2.0, -1.5, -1.0, -0.5, 0)
colors <- c("#08519C", "#3182BD", "#6BAED6", "#9ECAE1", "#C6DBEF", "#F7FBFF")

# 创建主地图（改进图例，适合完整布局）
main_map_layout <- ggplot(world_with_aapc) +
  geom_sf(aes(fill = AAPC), color = "white", size = 0.1) +
  scale_fill_gradientn(
    colors = colors,
    values = scales::rescale(breaks),
    name = "年均百分比变化 (AAPC)\n1990-2021年",
    na.value = "grey90",
    breaks = c(-2.0, -1.5, -1.0, -0.5),
    labels = c("-2.0%", "-1.5%", "-1.0%", "-0.5%"),
    guide = guide_colorbar(
      title.position = "top",
      title.hjust = 0.5,
      title.theme = element_text(size = 12, face = "bold"),
      label.theme = element_text(size = 10),
      barwidth = 18,
      barheight = 1.2,
      frame.colour = "black",
      frame.linewidth = 0.5,
      ticks.colour = "black",
      ticks.linewidth = 0.5
    )
  ) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.margin = margin(t = 10, b = 5),
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 12, hjust = 0.5),
    plot.margin = margin(5, 5, 5, 5)
  ) +
  labs(
    title = "脑卒中65岁以上人群发病率变化趋势 (1990-2021)",
    subtitle = "年均百分比变化 (AAPC) 按地区分布 | 负值表示发病率下降"
  )

# 定义六个局部放大区域
regions <- list(
  list(name = "Caribbean and\nCentral America", xlim = c(-95, -55), ylim = c(5, 30)),
  list(name = "Persian Gulf", xlim = c(45, 60), ylim = c(22, 32)),
  list(name = "Balkan Peninsula", xlim = c(12, 30), ylim = c(38, 48)),
  list(name = "South East Asia", xlim = c(90, 145), ylim = c(-15, 25)),
  list(name = "West Africa &\nEastern Mediterranean", xlim = c(-20, 45), ylim = c(10, 40)),
  list(name = "Northern Europe", xlim = c(-10, 35), ylim = c(50, 72))
)

# 创建局部放大地图（改进版本）
create_enhanced_zoom_map <- function(region_info) {
  ggplot(world_with_aapc) +
    geom_sf(aes(fill = AAPC), color = "white", size = 0.15) +
    scale_fill_gradientn(
      colors = colors,
      values = scales::rescale(breaks),
      na.value = "grey90",
      guide = "none"
    ) +
    coord_sf(
      xlim = region_info$xlim,
      ylim = region_info$ylim,
      expand = FALSE
    ) +
    theme_void() +
    theme(
      plot.title = element_text(size = 9, face = "bold", hjust = 0.5),
      plot.margin = margin(2, 2, 2, 2),
      panel.border = element_rect(color = "black", fill = NA, linewidth = 0.8)
    ) +
    labs(title = region_info$name)
}

zoom_maps <- lapply(regions, create_enhanced_zoom_map)

cat("地图创建完成\n")

# 创建完整布局
complete_enhanced_layout <- grid.arrange(
  main_map_layout,
  arrangeGrob(
    zoom_maps[[1]], zoom_maps[[2]], zoom_maps[[3]],
    zoom_maps[[4]], zoom_maps[[5]], zoom_maps[[6]],
    ncol = 3, nrow = 2
  ),
  heights = c(3, 2),
  ncol = 1
)

# 保存改进图例的完整布局
ggsave("stroke_incidence_enhanced_complete_layout_1990_2021.png", 
       complete_enhanced_layout, 
       width = 22, height = 18, dpi = 300, bg = "white")

cat("改进图例的完整布局已保存: stroke_incidence_enhanced_complete_layout_1990_2021.png\n")

# 创建单独的改进图例说明
create_legend_guide <- function() {
  # 创建图例说明
  legend_data <- data.frame(
    AAPC = c(-2.1, -1.8, -1.5, -1.2, -1.0, -0.8, -0.5),
    label = c("快速下降", "快速下降", "中等下降", "中等下降", "缓慢下降", "缓慢下降", "极缓慢下降"),
    color = c("#08519C", "#08519C", "#3182BD", "#3182BD", "#6BAED6", "#9ECAE1", "#C6DBEF")
  )
  
  ggplot(legend_data, aes(x = 1, y = AAPC, fill = color)) +
    geom_col(width = 0.5, color = "black") +
    geom_text(aes(label = paste0(AAPC, "%\n", label)), 
              hjust = -0.1, size = 3.5, fontface = "bold") +
    scale_fill_identity() +
    theme_void() +
    theme(
      plot.title = element_text(size = 14, face = "bold", hjust = 0.5),
      plot.subtitle = element_text(size = 12, hjust = 0.5),
      plot.margin = margin(10, 10, 10, 10)
    ) +
    labs(
      title = "AAPC 图例说明",
      subtitle = "颜色越深蓝 = 下降越快"
    ) +
    coord_flip() +
    xlim(0.5, 3)
}

legend_guide <- create_legend_guide()

ggsave("stroke_aapc_enhanced_legend_guide.png", 
       legend_guide, 
       width = 8, height = 6, dpi = 300, bg = "white")

cat("改进的图例说明已保存: stroke_aapc_enhanced_legend_guide.png\n")

cat("\n=== 改进图例的完整布局创建完成 ===\n")
cat("主要改进:\n")
cat("1. 图例标题更清晰: '年均百分比变化 (AAPC)'\n")
cat("2. 图例更长更明显: barwidth = 18, barheight = 1.2\n")
cat("3. 添加了边框和刻度线\n")
cat("4. 字体大小优化\n")
cat("5. 包含六个局部放大视图\n")

cat("\n推荐使用文件:\n")
cat("• stroke_incidence_enhanced_complete_layout_1990_2021.png (主要文件)\n")
cat("• stroke_aapc_enhanced_legend_guide.png (图例说明)\n")
