# 脑卒中65岁以上人群DALYs年均变化率地图 - 精细颜色分级版本 (1990-2021)
# 使用十分位数实现均匀的颜色分布

library(dplyr)
library(ggplot2)
library(maps)
library(viridis)
library(RColorBrewer)
library(gridExtra)
library(grid)

# 1. 加载处理好的数据
cat("Loading processed AAPC data for fine color grading...\n")
country_aapc <- read.csv("stroke_dalys_65plus_aapc_results_1990_2021.csv")

cat("Creating fine-grained color classification...\n")
cat("Total countries:", nrow(country_aapc), "\n")
cat("AAPC range:", round(min(country_aapc$aapc), 2), "to", round(max(country_aapc$aapc), 2), "\n")

# 2. 使用十分位数创建精细分级
decile_breaks <- quantile(country_aapc$aapc, probs = seq(0, 1, 0.1))
decile_breaks[1] <- decile_breaks[1] - 0.01  # 确保包含最小值
decile_breaks[11] <- decile_breaks[11] + 0.01  # 确保包含最大值

# 创建更简洁的标签
decile_labels <- c(
  "Fastest decline", "Very fast decline", "Fast decline", "Moderate decline", 
  "Mild decline", "Slight decline", "Minimal decline", "Minimal change", 
  "Slight increase", "Moderate increase"
)

# 或者使用数值标签
numeric_labels <- c()
for(i in 1:(length(decile_breaks)-1)) {
  numeric_labels <- c(numeric_labels, 
                     sprintf("%.2f to %.2f", decile_breaks[i], decile_breaks[i+1]))
}

cat("Decile breaks:\n")
for(i in 1:length(decile_breaks)) {
  cat(sprintf("  %6.2f\n", decile_breaks[i]))
}

# 3. 国家名称标准化
country_mapping <- data.frame(
  gbd_name = c("美国", "英国", "俄罗斯", "韩国", "伊朗伊斯兰共和国", "叙利亚", "委内瑞拉", "玻利维亚", 
               "坦桑尼亚", "刚果民主共和国", "老挝", "越南", "文莱", "北马其顿", "摩尔多瓦",
               "中国", "日本", "德国", "法国", "意大利", "西班牙", "加拿大", "澳大利亚",
               "巴西", "印度", "南非", "埃及", "土耳其", "沙特阿拉伯", "阿联酋"),
  map_name = c("USA", "UK", "Russia", "South Korea", "Iran", "Syria", "Venezuela", "Bolivia",
               "Tanzania", "Democratic Republic of the Congo", "Laos", "Vietnam", "Brunei", 
               "North Macedonia", "Moldova", "China", "Japan", "Germany", "France", "Italy", 
               "Spain", "Canada", "Australia", "Brazil", "India", "South Africa", "Egypt", 
               "Turkey", "Saudi Arabia", "UAE"),
  stringsAsFactors = FALSE
)

# 获取世界地图数据
world_map <- map_data("world")

# 标准化国家名称
country_aapc$region <- country_aapc$location_name
for(i in 1:nrow(country_mapping)) {
  country_aapc$region[country_aapc$location_name == country_mapping$gbd_name[i]] <- country_mapping$map_name[i]
}

# 合并地图数据
map_data_with_aapc <- merge(world_map, country_aapc, by = "region", all.x = TRUE)
map_data_with_aapc <- map_data_with_aapc[order(map_data_with_aapc$order), ]

# 4. 应用十分位数分级
map_data_with_aapc$aapc_decile <- cut(map_data_with_aapc$aapc, 
                                     breaks = decile_breaks, 
                                     labels = numeric_labels,
                                     include.lowest = TRUE)

# 检查分布
cat("Fine color distribution (each ~20 countries):\n")
decile_dist <- table(map_data_with_aapc$aapc_decile, useNA = "ifany")
print(decile_dist)

# 5. 创建精细的10级蓝-红渐变色带
# 使用RdYlBu调色板的反向，确保蓝色表示下降，红色表示上升
fine_colors <- rev(brewer.pal(10, "RdYlBu"))

# 或者使用自定义的精细渐变
custom_colors <- c(
  "#08306b", "#08519c", "#2171b5", "#4292c6", "#6baed6",
  "#c6dbef", "#fee0d2", "#fcbba1", "#fc9272", "#de2d26"
)

# 6. 创建主地图
cat("Creating fine-grained main world map...\n")
main_map_fine <- ggplot(map_data_with_aapc, aes(x = long, y = lat, group = group)) +
  geom_polygon(aes(fill = aapc_decile), color = "white", linewidth = 0.1) +
  scale_fill_manual(values = custom_colors, 
                   name = "AAPC (%)",
                   na.value = "grey90",
                   drop = FALSE) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 8, angle = 45, hjust = 1),
    legend.key.size = unit(0.6, "cm"),
    legend.key.width = unit(1.0, "cm"),
    plot.margin = margin(5, 5, 5, 5, "mm")
  ) +
  guides(fill = guide_legend(nrow = 2, byrow = TRUE, 
                            title.position = "top", title.hjust = 0.5)) +
  coord_fixed(1.3)

# 7. 创建区域放大地图函数
create_fine_regional_map <- function(xlim, ylim, title) {
  ggplot(map_data_with_aapc, aes(x = long, y = lat, group = group)) +
    geom_polygon(aes(fill = aapc_decile), color = "white", linewidth = 0.2) +
    scale_fill_manual(values = custom_colors, na.value = "grey90", guide = "none") +
    coord_fixed(1.3, xlim = xlim, ylim = ylim) +
    theme_void() +
    theme(
      plot.title = element_text(size = 10, hjust = 0.5, face = "bold"),
      plot.margin = margin(2, 2, 2, 2, "mm")
    ) +
    ggtitle(title)
}

# 8. 创建6个区域放大地图
cat("Creating fine-grained regional zoom maps...\n")

caribbean_fine <- create_fine_regional_map(c(-95, -55), c(5, 35), "Caribbean and Central America")
persian_gulf_fine <- create_fine_regional_map(c(45, 65), c(20, 40), "Persian Gulf")
balkans_fine <- create_fine_regional_map(c(10, 30), c(35, 50), "Balkan Peninsula")
southeast_asia_fine <- create_fine_regional_map(c(90, 140), c(-15, 25), "South East Asia")
west_africa_med_fine <- create_fine_regional_map(c(-20, 45), c(0, 40), "West Africa & Eastern Mediterranean")
northern_europe_fine <- create_fine_regional_map(c(-10, 35), c(50, 75), "Northern Europe")

# 9. 组合所有地图
cat("Combining all fine-grained maps into final layout...\n")

regional_grid_fine <- grid.arrange(
  caribbean_fine, persian_gulf_fine, balkans_fine,
  southeast_asia_fine, west_africa_med_fine, northern_europe_fine,
  ncol = 3, nrow = 2
)

final_plot_fine <- grid.arrange(
  main_map_fine,
  regional_grid_fine,
  ncol = 1,
  heights = c(2.5, 1),
  top = textGrob("Stroke DALYs AAPC in Population Aged ≥65 Years (1990-2021)\nFine-Grained Color Classification", 
                gp = gpar(fontsize = 16, fontface = "bold"))
)

# 10. 保存精细分级地图
cat("Saving fine-grained color maps...\n")

ggsave("stroke_dalys_65plus_fine_colors_complete_layout_1990_2021.png", 
       final_plot_fine, width = 20, height = 16, dpi = 300, bg = "white")

ggsave("stroke_dalys_65plus_fine_colors_main_map_1990_2021.png", 
       main_map_fine, width = 18, height = 12, dpi = 300, bg = "white")

# 保存分级信息
decile_info <- data.frame(
  level = 1:10,
  range = numeric_labels,
  color = custom_colors,
  countries_count = as.numeric(table(country_aapc$aapc_decile <- cut(country_aapc$aapc, 
                                                                    breaks = decile_breaks, 
                                                                    include.lowest = TRUE)))
)

write.csv(decile_info, "stroke_dalys_65plus_fine_color_legend_1990_2021.csv", row.names = FALSE)

cat("Fine-grained color visualization completed successfully!\n")
cat("Files saved:\n")
cat("- stroke_dalys_65plus_fine_colors_complete_layout_1990_2021.png\n")
cat("- stroke_dalys_65plus_fine_colors_main_map_1990_2021.png\n")
cat("- stroke_dalys_65plus_fine_color_legend_1990_2021.csv\n")
cat("\nEach color level now contains approximately 20 countries for better visual distribution!\n")
