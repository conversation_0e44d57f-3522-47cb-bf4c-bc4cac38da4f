# 简化的区域地图测试
library(ggplot2)
library(dplyr)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(gridExtra)
library(grid)

# 获取世界地图数据
world <- ne_countries(scale = "medium", returnclass = "sf")

# 为测试目的，创建一些虚拟的AAPC数据
set.seed(123)
world$aapc_value <- runif(nrow(world), -1.5, 1.5)

# 创建AAPC分组
world <- world %>%
  mutate(
    aapc_group = case_when(
      aapc_value < -1.0 ~ "< -1.0",
      aapc_value >= -1.0 & aapc_value < -0.5 ~ "-1.0 to < -0.5",
      aapc_value >= -0.5 & aapc_value < 0 ~ "-0.5 to < 0",
      aapc_value >= 0 & aapc_value < 0.5 ~ "0 to < 0.5",
      aapc_value >= 0.5 & aapc_value < 1.0 ~ "0.5 to < 1.0",
      aapc_value >= 1.0 & aapc_value < 1.5 ~ "1.0 to < 1.5",
      aapc_value >= 1.5 ~ "≥ 1.5"
    ),
    aapc_group = factor(aapc_group, 
                       levels = c("< -1.0", "-1.0 to < -0.5", "-0.5 to < 0", 
                                 "0 to < 0.5", "0.5 to < 1.0", "1.0 to < 1.5", 
                                 "≥ 1.5"))
  )

# 创建颜色调色板
colors <- c("#08519c", "#3182bd", "#6baed6", "#c6dbef", 
           "#fee0d2", "#fc9272", "#de2d26")
names(colors) <- levels(world$aapc_group)

# 创建区域地图函数
create_regional_map <- function(world_data, xlim, ylim, title, colors) {
  ggplot(world_data) +
    geom_sf(aes(fill = aapc_group), color = "white", linewidth = 0.2) +
    scale_fill_manual(values = colors, name = "AAPC (%)", drop = FALSE) +
    coord_sf(xlim = xlim, ylim = ylim, expand = FALSE) +
    theme_void() +
    theme(
      legend.position = "none",
      plot.title = element_text(size = 12, face = "bold", hjust = 0.5),
      plot.margin = margin(5, 5, 5, 5),
      panel.border = element_rect(color = "black", fill = NA, linewidth = 1)
    ) +
    labs(title = title)
}

# 定义区域
regions <- list(
  caribbean = list(xlim = c(-90, -55), ylim = c(10, 30), 
                  title = "Caribbean"),
  persian_gulf = list(xlim = c(45, 60), ylim = c(22, 32), 
                     title = "Persian Gulf"),
  balkans = list(xlim = c(12, 30), ylim = c(40, 48), 
                title = "Balkans"),
  southeast_asia = list(xlim = c(90, 140), ylim = c(-10, 25), 
                       title = "Southeast Asia"),
  west_africa = list(xlim = c(-20, 20), ylim = c(0, 20), 
                    title = "West Africa"),
  northern_europe = list(xlim = c(-10, 35), ylim = c(55, 72), 
                        title = "Northern Europe")
)

# 创建区域地图
regional_maps <- list()
for(i in 1:length(regions)) {
  region_name <- names(regions)[i]
  region_info <- regions[[i]]
  
  regional_maps[[region_name]] <- create_regional_map(
    world, 
    region_info$xlim, 
    region_info$ylim, 
    region_info$title, 
    colors
  )
}

# 测试单个地图
print("测试单个地图...")
ggsave("test_single_caribbean.png", regional_maps$caribbean, 
       width = 8, height = 6, dpi = 300, bg = "white")
print("单个加勒比海地图已保存")

# 测试组合地图
print("创建组合地图...")

# 方法1: 直接使用grid.arrange并保存
png("test_combined_regional_maps_v1.png", width = 16, height = 10, 
    units = "in", res = 300)

grid.arrange(
  regional_maps$caribbean, regional_maps$persian_gulf,
  regional_maps$balkans, regional_maps$southeast_asia,
  regional_maps$west_africa, regional_maps$northern_europe,
  ncol = 3, nrow = 2,
  top = textGrob("Regional Focus Areas: Stroke Mortality AAPC (1990-2019)", 
                 gp = gpar(fontsize = 14, fontface = "bold"))
)

dev.off()

print("组合地图方法1已保存: test_combined_regional_maps_v1.png")

# 方法2: 使用arrangeGrob
combined_grob <- arrangeGrob(
  regional_maps$caribbean, regional_maps$persian_gulf,
  regional_maps$balkans, regional_maps$southeast_asia,
  regional_maps$west_africa, regional_maps$northern_europe,
  ncol = 3, nrow = 2,
  top = textGrob("Regional Focus Areas: Stroke Mortality AAPC (1990-2019)", 
                 gp = gpar(fontsize = 14, fontface = "bold"))
)

ggsave("test_combined_regional_maps_v2.png", combined_grob,
       width = 16, height = 10, dpi = 300, bg = "white")

print("组合地图方法2已保存: test_combined_regional_maps_v2.png")

print("测试完成！")
