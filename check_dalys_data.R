# 检查脑卒中DALYs数据详细信息
library(dplyr)

# DALYs数据文件
dalys_files <- c(
  '死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-3/IHME-GBD_2021_DATA-336925ca-3.csv',
  '死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-4/IHME-GBD_2021_DATA-336925ca-4.csv'
)

all_dalys_data <- data.frame()

for(i in 1:length(dalys_files)) {
  file <- dalys_files[i]
  cat('Processing file', i, ':', basename(file), '\n')
  
  # 读取完整数据
  data <- read.csv(file)
  cat('Total rows:', nrow(data), '\n')
  cat('Year range:', min(data$year), '-', max(data$year), '\n')
  cat('Unique age groups:', paste(unique(data$age_name), collapse=", "), '\n')
  cat('Countries count:', length(unique(data$location_name)), '\n')
  
  # 检查65岁以上相关年龄组
  age_65plus <- data %>% 
    filter(grepl("65|70|75|80|85|全部|年龄标准化", age_name)) %>%
    select(age_name) %>%
    distinct()
  
  cat('65+ related age groups:', paste(age_65plus$age_name, collapse=", "), '\n\n')
  
  # 合并数据
  all_dalys_data <- rbind(all_dalys_data, data)
}

cat('Combined data summary:\n')
cat('Total rows:', nrow(all_dalys_data), '\n')
cat('Year range:', min(all_dalys_data$year), '-', max(all_dalys_data$year), '\n')
cat('Countries:', length(unique(all_dalys_data$location_name)), '\n')

# 检查是否有1990-2021年的完整数据
years_available <- sort(unique(all_dalys_data$year))
cat('Available years:', paste(years_available, collapse=", "), '\n')

# 检查65岁以上年龄组
age_65plus_all <- all_dalys_data %>% 
  filter(grepl("65|70|75|80|85", age_name)) %>%
  select(age_name) %>%
  distinct()

cat('All 65+ age groups:', paste(age_65plus_all$age_name, collapse=", "), '\n')
