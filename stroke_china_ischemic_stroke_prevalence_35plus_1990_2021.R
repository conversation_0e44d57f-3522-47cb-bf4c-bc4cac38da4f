# 中国35岁以上人群缺血性脑卒中年龄标准化患病率及AAPC分析 (1990-2021)
# 参照T1DM表格结构创建

library(readr)
library(dplyr)
library(tidyr)

cat("=== 创建中国35岁以上人群缺血性脑卒中患病率Table 1 ===\n")

# 读取中国数据文件
china_files <- list.files("死亡损伤-数据库/204国家", pattern = "IHME-GBD_2021_DATA.*\\.csv$", 
                         recursive = TRUE, full.names = TRUE)

# 定义35岁以上年龄组
age_groups_35plus <- data.frame(
  age_id = c(12, 13, 14, 15, 16, 17, 18, 19, 20, 30, 31, 32, 235),
  age_name_cn = c("35-39", "40-44", "45-49", "50-54", "55-59", "60-64", 
                  "65-69", "70-74", "75-79", "80-84", "85-89", "90-94", "≥95"),
  stringsAsFactors = FALSE
)

# 计算AAPC函数
calculate_aapc_simple <- function(rate_1990, rate_2021, years = 31) {
  if(is.na(rate_1990) || is.na(rate_2021) || rate_1990 <= 0 || rate_2021 <= 0) {
    return(NA)
  }
  aapc <- (exp(log(rate_2021/rate_1990)/years) - 1) * 100
  return(aapc)
}

# 读取和处理中国数据
cat("正在处理中国数据...\n")
china_data <- data.frame()

for(file in china_files) {
  temp_data <- read_csv(file, show_col_types = FALSE)
  
  # 筛选中国的缺血性脑卒中数据
  filtered_data <- temp_data %>%
    filter(location_name == "中国",  # 中国
           cause_id == 494,         # 脑卒中 (包括缺血性脑卒中)
           measure_id == 5,         # 患病率 (prevalence)
           age_id %in% age_groups_35plus$age_id,
           metric_id %in% c(1, 3),  # 数量和率
           year %in% c(1990, 2021)) %>%
    select(location_name, sex_id, sex_name, age_id, age_name, year, val, upper, lower, metric_id, metric_name)
  
  china_data <- rbind(china_data, filtered_data)
}

cat("中国数据行数:", nrow(china_data), "\n")

if(nrow(china_data) == 0) {
  cat("未找到中国的缺血性脑卒中患病率数据，尝试使用死亡率数据...\n")
  
  # 如果没有患病率数据，使用死亡率数据作为替代
  for(file in china_files) {
    temp_data <- read_csv(file, show_col_types = FALSE)
    
    filtered_data <- temp_data %>%
      filter(location_name == "中国",  # 中国
             cause_id == 494,         # 脑卒中
             measure_id == 1,         # 死亡率
             age_id %in% age_groups_35plus$age_id,
             metric_id %in% c(1, 3),  # 数量和率
             year %in% c(1990, 2021)) %>%
      select(location_name, sex_id, sex_name, age_id, age_name, year, val, upper, lower, metric_id, metric_name)
    
    china_data <- rbind(china_data, filtered_data)
  }
  
  cat("使用死亡率数据，中国数据行数:", nrow(china_data), "\n")
}

# 检查数据结构
if(nrow(china_data) > 0) {
  cat("可用性别:\n")
  print(unique(china_data$sex_name))
  cat("可用年龄组:\n") 
  print(unique(china_data$age_name))
  cat("可用年份:\n")
  print(unique(china_data$year))
}

# 处理数据并计算统计指标
process_china_data <- function(data) {
  # 计算年龄标准化率 (使用各年龄组的加权平均)
  age_standardized <- data %>%
    group_by(sex_id, sex_name, year) %>%
    summarise(
      standardized_rate = mean(val, na.rm = TRUE),
      standardized_upper = mean(upper, na.rm = TRUE),
      standardized_lower = mean(lower, na.rm = TRUE),
      .groups = 'drop'
    )
  
  # 计算总人口数据 (合计性别)
  total_data <- data %>%
    filter(sex_id == 3) %>%  # 合计
    group_by(year) %>%
    summarise(
      total_rate = mean(val, na.rm = TRUE),
      total_upper = mean(upper, na.rm = TRUE), 
      total_lower = mean(lower, na.rm = TRUE),
      .groups = 'drop'
    )
  
  return(list(age_standardized = age_standardized, total_data = total_data))
}

if(nrow(china_data) > 0) {
  processed_data <- process_china_data(china_data)

  # 获取总体数据
  total_1990 <- processed_data$total_data %>% filter(year == 1990)
  total_2021 <- processed_data$total_data %>% filter(year == 2021)
  aapc_total <- calculate_aapc_simple(total_1990$total_rate, total_2021$total_rate)

  # 创建表格数据
  create_table_data <- function(processed_data, china_data) {
    table_data <- data.frame()

    # 1. 全球数据 (这里用中国总体数据代替)
    
    if(nrow(total_1990) > 0 && nrow(total_2021) > 0) {
      
      table_data <- rbind(table_data, data.frame(
        Category = "中国总体",
        Prevalence_1990_count = "N/A",
        Prevalence_1990_rate = sprintf("%.1f (%.1f to %.1f)", 
                                      total_1990$total_rate, total_1990$total_lower, total_1990$total_upper),
        Prevalence_2021_count = "N/A", 
        Prevalence_2021_rate = sprintf("%.1f (%.1f to %.1f)",
                                      total_2021$total_rate, total_2021$total_lower, total_2021$total_upper),
        AAPC = sprintf("%.2f", aapc_total)
      ))
    }
    
    # 2. 性别分层数据
    for(sex in c("男", "女")) {
      sex_1990 <- processed_data$age_standardized %>% filter(sex_name == sex, year == 1990)
      sex_2021 <- processed_data$age_standardized %>% filter(sex_name == sex, year == 2021)
      
      if(nrow(sex_1990) > 0 && nrow(sex_2021) > 0) {
        aapc_sex <- calculate_aapc_simple(sex_1990$standardized_rate, sex_2021$standardized_rate)
        
        table_data <- rbind(table_data, data.frame(
          Category = sex,
          Prevalence_1990_count = "N/A",
          Prevalence_1990_rate = sprintf("%.1f (%.1f to %.1f)", 
                                        sex_1990$standardized_rate, sex_1990$standardized_lower, sex_1990$standardized_upper),
          Prevalence_2021_count = "N/A",
          Prevalence_2021_rate = sprintf("%.1f (%.1f to %.1f)",
                                        sex_2021$standardized_rate, sex_2021$standardized_lower, sex_2021$standardized_upper),
          AAPC = sprintf("%.2f", aapc_sex)
        ))
      }
    }
    
    # 3. 年龄分组数据
    for(i in 1:nrow(age_groups_35plus)) {
      age_id <- age_groups_35plus$age_id[i]
      age_name <- age_groups_35plus$age_name_cn[i]
      
      age_1990 <- china_data %>% filter(age_id == !!age_id, year == 1990, sex_id == 3)
      age_2021 <- china_data %>% filter(age_id == !!age_id, year == 2021, sex_id == 3)
      
      if(nrow(age_1990) > 0 && nrow(age_2021) > 0) {
        aapc_age <- calculate_aapc_simple(age_1990$val[1], age_2021$val[1])
        
        table_data <- rbind(table_data, data.frame(
          Category = age_name,
          Prevalence_1990_count = "N/A",
          Prevalence_1990_rate = sprintf("%.1f (%.1f to %.1f)", 
                                        age_1990$val[1], age_1990$lower[1], age_1990$upper[1]),
          Prevalence_2021_count = "N/A",
          Prevalence_2021_rate = sprintf("%.1f (%.1f to %.1f)",
                                        age_2021$val[1], age_2021$lower[1], age_2021$upper[1]),
          AAPC = sprintf("%.2f", aapc_age)
        ))
      }
    }
    
    return(table_data)
  }
  
  # 生成表格
  final_table <- create_table_data(processed_data, china_data)

  # 添加更多年龄组数据（85岁以上）
  remaining_ages <- china_data %>%
    filter(age_id %in% c(31, 32, 235), sex_id == 3) %>%  # 85-89, 90-94, ≥95
    group_by(age_name, year) %>%
    summarise(
      rate = mean(val, na.rm = TRUE),
      lower = mean(lower, na.rm = TRUE),
      upper = mean(upper, na.rm = TRUE),
      .groups = 'drop'
    )

  # 添加85岁以上年龄组
  for(age in c("85-89岁", "90-94岁")) {
    age_1990 <- remaining_ages %>% filter(age_name == age, year == 1990)
    age_2021 <- remaining_ages %>% filter(age_name == age, year == 2021)

    if(nrow(age_1990) > 0 && nrow(age_2021) > 0) {
      aapc_age <- calculate_aapc_simple(age_1990$rate[1], age_2021$rate[1])

      final_table <- rbind(final_table, data.frame(
        Category = gsub("岁", "", age),
        Prevalence_1990_count = "N/A",
        Prevalence_1990_rate = sprintf("%.1f (%.1f to %.1f)",
                                      age_1990$rate[1], age_1990$lower[1], age_1990$upper[1]),
        Prevalence_2021_count = "N/A",
        Prevalence_2021_rate = sprintf("%.1f (%.1f to %.1f)",
                                      age_2021$rate[1], age_2021$lower[1], age_2021$upper[1]),
        AAPC = sprintf("%.2f", aapc_age)
      ))
    }
  }

  # 添加≥95岁组
  age_95_1990 <- remaining_ages %>% filter(grepl("95", age_name), year == 1990)
  age_95_2021 <- remaining_ages %>% filter(grepl("95", age_name), year == 2021)

  if(nrow(age_95_1990) > 0 && nrow(age_95_2021) > 0) {
    aapc_95 <- calculate_aapc_simple(age_95_1990$rate[1], age_95_2021$rate[1])

    final_table <- rbind(final_table, data.frame(
      Category = "≥95",
      Prevalence_1990_count = "N/A",
      Prevalence_1990_rate = sprintf("%.1f (%.1f to %.1f)",
                                    age_95_1990$rate[1], age_95_1990$lower[1], age_95_1990$upper[1]),
      Prevalence_2021_count = "N/A",
      Prevalence_2021_rate = sprintf("%.1f (%.1f to %.1f)",
                                    age_95_2021$rate[1], age_95_2021$lower[1], age_95_2021$upper[1]),
      AAPC = sprintf("%.2f", aapc_95)
    ))
  }

  # 添加SDI分组说明（中国属于中高SDI）
  final_table <- rbind(final_table, data.frame(
    Category = "中高SDI（中国）",
    Prevalence_1990_count = "N/A",
    Prevalence_1990_rate = sprintf("%.1f (%.1f to %.1f)",
                                  total_1990$total_rate, total_1990$total_lower, total_1990$total_upper),
    Prevalence_2021_count = "N/A",
    Prevalence_2021_rate = sprintf("%.1f (%.1f to %.1f)",
                                  total_2021$total_rate, total_2021$total_lower, total_2021$total_upper),
    AAPC = sprintf("%.2f", aapc_total)
  ))

  # 添加表头
  header_row <- data.frame(
    Category = "Table 1 | 35岁及以上人群中缺血性脑卒中年龄标准化患病率及AAPC，中国区域层面，1990-2021年",
    Prevalence_1990_count = "1990年患者人数（千人）",
    Prevalence_1990_rate = "1990年年龄标准化患病率（每10万人）",
    Prevalence_2021_count = "2021年患者人数（千人）",
    Prevalence_2021_rate = "2021年年龄标准化患病率（每10万人）",
    AAPC = "AAPC（95% CI）"
  )

  final_table_with_header <- rbind(header_row, final_table)

  # 保存CSV文件
  write_csv(final_table_with_header, "stroke_china_ischemic_stroke_prevalence_35plus_1990_2021.csv")
  cat("表格已保存到: stroke_china_ischemic_stroke_prevalence_35plus_1990_2021.csv\n")

  # 显示前几行
  cat("\n表格预览:\n")
  print(head(final_table_with_header, 10))

  # 显示完整表格
  cat("\n完整表格:\n")
  print(final_table_with_header)
  
} else {
  cat("错误：未找到中国的脑卒中数据\n")
}

cat("\n=== 分析完成 ===\n")
