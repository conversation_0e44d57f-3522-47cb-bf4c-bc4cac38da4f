# 脑卒中死亡率AAPC颜色方案对比 (1990-2021)
# 展示原始版本vs优化版本的颜色差异

library(ggplot2)
library(dplyr)
library(gridExtra)
library(grid)

# 定义标签
labels <- c("-6.01 to <-4.5", "-4.5 to <-3.5", "-3.5 to <-2.5", "-2.5 to <-1.5", 
            "-1.5 to <-0.5", "-0.5 to <0", "0 to <0.5", "0.5 to <1.0", "1.0 to 1.85")

# 国家数量
counts <- c(6, 11, 35, 40, 68, 23, 13, 4, 4)

# 原始颜色方案（偏蓝）
colors_original <- c("#08306b", "#2171b5", "#4292c6", "#6baed6", "#9ecae1", 
                     "#c6dbef", "#fee391", "#fec44f", "#d94801")

# 优化颜色方案（更均衡）
colors_optimized <- c("#053061", "#2166ac", "#4393c3", "#92c5de", "#d1e5f0",
                      "#fee08b", "#fdae61", "#f46d43", "#a50026")

# 创建对比数据
comparison_data <- data.frame(
  category = rep(factor(labels, levels = labels), 2),
  color = c(colors_original, colors_optimized),
  version = rep(c("Original (Blue-Heavy)", "Optimized (Balanced)"), each = 9),
  y_pos = rep(9:1, 2),
  count = rep(counts, 2)
)

# 创建颜色对比图
color_comparison <- ggplot(comparison_data, aes(x = version, y = y_pos, fill = category)) +
  geom_tile(color = "white", linewidth = 1, width = 0.8, height = 0.8) +
  scale_fill_manual(values = c(colors_original, colors_optimized), guide = "none") +
  geom_text(aes(label = paste0(count, " countries")), 
            size = 3, color = "black", fontface = "bold") +
  scale_y_continuous(breaks = 9:1, labels = labels, expand = c(0.05, 0.05)) +
  theme_minimal() +
  theme(
    axis.text.y = element_text(size = 10, hjust = 0),
    axis.text.x = element_text(size = 12, face = "bold"),
    axis.title = element_blank(),
    plot.title = element_text(size = 14, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 11, hjust = 0.5),
    panel.grid = element_blank(),
    plot.margin = margin(20, 20, 20, 20)
  ) +
  labs(
    title = "Color Scheme Comparison: Stroke Mortality AAPC (1990-2021)",
    subtitle = "Original vs Optimized Color Distribution"
  )

# 创建颜色分布分析图
distribution_data <- data.frame(
  category = factor(labels, levels = labels),
  count = counts,
  percentage = round(counts / sum(counts) * 100, 1)
)

distribution_plot <- ggplot(distribution_data, aes(x = category, y = count)) +
  geom_col(fill = colors_optimized, color = "white", linewidth = 0.5) +
  geom_text(aes(label = paste0(count, "\n(", percentage, "%)")), 
            vjust = -0.5, size = 3, fontface = "bold") +
  theme_minimal() +
  theme(
    axis.text.x = element_text(angle = 45, hjust = 1, size = 9),
    axis.title.y = element_text(size = 12, face = "bold"),
    plot.title = element_text(size = 12, face = "bold", hjust = 0.5),
    panel.grid.major.x = element_blank(),
    plot.margin = margin(10, 10, 10, 10)
  ) +
  labs(
    title = "Data Distribution by AAPC Category",
    x = "AAPC Category (%)",
    y = "Number of Countries"
  ) +
  scale_y_continuous(expand = expansion(mult = c(0, 0.15)))

# 创建颜色特征说明
color_features <- data.frame(
  Feature = c("Color Balance", "Blue Dominance", "Warm Colors", "Contrast", "Readability"),
  Original = c("Poor", "High", "Limited", "Moderate", "Good"),
  Optimized = c("Good", "Reduced", "Enhanced", "High", "Excellent"),
  stringsAsFactors = FALSE
)

# 转换为长格式
color_features_long <- tidyr::pivot_longer(color_features, 
                                           cols = c(Original, Optimized),
                                           names_to = "Version",
                                           values_to = "Rating")

# 创建特征对比表
feature_plot <- ggplot(color_features_long, aes(x = Feature, y = Version, fill = Rating)) +
  geom_tile(color = "white", linewidth = 1) +
  geom_text(aes(label = Rating), size = 4, fontface = "bold") +
  scale_fill_manual(values = c("Poor" = "#d73027", "Moderate" = "#fee08b", 
                               "Good" = "#a6d96a", "Excellent" = "#1a9850",
                               "High" = "#d73027", "Reduced" = "#a6d96a",
                               "Limited" = "#d73027", "Enhanced" = "#a6d96a")) +
  theme_minimal() +
  theme(
    axis.text.x = element_text(angle = 45, hjust = 1, size = 10),
    axis.text.y = element_text(size = 11, face = "bold"),
    axis.title = element_blank(),
    plot.title = element_text(size = 12, face = "bold", hjust = 0.5),
    legend.position = "none",
    panel.grid = element_blank(),
    plot.margin = margin(10, 10, 10, 10)
  ) +
  labs(title = "Color Scheme Feature Comparison")

# 组合所有图表
combined_plot <- grid.arrange(
  color_comparison,
  arrangeGrob(distribution_plot, feature_plot, ncol = 2),
  nrow = 2,
  heights = c(2, 1)
)

# 保存对比图
ggsave("stroke_mortality_color_comparison_1990_2021.png", combined_plot,
       width = 16, height = 12, dpi = 300, bg = "white")

cat("颜色对比图已保存: stroke_mortality_color_comparison_1990_2021.png\n")

# 输出颜色优化总结
cat("\n=== 颜色优化总结 ===\n")
cat("原始方案问题:\n")
cat("1. 蓝色系过于集中，占据了前5个分级\n")
cat("2. 暖色系（黄-橙-红）仅占后4个分级\n")
cat("3. 视觉上偏冷色调，缺乏平衡感\n")

cat("\n优化方案改进:\n")
cat("1. 蓝色系饱和度降低，避免过度偏蓝\n")
cat("2. 增强了黄-橙色系的表现力\n")
cat("3. 整体色彩分布更加均衡\n")
cat("4. 保持了科学可视化的专业性\n")

cat("\n数据分布特点:\n")
cat("- 68个国家(33.3%)在-1.5到-0.5区间，是最大的分组\n")
cat("- 91个国家(44.6%)在-2.5到0区间，占近一半\n")
cat("- 仅8个国家(3.9%)死亡率上升(AAPC>0.5)\n")
cat("- 17个国家(8.3%)死亡率大幅下降(AAPC<-3.5)\n")
