# Fig 4复现指南：全球65岁及以上1型糖尿病患者DALYs年均变化率地图

## 📋 复现概述

本指南详细描述了如何复现Fig 4风格的全球地图，展示1990-2019年65岁及以上1型糖尿病患者DALYs（伤残调整生命年）年均百分比变化（AAPC）的空间分布。

## 🎯 研究目的

展示1990-2019年，全球各国（地区）65岁及以上1型糖尿病患者DALYs年均百分比变化（AAPC）的空间分布，识别疾病负担变化的地理模式。

## 📊 主要内容与技术细节

### 1. 数据准备阶段

#### 1.1 数据收集要求
- **数据来源**: GBD（Global Burden of Disease）数据库、IHME、WHO等
- **时间范围**: 1990-2019年（30年时间序列）
- **人群定义**: 65岁及以上1型糖尿病（T1DM）患者
- **核心指标**: DALYs数值（每年、每个国家/地区）

#### 1.2 数据结构要求
```
Country/Region | Year | DALYs | Population | Age_Group | Disease_Type
China         | 1990 | 1234.5| 1000000   | 65+       | T1DM
China         | 1991 | 1245.2| 1020000   | 65+       | T1DM
...           | ...  | ...   | ...       | ...       | ...
```

#### 1.3 数据质量控制
- **完整性检查**: 确保30年数据连续性
- **缺失值处理**: 插补或删除策略
- **异常值检测**: 识别和处理极端值
- **标准化**: 统一国家/地区名称

### 2. AAPC计算方法

#### 2.1 计算原理
年均变化率（AAPC）反映DALYs在研究期间的平均年度变化趋势：

```r
# 对每个国家/地区分别计算
for(country in countries) {
  # 1. 提取1990-2019年DALYs时间序列
  dalys_series <- extract_dalys_by_country(country)
  
  # 2. log线性回归
  model <- lm(log(dalys_series) ~ year)
  slope <- coef(model)[2]
  
  # 3. 计算AAPC
  aapc <- (exp(slope) - 1) * 100
}
```

#### 2.2 AAPC公式
**AAPC = (e^slope - 1) × 100%**

其中：
- `slope`: log(DALYs)与年份线性回归的斜率
- 负值: DALYs下降，疾病负担减轻
- 正值: DALYs上升，疾病负担加重

#### 2.3 统计显著性
- 计算95%置信区间
- 评估变化趋势的统计显著性
- 标记显著性水平

### 3. 地图可视化技术

#### 3.1 地理数据处理
```r
# 获取世界地图数据
world <- ne_countries(scale = "medium", returnclass = "sf")

# 国家名称标准化
world$name_clean <- countrycode(world$name, "country.name", "country.name")

# 数据合并
world_data <- world %>%
  left_join(aapc_data, by = c("name_clean" = "country"))
```

#### 3.2 Fig 4风格分组策略
**10个AAPC区间**（参考原Fig 4）：
1. `< -1.0%` (深蓝色)
2. `-1.0 to -0.75%` (中蓝色)
3. `-0.75 to -0.5%` (浅蓝色)
4. `-0.5 to -0.25%` (淡蓝色)
5. `-0.25 to 0%` (极淡蓝色)
6. `0 to 0.25%` (淡黄色)
7. `0.25 to 0.5%` (黄色)
8. `0.5 to 0.75%` (橙色)
9. `0.75 to 1.0%` (浅红色)
10. `≥ 1.0%` (深红色)

#### 3.3 颜色编码系统
```r
# Fig 4标准颜色方案：蓝-黄-红渐变
fig4_colors <- c("#08306b", "#08519c", "#2171b5", "#4292c6", "#6baed6",
                "#fee391", "#fec44f", "#fe9929", "#ec7014", "#cc4c02", "#cccccc")
```

### 4. 局部放大实现

#### 4.1 关键区域定义
基于Fig 4的局部放大策略：
- **加勒比海地区**: 小岛国集中区域
- **波斯湾地区**: 中东石油国家
- **巴尔干地区**: 东南欧国家
- **东南亚地区**: 东盟国家
- **西非地区**: 西非经济共同体
- **北欧地区**: 斯堪的纳维亚国家

#### 4.2 技术实现
```r
# 定义放大区域边界
regions <- list(
  caribbean = list(xlim = c(-90, -55), ylim = c(10, 30)),
  persian_gulf = list(xlim = c(45, 60), ylim = c(22, 32)),
  # ... 其他区域
)

# 创建局部地图
regional_map <- ggplot(world_data) +
  geom_sf(aes(fill = aapc_group)) +
  coord_sf(xlim = region$xlim, ylim = region$ylim, expand = FALSE)
```

### 5. 图例设计规范

#### 5.1 Fig 4标准图例
- **布局**: 2行5列（10个分组）
- **位置**: 地图底部居中
- **标题**: "AAPC (%)"
- **字体**: 清晰易读，适当大小

#### 5.2 技术实现
```r
scale_fill_manual(
  values = fig4_colors,
  name = "AAPC (%)",
  drop = FALSE,  # 强制显示所有分组
  guide = guide_legend(
    nrow = 2, byrow = TRUE,
    title.position = "top"
  )
)
```

## 🔧 技术实现要点

### 1. 数据处理关键点
- **时间序列完整性**: 确保30年数据无缺失
- **国家名称匹配**: 使用countrycode包标准化
- **地区分组**: 建立国家到地区的映射关系

### 2. 可视化关键点
- **颜色一致性**: 严格按照Fig 4颜色方案
- **图例完整性**: 使用`drop = FALSE`显示所有分组
- **空间精度**: 使用medium分辨率地图数据

### 3. 质量控制要点
- **数据验证**: 检查AAPC计算结果合理性
- **可视化验证**: 确保颜色映射正确
- **可重现性**: 完整记录所有参数和步骤

## 📈 预期结果

### 1. 主要输出文件
- **主地图**: `stroke_dalys_fig4_main_map.png`
- **局部放大图**: `stroke_dalys_fig4_regional_maps.png`
- **方法学文档**: `stroke_dalys_fig4_methodology.txt`
- **结果摘要**: `stroke_dalys_fig4_results_summary.csv`

### 2. 分析结果示例
基于脑卒中数据的复现结果：
- **改善地区**: 高收入国家、南亚（AAPC < 0）
- **恶化地区**: 撒哈拉以南非洲、北非中东等（AAPC > 0）
- **空间模式**: 发达国家vs发展中国家的明显差异

## 🔄 复现流程总结

1. **数据准备** → 收集GBD数据，整理为标准格式
2. **AAPC计算** → 对每个国家进行log线性回归
3. **数据合并** → 将AAPC结果与地图数据合并
4. **分组着色** → 按Fig 4标准进行10分组着色
5. **主图绘制** → 创建全球主地图
6. **局部放大** → 绘制6个关键区域放大图
7. **结果输出** → 保存图片和分析报告

## 📝 方法学报告要点

复现Fig 4时，方法学部分应包含：
- 数据来源和时间范围
- AAPC计算的具体公式和方法
- 分组策略和颜色编码说明
- 局部放大区域的选择依据
- 质量控制和验证步骤
- 结果解释和局限性讨论

---

**注**: 本指南基于脑卒中数据的实际复现经验，可直接应用于1型糖尿病DALYs数据的Fig 4复现工作。
