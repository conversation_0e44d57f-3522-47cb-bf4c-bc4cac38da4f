# 简单测试脚本 - 检查国家名称匹配
library(ggplot2)
library(dplyr)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)

# 读取脑卒中数据
stroke_data <- read.csv("脑卒中65岁以上人群患病率分析表.csv", 
                       stringsAsFactors = FALSE, 
                       fileEncoding = "UTF-8")

# 提取地区AAPC数据
region_aapc <- stroke_data %>%
  filter(分类 %in% c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", 
                    "高收入", "中欧、东欧和中亚", "拉丁美洲和加勒比海", 
                    "北非和中东")) %>%
  select(
    地区 = 分类,
    AAPC = `AAPC...`
  )

print("地区AAPC数据:")
print(region_aapc)

# 获取世界地图
world <- ne_countries(scale = "medium", returnclass = "sf")

# 查看实际的国家名称
cat("\n实际地图中的国家名称样本:\n")
if("NAME_EN" %in% names(world)) {
  print(head(world$NAME_EN, 20))
} else {
  print("NAME_EN字段不存在，可用字段:")
  print(names(world))
}

cat("\n总共有", nrow(world), "个国家/地区\n")

# 检查可用的字段
cat("\n地图数据的字段:\n")
print(names(world)[1:10])  # 显示前10个字段

# 使用正确的字段名
country_name_field <- if("name_en" %in% names(world)) {
  "name_en"
} else if("NAME_EN" %in% names(world)) {
  "NAME_EN"
} else if("name" %in% names(world)) {
  "name"
} else if("admin" %in% names(world)) {
  "admin"
} else {
  names(world)[1]
}
cat("\n使用字段:", country_name_field, "\n")

# 显示实际的国家名称
cat("\n实际国家名称样本:\n")
print(head(world[[country_name_field]], 20))

# 创建简单的映射 - 基于地理位置的大致分组
create_simple_mapping <- function(world_data, name_field) {

  country_names <- world_data[[name_field]]

  # 根据地理位置和经济发展水平进行简单分组
  mapping <- data.frame(
    country = country_names,
    region = case_when(
      # 高收入国家（主要是欧美发达国家）
      country_names %in% c("United States", "Canada", "Germany", "France", "United Kingdom",
                          "Italy", "Spain", "Netherlands", "Belgium", "Switzerland",
                          "Austria", "Sweden", "Norway", "Denmark", "Finland", "Ireland",
                          "Portugal", "Greece", "Australia", "New Zealand", "Japan",
                          "South Korea", "Israel", "Luxembourg", "Iceland") ~ "高收入",

      # 撒哈拉以南非洲 - 使用更简单的判断
      country_names %in% c("Nigeria", "Ethiopia", "South Africa", "Kenya", "Uganda", "Tanzania",
                          "Ghana", "Mozambique", "Madagascar", "Cameroon", "Angola", "Niger",
                          "Burkina Faso", "Mali", "Malawi", "Zambia", "Somalia", "Senegal",
                          "Chad", "Zimbabwe", "Guinea", "Rwanda", "Benin", "Burundi",
                          "South Sudan", "Togo", "Sierra Leone", "Liberia", "Botswana",
                          "Namibia", "Gabon", "Lesotho") ~ "撒哈拉以南非洲",

      # 南亚
      country_names %in% c("India", "Pakistan", "Bangladesh", "Afghanistan",
                          "Nepal", "Sri Lanka", "Bhutan", "Maldives") ~ "南亚",

      # 东南亚、东亚和大洋洲
      country_names %in% c("China", "Indonesia", "Philippines", "Vietnam", "Thailand",
                          "Myanmar", "Malaysia", "Cambodia", "Laos", "Singapore",
                          "Mongolia", "Brunei", "Timor-Leste", "Papua New Guinea",
                          "Fiji", "Solomon Islands", "Vanuatu", "Samoa") ~ "东南亚、东亚和大洋洲",

      # 中欧、东欧和中亚
      country_names %in% c("Russia", "Ukraine", "Belarus", "Moldova", "Romania", "Bulgaria",
                          "Serbia", "Montenegro", "Bosnia and Herzegovina", "Albania",
                          "North Macedonia", "Kosovo", "Kazakhstan", "Uzbekistan",
                          "Turkmenistan", "Kyrgyzstan", "Tajikistan", "Armenia",
                          "Azerbaijan", "Georgia", "Poland", "Czech Republic",
                          "Slovakia", "Hungary", "Croatia", "Slovenia", "Estonia",
                          "Latvia", "Lithuania") ~ "中欧、东欧和中亚",

      # 拉丁美洲和加勒比海
      country_names %in% c("Brazil", "Mexico", "Colombia", "Argentina", "Peru", "Venezuela",
                          "Chile", "Ecuador", "Guatemala", "Cuba", "Bolivia", "Haiti",
                          "Dominican Republic", "Honduras", "Paraguay", "Nicaragua",
                          "El Salvador", "Costa Rica", "Panama", "Uruguay", "Jamaica",
                          "Trinidad and Tobago", "Guyana", "Suriname", "Belize") ~ "拉丁美洲和加勒比海",

      # 北非和中东
      country_names %in% c("Egypt", "Iran", "Turkey", "Iraq", "Saudi Arabia", "Yemen",
                          "Syria", "Jordan", "Lebanon", "United Arab Emirates",
                          "Oman", "Kuwait", "Qatar", "Bahrain", "Algeria", "Morocco",
                          "Sudan", "Tunisia", "Libya") ~ "北非和中东",

      # 默认分组
      TRUE ~ "其他"
    ),
    stringsAsFactors = FALSE
  )

  return(mapping)
}

# 创建映射
country_mapping <- create_simple_mapping(world, country_name_field)

# 检查映射结果
cat("\n地区分组统计:\n")
region_counts <- table(country_mapping$region)
print(region_counts)

# 合并AAPC数据
country_aapc <- country_mapping %>%
  left_join(region_aapc, by = c("region" = "地区")) %>%
  select(country, region, AAPC)

cat("\n成功匹配AAPC数据的国家数量:", sum(!is.na(country_aapc$AAPC)), "/", nrow(country_aapc), "\n")

# 显示每个地区的匹配情况
cat("\n各地区匹配情况:\n")
match_summary <- country_aapc %>%
  group_by(region) %>%
  summarise(
    total_countries = n(),
    matched_countries = sum(!is.na(AAPC)),
    .groups = 'drop'
  )
print(match_summary)

cat("\n测试完成！\n")
