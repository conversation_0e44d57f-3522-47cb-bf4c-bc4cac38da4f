# 脑卒中AAPC可视化使用指南

## 快速开始

### 运行脚本
```powershell
# 在PowerShell中运行
& "C:\Program Files\R\R-4.5.1\bin\Rscript.exe" stroke_blue_to_red_visualization.R
```

### 输出文件
运行后将生成以下文件：
- `stroke_incidence_10level_main_map_1990_2021.png` - 主地图
- `stroke_incidence_10level_complete_layout_1990_2021.png` - 完整布局

## 自定义修改

### 1. 调整色阶范围

如果您有不同的AAPC数据范围，可以修改色阶断点：

```r
# 在第102-111行修改
breaks_10_level <- c(-3.0, -2.5, -2.0, -1.5, -1.0, -0.5, 0, 0.5, 1.0, 1.5, 2.0)
```

### 2. 更改颜色方案

```r
# 在第112-123行修改颜色
colors_10_level <- c(
  "#08306B",  # 可以替换为您喜欢的颜色
  "#1C5A8A",  
  # ... 其他颜色
)
```

### 3. 调整地图尺寸

```r
# 主地图尺寸（第176行）
ggsave("stroke_incidence_10level_main_map_1990_2021.png", 
       main_map_blue_red, 
       width = 20, height = 14, dpi = 300, bg = "white")

# 完整布局尺寸（第270行）
ggsave("stroke_incidence_10level_complete_layout_1990_2021.png", 
       complete_blue_red_layout, 
       width = 24, height = 20, dpi = 300, bg = "white")
```

### 4. 修改标题和说明

```r
# 在第166-174行修改
labs(
  title = "您的自定义标题",
  subtitle = "您的自定义副标题",
  caption = "您的自定义说明"
)
```

### 5. 调整局部放大区域

```r
# 在第182-189行修改坐标范围
regions <- list(
  list(name = "您的区域名称", xlim = c(经度1, 经度2), ylim = c(纬度1, 纬度2)),
  # ... 其他区域
)
```

## 数据更新

### 使用新数据

1. **准备数据文件**：确保CSV文件包含以下列：
   - `分类`：地区名称
   - `AAPC...`：AAPC值

2. **修改数据读取**：
```r
# 第16-18行
stroke_data <- read.csv("您的数据文件.csv", 
                       stringsAsFactors = FALSE, 
                       fileEncoding = "UTF-8")
```

3. **更新地区筛选**：
```r
# 第20-24行，根据您的数据调整地区名称
region_aapc <- stroke_data %>%
  filter(分类 %in% c("您的地区1", "您的地区2", ...)) %>%
  select(地区 = 分类, AAPC = `AAPC...`)
```

### 添加新地区

在`create_country_mapping`函数中（第30-91行）添加新的国家-地区映射：

```r
country_names %in% c("新国家1", "新国家2") ~ "新地区名称",
```

## 故障排除

### 常见问题

1. **R包缺失**
```r
# 安装缺失的包
install.packages(c("ggplot2", "dplyr", "sf", "rnaturalearth", 
                   "rnaturalearthdata", "RColorBrewer", "gridExtra", "scales"))
```

2. **编码问题**
```r
# 确保CSV文件使用UTF-8编码
stroke_data <- read.csv("文件名.csv", fileEncoding = "UTF-8")
```

3. **内存不足**
```r
# 增加内存限制
memory.limit(size = 8000)  # 8GB
```

4. **字体问题**
```r
# 在Windows上使用系统字体
theme(text = element_text(family = "SimSun"))  # 宋体
```

### 警告信息处理

脚本运行时可能出现警告，通常是关于：
- 地图投影转换
- 缺失数据处理
- 图形设备设置

这些警告通常不影响最终结果，但如需查看详细信息：
```r
warnings()  # 查看所有警告
```

## 高级定制

### 1. 添加数据标签

```r
# 在地图上添加数值标签
geom_sf_text(aes(label = round(AAPC, 2)), size = 2, color = "black")
```

### 2. 更改投影方式

```r
# 使用不同的地图投影
coord_sf(crs = "+proj=robin")  # Robinson投影
```

### 3. 添加等值线

```r
# 添加AAPC等值线
stat_contour(aes(z = AAPC), color = "black", alpha = 0.5)
```

### 4. 交互式地图

```r
# 使用plotly创建交互式版本
library(plotly)
ggplotly(main_map_blue_red)
```

## 输出格式选项

### 不同文件格式

```r
# PDF格式（矢量图）
ggsave("map.pdf", width = 20, height = 14)

# SVG格式（网页友好）
ggsave("map.svg", width = 20, height = 14)

# TIFF格式（期刊要求）
ggsave("map.tiff", width = 20, height = 14, dpi = 300, compression = "lzw")
```

### 分辨率设置

```r
# 不同用途的分辨率
dpi = 150   # 网页显示
dpi = 300   # 印刷标准
dpi = 600   # 高质量印刷
```

## 性能优化

### 提高运行速度

1. **减少数据点**：
```r
# 简化地图边界
world <- ne_countries(scale = "small")  # 使用小比例尺
```

2. **并行处理**：
```r
library(parallel)
# 使用多核处理大数据
```

3. **缓存数据**：
```r
# 保存处理后的数据
saveRDS(world_with_aapc, "processed_data.rds")
world_with_aapc <- readRDS("processed_data.rds")
```

## 质量检查

### 验证输出

1. **检查数据完整性**：
```r
# 检查缺失值
sum(is.na(world_with_aapc$AAPC))
```

2. **验证色阶映射**：
```r
# 检查AAPC值分布
summary(region_aapc$AAPC)
hist(region_aapc$AAPC)
```

3. **确认地理边界**：
```r
# 检查地图范围
st_bbox(world_with_aapc)
```

## 版本控制

建议使用Git管理您的修改：

```bash
git init
git add stroke_blue_to_red_visualization.R
git commit -m "Initial stroke AAPC visualization"
```

## 技术支持

如遇到问题，请检查：
1. R版本兼容性（推荐R 4.0+）
2. 包版本更新
3. 数据格式正确性
4. 系统内存充足

---

**注意**：本指南基于当前脚本版本，如有重大修改请相应更新指南内容。
