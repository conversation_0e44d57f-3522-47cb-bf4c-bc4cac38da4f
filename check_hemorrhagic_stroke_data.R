# 检查死亡损伤数据库中是否有真实的出血性脑卒中数据
# 而不是使用估值计算

library(dplyr)
library(readr)

# 设置数据库路径
db_path <- "死亡损伤-数据库/204国家"

# 获取所有CSV文件路径
csv_files <- list.files(db_path, pattern = "\\.csv$", recursive = TRUE, full.names = TRUE)

cat("找到", length(csv_files), "个CSV文件\n")
cat("文件列表:\n")
for(i in 1:length(csv_files)) {
  cat(i, ":", basename(csv_files[i]), "\n")
}

# 检查每个文件中的疾病分类
check_stroke_causes <- function(file_path) {
  cat("\n检查文件:", basename(file_path), "\n")
  
  # 读取文件头部和一些样本数据
  tryCatch({
    # 读取前1000行来检查数据结构
    sample_data <- read_csv(file_path, n_max = 1000, show_col_types = FALSE)
    
    # 检查是否有cause_name列
    if("cause_name" %in% colnames(sample_data)) {
      # 获取所有独特的疾病名称
      unique_causes <- unique(sample_data$cause_name)
      
      cat("发现", length(unique_causes), "种疾病分类\n")
      
      # 查找脑卒中相关的疾病
      stroke_causes <- unique_causes[grepl("脑卒中|stroke|出血|hemorrhagic|缺血|ischemic|intracerebral|subarachnoid", 
                                          unique_causes, ignore.case = TRUE)]
      
      if(length(stroke_causes) > 0) {
        cat("脑卒中相关疾病:\n")
        for(cause in stroke_causes) {
          cat("  -", cause, "\n")
        }
        
        # 检查是否有中国数据
        if("location_name" %in% colnames(sample_data)) {
          china_data <- sample_data %>% filter(grepl("中国|China", location_name, ignore.case = TRUE))
          if(nrow(china_data) > 0) {
            cat("  ✓ 包含中国数据\n")
            return(list(file = basename(file_path), stroke_causes = stroke_causes, has_china = TRUE))
          } else {
            cat("  ✗ 不包含中国数据\n")
            return(list(file = basename(file_path), stroke_causes = stroke_causes, has_china = FALSE))
          }
        }
      } else {
        cat("  未发现脑卒中相关疾病\n")
        return(NULL)
      }
    } else {
      cat("  文件结构不包含cause_name列\n")
      return(NULL)
    }
  }, error = function(e) {
    cat("  读取文件时出错:", e$message, "\n")
    return(NULL)
  })
}

# 检查所有文件
results <- list()
for(i in 1:length(csv_files)) {
  result <- check_stroke_causes(csv_files[i])
  if(!is.null(result)) {
    results[[length(results) + 1]] <- result
  }
}

# 总结结果
cat("\n=== 检查结果总结 ===\n")
if(length(results) > 0) {
  cat("发现", length(results), "个文件包含脑卒中数据:\n")
  for(i in 1:length(results)) {
    cat("\n文件", i, ":", results[[i]]$file, "\n")
    cat("包含中国数据:", ifelse(results[[i]]$has_china, "是", "否"), "\n")
    cat("脑卒中疾病分类:\n")
    for(cause in results[[i]]$stroke_causes) {
      cat("  -", cause, "\n")
    }
  }
} else {
  cat("未发现包含脑卒中数据的文件\n")
}

# 如果找到了包含中国数据的文件，进一步检查详细分类
if(length(results) > 0) {
  china_files <- results[sapply(results, function(x) x$has_china)]
  
  if(length(china_files) > 0) {
    cat("\n=== 详细检查中国脑卒中数据 ===\n")
    
    for(i in 1:length(china_files)) {
      file_path <- csv_files[basename(csv_files) == china_files[[i]]$file]
      cat("\n检查文件:", china_files[[i]]$file, "\n")
      
      tryCatch({
        # 读取完整数据（或大样本）
        data <- read_csv(file_path, show_col_types = FALSE)
        
        # 筛选中国数据
        china_data <- data %>% 
          filter(grepl("中国|China", location_name, ignore.case = TRUE))
        
        if(nrow(china_data) > 0) {
          cat("中国数据行数:", nrow(china_data), "\n")
          
          # 检查脑卒中分类
          stroke_data <- china_data %>%
            filter(grepl("脑卒中|stroke|出血|hemorrhagic|缺血|ischemic", cause_name, ignore.case = TRUE))
          
          if(nrow(stroke_data) > 0) {
            cat("中国脑卒中数据行数:", nrow(stroke_data), "\n")
            
            # 显示所有脑卒中相关的疾病分类
            unique_stroke_causes <- unique(stroke_data$cause_name)
            cat("中国脑卒中疾病分类详情:\n")
            for(cause in unique_stroke_causes) {
              count <- sum(stroke_data$cause_name == cause)
              cat("  -", cause, "(", count, "条记录)\n")
            }
            
            # 检查年份范围
            years <- range(stroke_data$year, na.rm = TRUE)
            cat("年份范围:", years[1], "-", years[2], "\n")
            
            # 检查年龄组
            age_groups <- unique(stroke_data$age_name)
            cat("年龄组数量:", length(age_groups), "\n")
            
            # 检查是否有35岁以上的数据
            adult_ages <- age_groups[!grepl("0-|1-|5-|10-|15-|20-|25-|30-", age_groups)]
            if(length(adult_ages) > 0) {
              cat("35岁以上年龄组:", length(adult_ages), "个\n")
            }
          }
        }
      }, error = function(e) {
        cat("处理文件时出错:", e$message, "\n")
      })
    }
  }
}

cat("\n检查完成！\n")
