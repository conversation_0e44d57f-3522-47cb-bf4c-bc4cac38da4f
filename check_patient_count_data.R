# 检查数据库中是否有患者数量数据
library(readr)
library(dplyr)

cat("=== 检查中国脑卒中患者数量数据 ===\n")

# 读取中国数据文件
china_files <- list.files("死亡损伤-数据库/204国家", pattern = "IHME-GBD_2021_DATA.*\\.csv$", 
                         recursive = TRUE, full.names = TRUE)

cat("找到", length(china_files), "个数据文件\n")

# 检查每个文件中的metric类型
for(i in 1:min(3, length(china_files))) {
  file <- china_files[i]
  cat("\n检查文件", i, ":", basename(file), "\n")
  
  temp_data <- read_csv(file, show_col_types = FALSE, n_max = 1000)
  
  # 检查metric类型
  cat("可用metric类型:\n")
  metrics <- unique(temp_data$metric_name)
  for(j in 1:length(metrics)) {
    metric_count <- sum(temp_data$metric_name == metrics[j])
    cat("  ", j, ":", metrics[j], "(", metric_count, "行)\n")
  }
  
  # 检查measure类型
  cat("可用measure类型:\n")
  measures <- unique(temp_data$measure_name)
  for(j in 1:length(measures)) {
    measure_count <- sum(temp_data$measure_name == measures[j])
    cat("  ", j, ":", measures[j], "(", measure_count, "行)\n")
  }
  
  # 检查中国脑卒中数据
  china_stroke <- temp_data %>%
    filter(location_name == "中国", cause_id == 494)
  
  if(nrow(china_stroke) > 0) {
    cat("中国脑卒中数据:\n")
    cat("  行数:", nrow(china_stroke), "\n")
    cat("  年份范围:", min(china_stroke$year), "-", max(china_stroke$year), "\n")
    cat("  metric类型:", paste(unique(china_stroke$metric_name), collapse = ", "), "\n")
    cat("  measure类型:", paste(unique(china_stroke$measure_name), collapse = ", "), "\n")
    
    # 检查是否有数量数据
    count_data <- china_stroke %>% filter(metric_name == "数量")
    if(nrow(count_data) > 0) {
      cat("  找到数量数据:", nrow(count_data), "行\n")
      cat("  数量数据样本:\n")
      print(head(count_data[, c("year", "age_name", "sex_name", "val", "measure_name")], 3))
    } else {
      cat("  未找到数量数据\n")
    }
  } else {
    cat("未找到中国脑卒中数据\n")
  }
}

# 专门检查是否有患者数量数据
cat("\n=== 专门检查患者数量数据 ===\n")
china_data <- data.frame()

for(file in china_files[1:2]) {  # 只检查前两个文件
  temp_data <- read_csv(file, show_col_types = FALSE)
  
  # 筛选中国脑卒中数据，包括数量和率
  filtered_data <- temp_data %>%
    filter(location_name == "中国",
           cause_id == 494,         # 脑卒中
           year %in% c(1990, 2021)) %>%
    select(location_name, sex_id, sex_name, age_id, age_name, year, val, upper, lower, 
           metric_id, metric_name, measure_id, measure_name)
  
  china_data <- rbind(china_data, filtered_data)
}

if(nrow(china_data) > 0) {
  cat("找到中国脑卒中数据:", nrow(china_data), "行\n")
  
  # 按metric类型分组
  metric_summary <- china_data %>%
    group_by(metric_name, measure_name) %>%
    summarise(count = n(), .groups = 'drop')
  
  cat("数据类型分布:\n")
  print(metric_summary)
  
  # 检查数量数据
  count_data <- china_data %>% filter(metric_name == "数量")
  if(nrow(count_data) > 0) {
    cat("\n找到数量数据:", nrow(count_data), "行\n")
    cat("数量数据样本:\n")
    print(head(count_data[, c("year", "age_name", "sex_name", "val", "measure_name")], 5))
    
    # 检查35岁以上年龄组的数量数据
    age_groups_35plus <- c(12, 13, 14, 15, 16, 17, 18, 19, 20, 30, 31, 32, 235)
    count_35plus <- count_data %>% filter(age_id %in% age_groups_35plus)
    
    if(nrow(count_35plus) > 0) {
      cat("\n35岁以上数量数据:", nrow(count_35plus), "行\n")
      
      # 计算1990年和2021年的总数
      count_1990 <- count_35plus %>% 
        filter(year == 1990, sex_id == 3) %>%
        summarise(total = sum(val, na.rm = TRUE))
      
      count_2021 <- count_35plus %>% 
        filter(year == 2021, sex_id == 3) %>%
        summarise(total = sum(val, na.rm = TRUE))
      
      cat("1990年35岁以上脑卒中患者总数:", round(count_1990$total/1000, 1), "千人\n")
      cat("2021年35岁以上脑卒中患者总数:", round(count_2021$total/1000, 1), "千人\n")
    }
  } else {
    cat("未找到数量数据\n")
  }
} else {
  cat("未找到中国脑卒中数据\n")
}

cat("\n=== 检查完成 ===\n")
