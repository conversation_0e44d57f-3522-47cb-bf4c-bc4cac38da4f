# 修复版本：Fig 3 脑卒中区域地图显示问题
# 确保区域放大地图正确显示

library(ggplot2)
library(dplyr)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(RColorBrewer)
library(gridExtra)
library(grid)
library(scales)

# 读取数据
cat("=== 读取数据 ===\n")
stroke_data <- read.csv("脑卒中65岁以上人群患病率分析表.csv", 
                       stringsAsFactors = FALSE, 
                       fileEncoding = "UTF-8")

# 提取地区AAPC数据
region_aapc <- stroke_data %>%
  filter(分类 %in% c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", 
                    "高收入", "中欧、东欧和中亚", "拉丁美洲和加勒比海", 
                    "北非和中东")) %>%
  select(
    地区 = 分类,
    AAPC = `AAPC...`
  )

# 获取世界地图
world <- ne_countries(scale = "medium", returnclass = "sf")

# 创建国家映射
create_country_mapping <- function(world_data) {
  country_names <- world_data$name_en
  
  mapping <- data.frame(
    country = country_names,
    region = case_when(
      country_names %in% c("United States of America", "Canada", "Germany", "France", "United Kingdom", 
                          "Italy", "Spain", "Netherlands", "Belgium", "Switzerland", 
                          "Austria", "Sweden", "Norway", "Denmark", "Finland", "Ireland", 
                          "Portugal", "Greece", "Australia", "New Zealand", "Japan", 
                          "South Korea", "Israel", "Luxembourg", "Iceland") ~ "高收入",
      
      country_names %in% c("Nigeria", "Ethiopia", "South Africa", "Kenya", "Uganda", "Tanzania", 
                          "Ghana", "Mozambique", "Madagascar", "Cameroon", "Angola", "Niger", 
                          "Burkina Faso", "Mali", "Malawi", "Zambia", "Somalia", "Senegal", 
                          "Chad", "Zimbabwe", "Guinea", "Rwanda", "Benin", "Burundi", 
                          "South Sudan", "Togo", "Sierra Leone", "Liberia", "Botswana", 
                          "Namibia", "Gabon", "Lesotho") ~ "撒哈拉以南非洲",
      
      country_names %in% c("India", "Pakistan", "Bangladesh", "Afghanistan", 
                          "Nepal", "Sri Lanka", "Bhutan", "Maldives") ~ "南亚",
      
      country_names %in% c("China", "Indonesia", "Philippines", "Vietnam", "Thailand", 
                          "Myanmar", "Malaysia", "Cambodia", "Laos", "Singapore", 
                          "Mongolia", "Brunei", "Timor-Leste", "Papua New Guinea", 
                          "Fiji", "Solomon Islands", "Vanuatu", "Samoa") ~ "东南亚、东亚和大洋洲",
      
      country_names %in% c("Russia", "Ukraine", "Belarus", "Moldova", "Romania", "Bulgaria", 
                          "Serbia", "Montenegro", "Bosnia and Herzegovina", "Albania", 
                          "North Macedonia", "Kosovo", "Kazakhstan", "Uzbekistan", 
                          "Turkmenistan", "Kyrgyzstan", "Tajikistan", "Armenia", 
                          "Azerbaijan", "Georgia", "Poland", "Czech Republic", 
                          "Slovakia", "Hungary", "Croatia", "Slovenia", "Estonia", 
                          "Latvia", "Lithuania") ~ "中欧、东欧和中亚",
      
      country_names %in% c("Brazil", "Mexico", "Colombia", "Argentina", "Peru", "Venezuela", 
                          "Chile", "Ecuador", "Guatemala", "Cuba", "Bolivia", "Haiti", 
                          "Dominican Republic", "Honduras", "Paraguay", "Nicaragua", 
                          "El Salvador", "Costa Rica", "Panama", "Uruguay", "Jamaica", 
                          "Trinidad and Tobago", "Guyana", "Suriname", "Belize") ~ "拉丁美洲和加勒比海",
      
      country_names %in% c("Egypt", "Iran", "Turkey", "Iraq", "Saudi Arabia", "Yemen", 
                          "Syria", "Jordan", "Lebanon", "United Arab Emirates", 
                          "Oman", "Kuwait", "Qatar", "Bahrain", "Algeria", "Morocco", 
                          "Sudan", "Tunisia", "Libya") ~ "北非和中东",
      
      TRUE ~ "其他"
    ),
    stringsAsFactors = FALSE
  )
  
  return(mapping)
}

# 创建映射和合并数据
country_mapping <- create_country_mapping(world)
country_aapc <- country_mapping %>%
  left_join(region_aapc, by = c("region" = "地区")) %>%
  select(country, region, AAPC)

# 创建颜色分级
breaks <- c(-1.0, -0.8, -0.6, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 2.0)
labels <- c("-1.0 to <-0.8", "-0.8 to <-0.6", "-0.6 to <-0.4", "-0.4 to <-0.2", "-0.2 to <0.0",
           "0.0 to <0.2", "0.2 to <0.4", "0.4 to <0.6", "0.6 to <0.8", "0.8 to <1.0",
           "1.0 to <1.2", "1.2 to <1.4", "1.4 to <1.6", "≥1.6")

# 分配颜色组
country_aapc$aapc_group <- cut(country_aapc$AAPC, breaks = breaks, labels = labels, 
                              include.lowest = TRUE, right = FALSE)

# 创建颜色调色板
colors <- c("#08306b", "#08519c", "#3182bd", "#6baed6", "#9ecae1", "#c6dbef", 
           "#fee0d2", "#fcbba1", "#fc9272", "#fb6a4a", "#ef3b2c", "#cb181d", "#a50f15", "#67000d")
names(colors) <- labels

# 合并到地图数据
world_with_data <- world %>%
  left_join(country_aapc, by = c("name_en" = "country"))

cat("成功匹配的国家数量:", sum(!is.na(world_with_data$AAPC)), "/", nrow(world_with_data), "\n")

# 创建主地图
create_main_map <- function(world_data, colors) {
  color_mapping <- colors
  
  main_map <- ggplot(world_data) +
    geom_sf(aes(fill = aapc_group), color = "white", linewidth = 0.1) +
    scale_fill_manual(
      values = color_mapping,
      name = "AAPC (%)",
      na.value = "grey90",
      drop = FALSE,
      guide = guide_legend(
        title = "Average Annual Percentage Change (%)\nStroke Prevalence (≥65 years)\n1990-2019",
        title.position = "top",
        title.hjust = 0.5,
        ncol = 1,
        keywidth = 1.2,
        keyheight = 0.8
      )
    ) +
    coord_sf(crs = "+proj=robin +lon_0=0 +x_0=0 +y_0=0 +ellps=WGS84 +datum=WGS84 +units=m +no_defs") +
    theme_void() +
    theme(
      legend.position = "right",
      legend.margin = margin(l = 20),
      plot.title = element_text(size = 14, face = "bold", hjust = 0.5, margin = margin(b = 20)),
      plot.subtitle = element_text(size = 12, hjust = 0.5, margin = margin(b = 15)),
      plot.margin = margin(10, 10, 10, 10),
      panel.background = element_rect(fill = "white", color = NA),
      plot.background = element_rect(fill = "white", color = NA)
    ) +
    labs(
      title = "Global Stroke Prevalence: Average Annual Percentage Change (1990-2019)",
      subtitle = "Population aged ≥65 years"
    )
  
  return(main_map)
}

main_map <- create_main_map(world_with_data, colors)

# 定义区域（使用WGS84坐标系）
regions <- list(
  caribbean = list(
    xlim = c(-90, -55), 
    ylim = c(10, 30), 
    title = "Caribbean and Central America"
  ),
  persian_gulf = list(
    xlim = c(45, 60), 
    ylim = c(22, 32), 
    title = "Persian Gulf"
  ),
  balkans = list(
    xlim = c(12, 30), 
    ylim = c(40, 48), 
    title = "Balkan Peninsula"
  ),
  southeast_asia = list(
    xlim = c(90, 140), 
    ylim = c(-10, 25), 
    title = "South East Asia"
  ),
  west_africa = list(
    xlim = c(-20, 20), 
    ylim = c(0, 20), 
    title = "West Africa & Eastern Mediterranean"
  ),
  northern_europe = list(
    xlim = c(-10, 35), 
    ylim = c(55, 72), 
    title = "Northern Europe"
  )
)

# 创建区域地图函数（修复版本）
create_regional_map <- function(world_data, region_info, colors) {
  
  regional_map <- ggplot(world_data) +
    geom_sf(aes(fill = aapc_group), color = "white", linewidth = 0.3) +
    scale_fill_manual(
      values = colors,
      name = "AAPC (%)",
      na.value = "grey90",
      drop = FALSE
    ) +
    coord_sf(
      xlim = region_info$xlim, 
      ylim = region_info$ylim, 
      expand = FALSE
    ) +
    theme_void() +
    theme(
      legend.position = "none",
      plot.title = element_text(size = 11, face = "bold", hjust = 0.5),
      plot.margin = margin(5, 5, 5, 5),
      panel.border = element_rect(color = "black", fill = NA, linewidth = 1),
      panel.background = element_rect(fill = "white", color = NA)
    ) +
    labs(title = region_info$title)
  
  return(regional_map)
}

# 生成所有区域地图
cat("\n=== 创建区域放大地图 ===\n")
regional_maps <- list()

for(region_name in names(regions)) {
  cat("创建", region_name, "地图...\n")
  regional_maps[[region_name]] <- create_regional_map(
    world_with_data, 
    regions[[region_name]], 
    colors
  )
}

# 测试单个区域地图
cat("保存单个区域地图测试...\n")
ggsave("test_caribbean_fixed.png", regional_maps$caribbean, 
       width = 8, height = 6, dpi = 300, bg = "white")

# 创建区域地图组合
cat("创建区域地图组合...\n")
regional_combined <- arrangeGrob(
  regional_maps$caribbean, regional_maps$persian_gulf,
  regional_maps$balkans, regional_maps$southeast_asia,
  regional_maps$west_africa, regional_maps$northern_europe,
  ncol = 3, nrow = 2,
  top = textGrob("Regional Focus Areas: Stroke Prevalence AAPC (1990-2019)", 
                 gp = gpar(fontsize = 14, fontface = "bold"))
)

ggsave("stroke_fig3_regional_maps_fixed.png", regional_combined,
       width = 15, height = 8, dpi = 300, bg = "white")

# 创建完整布局
final_layout <- arrangeGrob(
  main_map,
  regional_combined,
  heights = c(2, 1),
  ncol = 1
)

ggsave("stroke_fig3_complete_layout_fixed.png", final_layout,
       width = 16, height = 12, dpi = 300, bg = "white")

cat("修复版本完成！生成的文件:\n")
cat("1. test_caribbean_fixed.png - 单个区域测试\n")
cat("2. stroke_fig3_regional_maps_fixed.png - 区域地图组合\n")
cat("3. stroke_fig3_complete_layout_fixed.png - 完整布局\n")
