# 分析AAPC数据分布，设计更均匀的颜色分级

library(dplyr)

# 加载数据
country_aapc <- read.csv('stroke_dalys_65plus_aapc_results_1990_2021.csv')

cat("=== AAPC数据分布分析 ===\n")
cat("总国家数:", nrow(country_aapc), "\n")
cat("数据范围:", round(min(country_aapc$aapc), 2), "to", round(max(country_aapc$aapc), 2), "\n")
cat("平均值:", round(mean(country_aapc$aapc), 2), "\n")
cat("中位数:", round(median(country_aapc$aapc), 2), "\n\n")

# 计算详细分位数
cat("=== 分位数分析 ===\n")
quantiles <- quantile(country_aapc$aapc, probs = seq(0, 1, 0.1))
for(i in 1:length(quantiles)) {
  cat(sprintf("%s: %5.2f\n", names(quantiles)[i], quantiles[i]))
}

# 分析当前分级的分布
cat("\n=== 当前分级分布 ===\n")
breaks_current <- c(-6.5, -3.5, -2.5, -2.0, -1.5, -1.0, -0.5, 0, 0.5, 2.0)
country_aapc$current_category <- cut(country_aapc$aapc, breaks = breaks_current, include.lowest = TRUE)
current_dist <- table(country_aapc$current_category, useNA = "ifany")
print(current_dist)

# 设计基于分位数的均匀分级（10级）
cat("\n=== 设计均匀分级方案 ===\n")
# 使用十分位数创建10个相等的组
decile_breaks <- quantile(country_aapc$aapc, probs = seq(0, 1, 0.1))
decile_breaks[1] <- decile_breaks[1] - 0.01  # 确保包含最小值
decile_breaks[11] <- decile_breaks[11] + 0.01  # 确保包含最大值

cat("建议的分位数分级点:\n")
for(i in 1:length(decile_breaks)) {
  cat(sprintf("Break %2d: %6.2f\n", i, decile_breaks[i]))
}

# 创建标签
decile_labels <- c()
for(i in 1:(length(decile_breaks)-1)) {
  decile_labels <- c(decile_labels, 
                    sprintf("%.2f to %.2f", decile_breaks[i], decile_breaks[i+1]))
}

cat("\n建议的标签:\n")
for(i in 1:length(decile_labels)) {
  cat(sprintf("Level %2d: %s\n", i, decile_labels[i]))
}

# 测试新分级的分布
country_aapc$decile_category <- cut(country_aapc$aapc, 
                                   breaks = decile_breaks, 
                                   labels = decile_labels,
                                   include.lowest = TRUE)

cat("\n新分级的国家分布:\n")
decile_dist <- table(country_aapc$decile_category, useNA = "ifany")
print(decile_dist)

# 另一种方案：手动调整的均匀分级
cat("\n=== 手动调整的均匀分级方案 ===\n")
# 基于数据分布特点手动设计
manual_breaks <- c(-6.5, -4.0, -3.0, -2.5, -2.0, -1.5, -1.0, -0.5, 0, 0.5, 2.0)
manual_labels <- c("< -4.0", "-4.0 to -3.0", "-3.0 to -2.5", "-2.5 to -2.0", 
                  "-2.0 to -1.5", "-1.5 to -1.0", "-1.0 to -0.5", "-0.5 to 0", 
                  "0 to 0.5", "> 0.5")

country_aapc$manual_category <- cut(country_aapc$aapc, 
                                   breaks = manual_breaks, 
                                   labels = manual_labels,
                                   include.lowest = TRUE)

cat("手动调整分级的国家分布:\n")
manual_dist <- table(country_aapc$manual_category, useNA = "ifany")
print(manual_dist)

# 保存分析结果
analysis_result <- data.frame(
  method = c(rep("Current", length(current_dist)), 
            rep("Decile", length(decile_dist)),
            rep("Manual", length(manual_dist))),
  category = c(names(current_dist), names(decile_dist), names(manual_dist)),
  count = c(as.numeric(current_dist), as.numeric(decile_dist), as.numeric(manual_dist))
)

write.csv(analysis_result, "color_distribution_analysis.csv", row.names = FALSE)

cat("\n=== 推荐方案 ===\n")
cat("建议使用十分位数方案，每个颜色级别包含约20个国家，\n")
cat("这样可以确保颜色在地图上分布更加均匀。\n")
cat("分析结果已保存到: color_distribution_analysis.csv\n")
