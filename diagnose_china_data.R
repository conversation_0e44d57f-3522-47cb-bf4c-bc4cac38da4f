# 诊断中国数据匹配问题

library(dplyr)
library(rnaturalearth)
library(sf)

# 1. 读取原始数据
cat("=== 1. 读取原始数据 ===\n")
country_aapc_35plus <- read.csv("stroke_dalys_35plus_aapc_results_1990_2021.csv", 
                               stringsAsFactors = FALSE, fileEncoding = "UTF-8")

# 检查原始中国数据
china_original <- country_aapc_35plus[country_aapc_35plus$location_name == "中国", ]
cat("原始中国数据:\n")
print(china_original)

taiwan_original <- country_aapc_35plus[country_aapc_35plus$location_name == "台湾", ]
cat("原始台湾数据:\n")
print(taiwan_original)

# 2. 应用名称映射
cat("\n=== 2. 应用名称映射 ===\n")
country_aapc_35plus$location_name_en <- country_aapc_35plus$location_name

# 简单映射中国
country_aapc_35plus$location_name_en[country_aapc_35plus$location_name == "中国"] <- "China"
country_aapc_35plus$location_name_en[country_aapc_35plus$location_name == "台湾"] <- "Taiwan"

# 检查映射后的数据
china_mapped <- country_aapc_35plus[country_aapc_35plus$location_name == "中国", ]
cat("映射后中国数据:\n")
print(china_mapped)

# 3. 处理台湾合并
cat("\n=== 3. 处理台湾合并 ===\n")
taiwan_data <- country_aapc_35plus[country_aapc_35plus$location_name == "台湾", ]
china_data <- country_aapc_35plus[country_aapc_35plus$location_name == "中国", ]

if(nrow(taiwan_data) > 0 && nrow(china_data) > 0) {
  combined_aapc <- mean(c(china_data$aapc, taiwan_data$aapc), na.rm = TRUE)
  country_aapc_35plus$aapc[country_aapc_35plus$location_name == "中国"] <- combined_aapc
  country_aapc_35plus$location_name_en[country_aapc_35plus$location_name == "中国"] <- "China"
  country_aapc_35plus <- country_aapc_35plus[country_aapc_35plus$location_name != "台湾", ]
  
  cat("台湾数据已合并到中国，合并后AAPC:", round(combined_aapc, 2), "%\n")
}

# 检查最终中国数据
china_final <- country_aapc_35plus[country_aapc_35plus$location_name_en == "China", ]
cat("最终中国数据:\n")
print(china_final)

# 4. 检查世界地图数据
cat("\n=== 4. 检查世界地图数据 ===\n")
world <- ne_countries(scale = "medium", returnclass = "sf")

# 查找中国相关条目
china_entries <- world[grep("China", world$name, ignore.case = TRUE), ]
cat("世界地图中的中国条目:\n")
print(data.frame(name = china_entries$name, name_en = china_entries$name_en))

# 5. 测试数据合并
cat("\n=== 5. 测试数据合并 ===\n")
world_with_aapc <- world %>%
  left_join(country_aapc_35plus, by = c("name" = "location_name_en"))

# 检查中国是否成功匹配
china_in_map <- world_with_aapc[world_with_aapc$name == "China", ]
cat("地图中的中国数据:\n")
print(data.frame(
  name = china_in_map$name,
  aapc = china_in_map$aapc,
  location_name = china_in_map$location_name,
  is_na = is.na(china_in_map$aapc)
))

# 6. 检查所有匹配情况
cat("\n=== 6. 匹配统计 ===\n")
matched_count <- sum(!is.na(world_with_aapc$aapc))
total_count <- nrow(world_with_aapc)
cat("成功匹配的国家:", matched_count, "/", total_count, "\n")

# 检查未匹配的国家
unmatched_countries <- world_with_aapc[is.na(world_with_aapc$aapc), ]
cat("未匹配的国家数量:", nrow(unmatched_countries), "\n")

cat("\n诊断完成！\n")
