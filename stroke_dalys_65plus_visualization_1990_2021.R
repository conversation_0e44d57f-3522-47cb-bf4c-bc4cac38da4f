# 脑卒中65岁以上人群DALYs年均变化率地图可视化 (1990-2021)
# T1DM风格完整布局：主地图 + 6个区域放大图

library(dplyr)
library(ggplot2)
library(maps)
library(viridis)
library(RColorBrewer)
library(gridExtra)
library(grid)

# 1. 加载处理好的数据
cat("Loading processed AAPC data...\n")
country_aapc <- read.csv("stroke_dalys_65plus_aapc_results_1990_2021.csv")

# 国家名称标准化
country_mapping <- data.frame(
  gbd_name = c("美国", "英国", "俄罗斯", "韩国", "伊朗", "叙利亚", "委内瑞拉", "玻利维亚", 
               "坦桑尼亚", "刚果民主共和国", "老挝", "越南", "文莱", "北马其顿", "摩尔多瓦",
               "中国", "日本", "德国", "法国", "意大利", "西班牙", "加拿大", "澳大利亚",
               "巴西", "印度", "南非", "埃及", "土耳其", "沙特阿拉伯", "阿联酋"),
  map_name = c("USA", "UK", "Russia", "South Korea", "Iran", "Syria", "Venezuela", "Bolivia",
               "Tanzania", "Democratic Republic of the Congo", "Laos", "Vietnam", "Brunei", 
               "North Macedonia", "Moldova", "China", "Japan", "Germany", "France", "Italy", 
               "Spain", "Canada", "Australia", "Brazil", "India", "South Africa", "Egypt", 
               "Turkey", "Saudi Arabia", "UAE"),
  stringsAsFactors = FALSE
)

# 获取世界地图数据
world_map <- map_data("world")

# 标准化国家名称
country_aapc$region <- country_aapc$location_name
for(i in 1:nrow(country_mapping)) {
  country_aapc$region[country_aapc$location_name == country_mapping$gbd_name[i]] <- country_mapping$map_name[i]
}

# 合并地图数据
map_data_with_aapc <- merge(world_map, country_aapc, by = "region", all.x = TRUE)
map_data_with_aapc <- map_data_with_aapc[order(map_data_with_aapc$order), ]

# 2. 设置颜色分级
breaks <- c(-6.5, -4.0, -3.0, -2.0, -1.0, -0.5, 0, 0.5, 1.0, 1.88)
labels <- c("< -4.0", "-4.0 to -3.0", "-3.0 to -2.0", "-2.0 to -1.0", 
           "-1.0 to -0.5", "-0.5 to 0", "0 to 0.5", "0.5 to 1.0", "> 1.0")

# 平衡的蓝-红色带
colors <- c("#08519c", "#2171b5", "#4292c6", "#6baed6", "#9ecae1", 
           "#fee0d2", "#fcbba1", "#fc9272", "#de2d26")

# 分类AAPC值
map_data_with_aapc$aapc_category <- cut(map_data_with_aapc$aapc, 
                                       breaks = breaks, 
                                       labels = labels, 
                                       include.lowest = TRUE)

# 3. 创建主地图
cat("Creating main world map...\n")
main_map <- ggplot(map_data_with_aapc, aes(x = long, y = lat, group = group)) +
  geom_polygon(aes(fill = aapc_category), color = "white", size = 0.1) +
  scale_fill_manual(values = colors, 
                   name = "AAPC (%)",
                   na.value = "grey90",
                   drop = FALSE) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 10),
    legend.key.size = unit(0.8, "cm"),
    legend.key.width = unit(1.2, "cm"),
    plot.margin = margin(5, 5, 5, 5, "mm")
  ) +
  guides(fill = guide_legend(nrow = 1, byrow = TRUE)) +
  coord_fixed(1.3)

# 4. 创建区域放大地图函数
create_regional_map <- function(xlim, ylim, title) {
  ggplot(map_data_with_aapc, aes(x = long, y = lat, group = group)) +
    geom_polygon(aes(fill = aapc_category), color = "white", size = 0.2) +
    scale_fill_manual(values = colors, na.value = "grey90", guide = "none") +
    coord_fixed(1.3, xlim = xlim, ylim = ylim) +
    theme_void() +
    theme(
      plot.title = element_text(size = 10, hjust = 0.5, face = "bold"),
      plot.margin = margin(2, 2, 2, 2, "mm")
    ) +
    ggtitle(title)
}

# 5. 创建6个区域放大地图
cat("Creating regional zoom maps...\n")

# 加勒比和中美洲
caribbean <- create_regional_map(c(-95, -55), c(5, 35), "Caribbean and Central America")

# 波斯湾地区  
persian_gulf <- create_regional_map(c(45, 65), c(20, 40), "Persian Gulf")

# 巴尔干半岛
balkans <- create_regional_map(c(10, 30), c(35, 50), "Balkan Peninsula")

# 东南亚
southeast_asia <- create_regional_map(c(90, 140), c(-15, 25), "South East Asia")

# 西非与东地中海
west_africa_med <- create_regional_map(c(-20, 45), c(0, 40), "West Africa & Eastern Mediterranean")

# 北欧
northern_europe <- create_regional_map(c(-10, 35), c(50, 75), "Northern Europe")

cat("Combining all maps into final layout...\n")

# 6. 组合所有地图
# 创建区域地图的网格布局
regional_grid <- grid.arrange(
  caribbean, persian_gulf, balkans,
  southeast_asia, west_africa_med, northern_europe,
  ncol = 3, nrow = 2
)

# 7. 创建最终完整布局
final_plot <- grid.arrange(
  main_map,
  regional_grid,
  ncol = 1,
  heights = c(2, 1),
  top = textGrob("Stroke DALYs AAPC in Population Aged ≥65 Years (1990-2021)", 
                gp = gpar(fontsize = 16, fontface = "bold"))
)

# 8. 保存地图
cat("Saving maps...\n")

# 保存完整布局
ggsave("stroke_dalys_65plus_complete_layout_1990_2021.png", 
       final_plot, width = 16, height = 12, dpi = 300, bg = "white")

# 保存主地图
ggsave("stroke_dalys_65plus_main_map_1990_2021.png", 
       main_map, width = 14, height = 8, dpi = 300, bg = "white")

# 保存区域地图
ggsave("stroke_dalys_65plus_regional_maps_1990_2021.png", 
       regional_grid, width = 12, height = 8, dpi = 300, bg = "white")

cat("Visualization completed successfully!\n")
cat("Files saved:\n")
cat("- stroke_dalys_65plus_complete_layout_1990_2021.png (Complete layout)\n")
cat("- stroke_dalys_65plus_main_map_1990_2021.png (Main world map)\n")
cat("- stroke_dalys_65plus_regional_maps_1990_2021.png (Regional zoom maps)\n")
