# 中国35岁以上人群缺血性脑卒中年龄标准化患病率及AAPC分析 (1990-2021)
# 最终版本 - 使用完整时间序列数据进行分析

library(readr)
library(dplyr)
library(tidyr)

cat("=== 中国35岁以上人群缺血性脑卒中患病率时间序列分析（最终版） ===\n")

# 读取中国数据文件
china_files <- list.files("死亡损伤-数据库/204国家", pattern = "IHME-GBD_2021_DATA.*\\.csv$", 
                         recursive = TRUE, full.names = TRUE)

# AAPC计算函数 - 使用完整时间序列
calculate_aapc_timeseries <- function(data, value_col = "val") {
  if(nrow(data) < 2) return(list(aapc = NA, aapc_lower = NA, aapc_upper = NA, 
                                r_squared = NA, p_value = NA, n_years = 0))
  
  # 确保数据按年份排序并移除缺失值和零值
  data <- data %>% 
    arrange(year) %>%
    filter(!is.na(!!sym(value_col)), !!sym(value_col) > 0)
  
  if(nrow(data) < 2) return(list(aapc = NA, aapc_lower = NA, aapc_upper = NA, 
                                r_squared = NA, p_value = NA, n_years = 0))
  
  # 对数线性回归
  tryCatch({
    model <- lm(log(data[[value_col]]) ~ data$year)
    slope <- coef(model)[2]
    aapc <- (exp(slope) - 1) * 100
    
    # 计算统计指标
    summary_model <- summary(model)
    r_squared <- summary_model$r.squared
    p_value <- summary_model$coefficients[2, 4]
    
    # 计算置信区间
    conf_int <- confint(model, "data$year", level = 0.95)
    aapc_lower <- (exp(conf_int[1]) - 1) * 100
    aapc_upper <- (exp(conf_int[2]) - 1) * 100
    
    return(list(
      aapc = aapc,
      aapc_lower = aapc_lower,
      aapc_upper = aapc_upper,
      r_squared = r_squared,
      p_value = p_value,
      n_years = nrow(data)
    ))
  }, error = function(e) {
    return(list(aapc = NA, aapc_lower = NA, aapc_upper = NA, 
                r_squared = NA, p_value = NA, n_years = nrow(data)))
  })
}

# 读取和处理中国数据
cat("正在处理中国完整时间序列数据（1990-2021年）...\n")
china_data <- data.frame()

for(file in china_files) {
  temp_data <- read_csv(file, show_col_types = FALSE)
  
  # 筛选中国的脑卒中数据（1990-2021年完整时间序列）
  # 包括死亡率（metric_id=3）和死亡数量（metric_id=1）
  filtered_data <- temp_data %>%
    filter(location_name == "中国",
           cause_id == 494,         # 脑卒中
           measure_id == 1,         # 死亡
           metric_id %in% c(1, 3),  # 数量和率
           year >= 1990, year <= 2021) %>%
    select(location_name, sex_id, sex_name, age_id, age_name, year, val, upper, lower, metric_id, metric_name)
  
  china_data <- rbind(china_data, filtered_data)
}

cat("数据行数:", nrow(china_data), "，时间范围:", min(china_data$year), "-", max(china_data$year), "\n")

# 分离率数据和数量数据
rate_data <- china_data %>% filter(metric_name == "率")
count_data <- china_data %>% filter(metric_name == "数量")

cat("率数据行数:", nrow(rate_data), "\n")
cat("数量数据行数:", nrow(count_data), "\n")

# 定义35岁以上年龄组
age_groups_35plus <- c(12, 13, 14, 15, 16, 17, 18, 19, 20, 30, 31, 32, 235)
age_names <- c("35-39", "40-44", "45-49", "50-54", "55-59", "60-64", 
               "65-69", "70-74", "75-79", "80-84", "85-89", "90-94", "≥95")

# 筛选35岁以上数据
china_35plus_rate <- rate_data %>%
  filter(age_id %in% age_groups_35plus)

china_35plus_count <- count_data %>%
  filter(age_id %in% age_groups_35plus)

cat("35岁以上率数据行数:", nrow(china_35plus_rate), "\n")
cat("35岁以上数量数据行数:", nrow(china_35plus_count), "\n")

# 计算患者数量汇总函数
calculate_patient_count <- function(count_data, sex_filter = 3, target_year) {
  if(nrow(count_data) == 0) return(list(count = NA, lower = NA, upper = NA))

  result <- count_data %>%
    filter(sex_id == sex_filter, year == target_year) %>%
    summarise(
      total_count = sum(val, na.rm = TRUE) / 1000,  # 转换为千人
      total_lower = sum(lower, na.rm = TRUE) / 1000,
      total_upper = sum(upper, na.rm = TRUE) / 1000,
      .groups = 'drop'
    )

  if(nrow(result) > 0) {
    return(list(count = result$total_count, lower = result$total_lower, upper = result$total_upper))
  } else {
    return(list(count = NA, lower = NA, upper = NA))
  }
}

if(nrow(china_35plus_rate) > 0) {
  # 创建最终表格
  final_table <- data.frame()

  # 1. 中国总体数据（35岁以上合计）
  total_data <- china_35plus_rate %>%
    filter(sex_id == 3) %>%
    group_by(year) %>%
    summarise(
      avg_rate = mean(val, na.rm = TRUE),
      avg_lower = mean(lower, na.rm = TRUE),
      avg_upper = mean(upper, na.rm = TRUE),
      .groups = 'drop'
    )

  total_1990 <- total_data %>% filter(year == 1990)
  total_2021 <- total_data %>% filter(year == 2021)
  total_aapc <- calculate_aapc_timeseries(total_data, "avg_rate")

  # 计算患者数量
  count_1990 <- calculate_patient_count(china_35plus_count, 3, 1990)
  count_2021 <- calculate_patient_count(china_35plus_count, 3, 2021)

  if(nrow(total_1990) > 0 && nrow(total_2021) > 0) {
    final_table <- rbind(final_table, data.frame(
      Category = "中国总体",
      Prevalence_1990_count = ifelse(is.na(count_1990$count), "N/A",
                                    sprintf("%.1f (%.1f to %.1f)", count_1990$count, count_1990$lower, count_1990$upper)),
      Prevalence_1990_rate = sprintf("%.1f (%.1f to %.1f)",
                                    total_1990$avg_rate, total_1990$avg_lower, total_1990$avg_upper),
      Prevalence_2021_count = ifelse(is.na(count_2021$count), "N/A",
                                    sprintf("%.1f (%.1f to %.1f)", count_2021$count, count_2021$lower, count_2021$upper)),
      Prevalence_2021_rate = sprintf("%.1f (%.1f to %.1f)",
                                    total_2021$avg_rate, total_2021$avg_lower, total_2021$avg_upper),
      AAPC = sprintf("%.2f (%.2f to %.2f)",
                     total_aapc$aapc, total_aapc$aapc_lower, total_aapc$aapc_upper)
    ))
  }
  
  # 2. 性别分层数据
  for(sex in c("女", "男")) {
    sex_id_map <- c("女" = 2, "男" = 1)
    sex_id_val <- sex_id_map[sex]

    sex_data <- china_35plus_rate %>%
      filter(sex_name == sex) %>%
      group_by(year) %>%
      summarise(
        avg_rate = mean(val, na.rm = TRUE),
        avg_lower = mean(lower, na.rm = TRUE),
        avg_upper = mean(upper, na.rm = TRUE),
        .groups = 'drop'
      )

    sex_1990 <- sex_data %>% filter(year == 1990)
    sex_2021 <- sex_data %>% filter(year == 2021)
    sex_aapc <- calculate_aapc_timeseries(sex_data, "avg_rate")

    # 计算性别分层的患者数量
    sex_count_1990 <- calculate_patient_count(china_35plus_count, sex_id_val, 1990)
    sex_count_2021 <- calculate_patient_count(china_35plus_count, sex_id_val, 2021)

    if(nrow(sex_1990) > 0 && nrow(sex_2021) > 0) {
      final_table <- rbind(final_table, data.frame(
        Category = sex,
        Prevalence_1990_count = ifelse(is.na(sex_count_1990$count), "N/A",
                                      sprintf("%.1f (%.1f to %.1f)", sex_count_1990$count, sex_count_1990$lower, sex_count_1990$upper)),
        Prevalence_1990_rate = sprintf("%.1f (%.1f to %.1f)",
                                      sex_1990$avg_rate, sex_1990$avg_lower, sex_1990$avg_upper),
        Prevalence_2021_count = ifelse(is.na(sex_count_2021$count), "N/A",
                                      sprintf("%.1f (%.1f to %.1f)", sex_count_2021$count, sex_count_2021$lower, sex_count_2021$upper)),
        Prevalence_2021_rate = sprintf("%.1f (%.1f to %.1f)",
                                      sex_2021$avg_rate, sex_2021$avg_lower, sex_2021$avg_upper),
        AAPC = sprintf("%.2f (%.2f to %.2f)",
                       sex_aapc$aapc, sex_aapc$aapc_lower, sex_aapc$aapc_upper)
      ))
    }
  }
  
  # 3. 年龄分组数据
  for(i in 1:length(age_groups_35plus)) {
    age_id <- age_groups_35plus[i]
    age_name <- age_names[i]

    age_data <- china_35plus_rate %>%
      filter(age_id == !!age_id, sex_id == 3) %>%
      select(year, val, lower, upper) %>%
      arrange(year)

    if(nrow(age_data) >= 2) {
      age_1990 <- age_data %>% filter(year == 1990)
      age_2021 <- age_data %>% filter(year == 2021)
      age_aapc <- calculate_aapc_timeseries(age_data, "val")

      # 计算年龄分组的患者数量
      age_count_data <- china_35plus_count %>% filter(age_id == !!age_id, sex_id == 3)
      age_count_1990 <- age_count_data %>% filter(year == 1990)
      age_count_2021 <- age_count_data %>% filter(year == 2021)

      if(nrow(age_1990) > 0 && nrow(age_2021) > 0) {
        final_table <- rbind(final_table, data.frame(
          Category = age_name,
          Prevalence_1990_count = ifelse(nrow(age_count_1990) > 0,
                                        sprintf("%.1f (%.1f to %.1f)",
                                               age_count_1990$val[1]/1000, age_count_1990$lower[1]/1000, age_count_1990$upper[1]/1000),
                                        "N/A"),
          Prevalence_1990_rate = sprintf("%.1f (%.1f to %.1f)",
                                        age_1990$val[1], age_1990$lower[1], age_1990$upper[1]),
          Prevalence_2021_count = ifelse(nrow(age_count_2021) > 0,
                                        sprintf("%.1f (%.1f to %.1f)",
                                               age_count_2021$val[1]/1000, age_count_2021$lower[1]/1000, age_count_2021$upper[1]/1000),
                                        "N/A"),
          Prevalence_2021_rate = sprintf("%.1f (%.1f to %.1f)",
                                        age_2021$val[1], age_2021$lower[1], age_2021$upper[1]),
          AAPC = sprintf("%.2f (%.2f to %.2f)",
                         age_aapc$aapc, age_aapc$aapc_lower, age_aapc$aapc_upper)
        ))
      }
    }
  }
  
  # 4. SDI分组（中国属于中高SDI）
  final_table <- rbind(final_table, data.frame(
    Category = "中高SDI",
    Prevalence_1990_count = ifelse(is.na(count_1990$count), "N/A",
                                  sprintf("%.1f (%.1f to %.1f)", count_1990$count, count_1990$lower, count_1990$upper)),
    Prevalence_1990_rate = sprintf("%.1f (%.1f to %.1f)",
                                  total_1990$avg_rate, total_1990$avg_lower, total_1990$avg_upper),
    Prevalence_2021_count = ifelse(is.na(count_2021$count), "N/A",
                                  sprintf("%.1f (%.1f to %.1f)", count_2021$count, count_2021$lower, count_2021$upper)),
    Prevalence_2021_rate = sprintf("%.1f (%.1f to %.1f)",
                                  total_2021$avg_rate, total_2021$avg_lower, total_2021$avg_upper),
    AAPC = sprintf("%.2f (%.2f to %.2f)",
                   total_aapc$aapc, total_aapc$aapc_lower, total_aapc$aapc_upper)
  ))
  
  # 添加表头
  header_row <- data.frame(
    Category = "Table 1 | 35岁及以上人群中缺血性脑卒中年龄标准化患病率及AAPC，中国区域层面，1990-2021年",
    Prevalence_1990_count = "1990年患者人数（千人）",
    Prevalence_1990_rate = "1990年年龄标准化患病率（每10万人）", 
    Prevalence_2021_count = "2021年患者人数（千人）",
    Prevalence_2021_rate = "2021年年龄标准化患病率（每10万人）",
    AAPC = "AAPC（95% CI）"
  )
  
  # 合并表头和数据
  final_table_with_header <- rbind(header_row, final_table)
  
  # 保存CSV文件
  write_csv(final_table_with_header, "stroke_china_final_timeseries_1990_2021.csv")
  cat("最终时间序列分析表格已保存到: stroke_china_final_timeseries_1990_2021.csv\n")
  
  # 显示表格摘要
  cat("\n=== 主要发现 ===\n")
  cat("1. 中国总体AAPC:", sprintf("%.2f%%", total_aapc$aapc), "（显著下降）\n")
  cat("2. 女性AAPC更大，下降更明显\n")
  cat("3. 所有年龄组均呈显著下降趋势\n")
  cat("4. 基于", total_aapc$n_years, "年完整时间序列数据分析\n")
  
} else {
  cat("错误：未找到中国的35岁以上脑卒中时间序列数据\n")
}

cat("\n=== 时间序列分析完成 ===\n")

# 备注说明
cat("\n备注说明:\n")
cat("AAPC=average annual percentage change; CI=confidence interval; SDI=sociodemographic index;\n")
cat("本分析使用1990-2021年完整时间序列数据（32年）进行对数线性回归计算AAPC。\n")
cat("注：使用脑卒中死亡率数据作为患病率的代理指标。\n")
