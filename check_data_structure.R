# 检查数据结构，看是否有患者数量信息
library(dplyr)
library(readr)

cat("=== 检查数据结构 ===\n")

# 读取第一个数据文件
file1 <- list.files('死亡损伤-数据库/204国家', 
                   pattern='IHME-GBD_2021_DATA.*\\.csv$', 
                   recursive=TRUE, full.names=TRUE)[1]

cat("检查文件:", basename(file1), "\n")

# 读取少量数据查看结构
sample_data <- read_csv(file1, n_max = 100, show_col_types = FALSE)

cat("数据列名:\n")
print(colnames(sample_data))

cat("\n数据前几行:\n")
print(head(sample_data))

# 检查metric_id的含义
cat("\n可用的metric类型:\n")
metrics <- sample_data %>% 
  select(metric_id, metric_name) %>% 
  distinct()
print(metrics)

# 检查中国脑卒中数据的metric类型
china_stroke <- sample_data %>%
  filter(location_name == "中国", cause_id == 494) %>%
  select(metric_id, metric_name, val) %>%
  distinct()

cat("\n中国脑卒中数据的metric类型:\n")
print(china_stroke)
