# 脑卒中35岁以上人群DALYs年均变化率地图 - 超精细颜色分级 (1990-2021)

library(dplyr)
library(ggplot2)
library(maps)
library(viridis)
library(RColorBrewer)
library(gridExtra)
library(grid)

# 1. 加载35岁以上分析结果
cat("Loading 35+ population AAPC results...\n")
country_aapc_35plus <- read.csv("stroke_dalys_35plus_aapc_results_1990_2021.csv",
                               stringsAsFactors = FALSE,
                               fileEncoding = "UTF-8")

cat("35+ population analysis summary:\n")
cat("Countries:", nrow(country_aapc_35plus), "\n")
cat("AAPC range:", round(min(country_aapc_35plus$aapc), 2), "to",
    round(max(country_aapc_35plus$aapc), 2), "\n")
cat("Mean AAPC:", round(mean(country_aapc_35plus$aapc), 2), "%\n")
cat("Median AAPC:", round(median(country_aapc_35plus$aapc), 2), "%\n")

# 1.1 创建完整的中英文国家名称映射表
create_country_name_mapping <- function() {
  mapping <- data.frame(
    chinese_name = c(
      "中国", "美利坚合众国", "印度", "俄罗斯", "日本", "德国", "英国", "法国", "意大利", "巴西",
      "加拿大", "大韩民国", "西班牙", "澳大利亚", "墨西哥", "印度尼西亚", "荷兰", "沙特阿拉伯",
      "土耳其", "瑞士", "比利时", "爱尔兰", "以色列", "奥地利", "尼日利亚", "阿根廷",
      "南非", "埃及", "孟加拉国", "越南", "智利", "芬兰", "罗马尼亚", "捷克共和国",
      "新西兰", "秘鲁", "希腊", "葡萄牙", "伊拉克", "阿尔及利亚", "卡塔尔", "哈萨克斯坦",
      "匈牙利", "科威特", "摩洛哥", "斯洛伐克", "厄瓜多尔", "古巴", "阿拉伯联合酋长国", "白俄罗斯",
      "阿塞拜疆", "斯里兰卡", "缅甸", "乌兹别克斯坦", "多米尼加共和国", "乌拉圭", "哥斯达黎加",
      "斯洛文尼亚", "立陶宛", "巴拿马", "保加利亚", "克罗地亚", "约旦", "塞尔维亚",
      "黎巴嫩", "新加坡", "挪威", "丹麦", "瑞典", "阿富汗", "乌干达", "乌克兰",
      "阿尔巴尼亚", "巴林岛", "亚美尼亚", "蒙古", "牙买加", "纳米比亚", "博茨瓦纳",
      "加蓬", "莱索托", "毛里求斯", "斯威士兰", "冰岛", "马尔他", "塞浦路斯",
      "文莱达鲁萨兰国", "巴巴多斯", "马尔代夫", "卢森堡公国", "不丹", "东帝汶民主共和国",
      "中非共和国", "乍得", "也门", "伊朗伊斯兰共和国", "伯利兹城", "佛得角", "津巴布韦",
      "洪都拉斯", "莫桑比克", "黑山共和国", "利比亚", "肯尼亚", "台湾", "爱沙尼亚",
      "拉脱维亚", "波兰", "阿曼", "巴基斯坦", "尼泊尔", "马来西亚", "泰国", "菲律宾",
      "老挝人民民主共和国", "柬埔寨", "巴布亚新几内亚", "斐济", "所罗门群岛", "瓦努阿图", "萨摩亚",
      "汤加", "基里巴斯", "图瓦卢", "瑙鲁共和国", "帕劳共和国", "马绍尔群岛", "密克罗尼西亚联邦",
      "委内瑞拉玻利瓦尔共和国", "哥伦比亚", "玻利维亚国", "巴拉圭", "苏里南", "圭亚那", "海地",
      "格鲁吉亚", "摩尔多瓦共和国", "塔吉克斯坦", "吉尔吉斯斯坦", "土库曼斯坦", "埃塞俄比亚", "加纳",
      "冈比亚", "几内亚", "几内亚比绍", "利比里亚", "塞拉利昂", "塞内加尔", "马里", "布基纳法索",
      "尼日尔", "贝宁", "多哥", "科特廸亚", "刚果", "刚果民主共和国", "安哥拉", "赞比亚",
      "马拉维", "坦桑尼亚联合共和国", "卢旺达", "布隆迪", "南苏丹", "苏丹", "索马里",
      "吉布提", "厄立特里亚国", "突尼斯", "毛里塔尼亚", "马达加斯加", "科摩罗", "塞舌尔",
      "朝鲜民主主义人民共和国", "格林纳达", "安提瓜和巴布达", "圣基茨和尼维斯联邦", "圣卢西亚岛",
      "圣文森特和格林纳丁斯", "多米尼加岛", "特立尼达拉岛和多巴哥", "巴哈马", "萨尔瓦多", "危地马拉",
      "尼加拉瓜", "波斯尼亚和黑塞哥维那", "北马其顿", "阿拉伯叙利亚共和国", "巴勒斯坦", "赤道几内亚",
      "圣多美和普林西比民主共和国", "安道尔共和国", "摩纳哥公国", "圣马力诺共和国"
    ),
    english_name = c(
      "China", "United States of America", "India", "Russia", "Japan", "Germany", "United Kingdom", "France", "Italy", "Brazil",
      "Canada", "South Korea", "Spain", "Australia", "Mexico", "Indonesia", "Netherlands", "Saudi Arabia",
      "Turkey", "Switzerland", "Belgium", "Ireland", "Israel", "Austria", "Nigeria", "Argentina",
      "South Africa", "Egypt", "Bangladesh", "Vietnam", "Chile", "Finland", "Romania", "Czech Republic",
      "New Zealand", "Peru", "Greece", "Portugal", "Iraq", "Algeria", "Qatar", "Kazakhstan",
      "Hungary", "Kuwait", "Morocco", "Slovakia", "Ecuador", "Cuba", "United Arab Emirates", "Belarus",
      "Azerbaijan", "Sri Lanka", "Myanmar", "Uzbekistan", "Dominican Republic", "Uruguay", "Costa Rica",
      "Slovenia", "Lithuania", "Panama", "Bulgaria", "Croatia", "Jordan", "Serbia",
      "Lebanon", "Singapore", "Norway", "Denmark", "Sweden", "Afghanistan", "Uganda", "Ukraine",
      "Albania", "Bahrain", "Armenia", "Mongolia", "Jamaica", "Namibia", "Botswana",
      "Gabon", "Lesotho", "Mauritius", "Eswatini", "Iceland", "Malta", "Cyprus",
      "Brunei", "Barbados", "Maldives", "Luxembourg", "Bhutan", "East Timor",
      "Central African Republic", "Chad", "Yemen", "Iran", "Belize", "Cape Verde", "Zimbabwe",
      "Honduras", "Mozambique", "Montenegro", "Libya", "Kenya", "Taiwan", "Estonia",
      "Latvia", "Poland", "Oman", "Pakistan", "Nepal", "Malaysia", "Thailand", "Philippines",
      "Laos", "Cambodia", "Papua New Guinea", "Fiji", "Solomon Islands", "Vanuatu", "Samoa",
      "Tonga", "Kiribati", "Tuvalu", "Nauru", "Palau", "Marshall Islands", "Micronesia",
      "Venezuela", "Colombia", "Bolivia", "Paraguay", "Suriname", "Guyana", "Haiti",
      "Georgia", "Moldova", "Tajikistan", "Kyrgyzstan", "Turkmenistan", "Ethiopia", "Ghana",
      "Gambia", "Guinea", "Guinea-Bissau", "Liberia", "Sierra Leone", "Senegal", "Mali", "Burkina Faso",
      "Niger", "Benin", "Togo", "Ivory Coast", "Republic of the Congo", "Democratic Republic of the Congo", "Angola", "Zambia",
      "Malawi", "Tanzania", "Rwanda", "Burundi", "South Sudan", "Sudan", "Somalia",
      "Djibouti", "Eritrea", "Tunisia", "Mauritania", "Madagascar", "Comoros", "Seychelles",
      "North Korea", "Grenada", "Antigua and Barbuda", "Saint Kitts and Nevis", "Saint Lucia",
      "Saint Vincent and the Grenadines", "Dominica", "Trinidad and Tobago", "Bahamas", "El Salvador", "Guatemala",
      "Nicaragua", "Bosnia and Herzegovina", "North Macedonia", "Syria", "Palestine", "Equatorial Guinea",
      "São Tomé and Príncipe", "Andorra", "Monaco", "San Marino"
    ),
    stringsAsFactors = FALSE
  )
  return(mapping)
}

# 1.2 应用国家名称映射
name_mapping <- create_country_name_mapping()
country_aapc_35plus$location_name_en <- country_aapc_35plus$location_name

# 替换已知的中文名称为英文名称
for(i in 1:nrow(name_mapping)) {
  country_aapc_35plus$location_name_en[country_aapc_35plus$location_name == name_mapping$chinese_name[i]] <- name_mapping$english_name[i]
}

# 检查映射效果
mapped_count <- sum(country_aapc_35plus$location_name_en != country_aapc_35plus$location_name)
cat("成功映射的国家数量:", mapped_count, "/", nrow(country_aapc_35plus), "\n")

# 显示未映射的国家（用于调试）
unmapped_countries <- country_aapc_35plus[country_aapc_35plus$location_name_en == country_aapc_35plus$location_name, ]
if(nrow(unmapped_countries) > 0) {
  cat("未映射的国家:\n")
  print(unmapped_countries$location_name)
}

# 1.3 处理台湾数据：实现一个中国原则的可视化表达
taiwan_data <- country_aapc_35plus[country_aapc_35plus$location_name == "台湾", ]
china_data <- country_aapc_35plus[country_aapc_35plus$location_name == "中国", ]

if(nrow(taiwan_data) > 0 && nrow(china_data) > 0) {
  # 计算中国大陆和台湾的加权平均AAPC
  combined_aapc <- mean(c(china_data$aapc, taiwan_data$aapc), na.rm = TRUE)

  # 更新中国的AAPC值
  country_aapc_35plus$aapc[country_aapc_35plus$location_name == "中国"] <- combined_aapc
  country_aapc_35plus$location_name_en[country_aapc_35plus$location_name == "中国"] <- "China"

  # 保留台湾条目但使用与中国相同的AAPC值，实现统一颜色显示
  country_aapc_35plus$aapc[country_aapc_35plus$location_name == "台湾"] <- combined_aapc
  country_aapc_35plus$location_name_en[country_aapc_35plus$location_name == "台湾"] <- "Taiwan"

  cat("一个中国原则实施：中国大陆和台湾地区使用统一AAPC:", round(combined_aapc, 2), "%\n")
  cat("地图上中国大陆和台湾地区将显示相同颜色\n")
} else {
  cat("Taiwan or China data not found for unified coloring\n")
}

cat("Countries after mapping and merging:", nrow(country_aapc_35plus), "\n")
cat("Mapped countries:", sum(country_aapc_35plus$location_name_en != country_aapc_35plus$location_name), "\n")

# 2. 创建十分位数分级（确保颜色均匀分布）
decile_breaks_35plus <- quantile(country_aapc_35plus$aapc, probs = seq(0, 1, 0.1))
decile_breaks_35plus[1] <- decile_breaks_35plus[1] - 0.01
decile_breaks_35plus[11] <- decile_breaks_35plus[11] + 0.01

# 创建标签
decile_labels_35plus <- c()
for(i in 1:(length(decile_breaks_35plus)-1)) {
  decile_labels_35plus <- c(decile_labels_35plus, 
                           sprintf("%.1f to %.1f", decile_breaks_35plus[i], decile_breaks_35plus[i+1]))
}

cat("\n35+ population decile classification:\n")
for(i in 1:length(decile_labels_35plus)) {
  cat(sprintf("Level %2d: %s\n", i, decile_labels_35plus[i]))
}

# 3. 使用更好的地图数据源
library(rnaturalearth)
library(sf)

# 获取世界地图数据
world <- ne_countries(scale = "medium", returnclass = "sf")

# 检查world数据的字段名称
cat("Available name fields in world data:\n")
name_fields <- colnames(world)[grep("name", colnames(world), ignore.case = TRUE)]
print(name_fields)

# 检查中国在world数据中的名称
china_entries <- world[grep("China|china", world$name, ignore.case = TRUE), ]
cat("China entries in world map:\n")
if(nrow(china_entries) > 0) {
  print(china_entries$name)
} else {
  cat("No China entries found, checking other name fields...\n")
  # 尝试其他可能的字段
  for(field in name_fields) {
    china_test <- world[grep("China|china", world[[field]], ignore.case = TRUE), ]
    if(nrow(china_test) > 0) {
      cat("Found China in field", field, ":", china_test[[field]], "\n")
    }
  }
}

# 合并世界地图与国家AAPC数据（使用正确的字段名称）
# 通常rnaturalearth使用'name'字段作为英文国家名称
world_with_aapc_35plus <- world %>%
  left_join(country_aapc_35plus, by = c("name" = "location_name_en"))

# 检查匹配情况
matched_count <- sum(!is.na(world_with_aapc_35plus$aapc))
total_count <- nrow(world_with_aapc_35plus)
cat("Successfully matched countries:", matched_count, "/", total_count, "\n")

# 4. 应用十分位数分级
world_with_aapc_35plus$aapc_decile <- cut(world_with_aapc_35plus$aapc,
                                         breaks = decile_breaks_35plus,
                                         labels = decile_labels_35plus,
                                         include.lowest = TRUE)

# 验证颜色分布
cat("\n35+ population color distribution:\n")
decile_counts_35plus <- table(country_aapc_35plus$aapc_decile <- cut(country_aapc_35plus$aapc,
                                                                    breaks = decile_breaks_35plus,
                                                                    include.lowest = TRUE))
for(i in 1:length(decile_counts_35plus)) {
  cat(sprintf("Decile %2d: %2d countries\n", i, decile_counts_35plus[i]))
}

# 5. 超精细颜色方案
ultra_fine_colors_35plus <- c(
  "#053061",  # 深蓝 - 最大下降
  "#2166ac",  # 蓝
  "#4393c3",  # 中蓝
  "#92c5de",  # 浅蓝
  "#d1e5f0",  # 很浅蓝
  "#f7f7f7",  # 接近白色 - 中性
  "#fddbc7",  # 很浅橙
  "#f4a582",  # 浅橙
  "#d6604d",  # 橙红
  "#b2182b"   # 深红 - 最大上升
)

# 6. 创建主地图
cat("\nCreating 35+ population main world map...\n")
main_map_35plus <- ggplot(world_with_aapc_35plus) +
  geom_sf(aes(fill = aapc_decile), color = "white", size = 0.1) +
  scale_fill_manual(values = ultra_fine_colors_35plus,
                   name = "AAPC (% per year)",
                   na.value = "grey85",
                   drop = FALSE) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 14, face = "bold"),
    legend.text = element_text(size = 9),
    legend.key.size = unit(0.8, "cm"),
    legend.key.width = unit(1.2, "cm"),
    plot.margin = margin(5, 5, 10, 5, "mm"),
    legend.margin = margin(t = 10),
    plot.title = element_text(size = 18, face = "bold", hjust = 0.5, margin = margin(b = 8)),
    plot.subtitle = element_text(size = 14, hjust = 0.5, margin = margin(b = 12)),
    plot.caption = element_text(size = 11, hjust = 0.5, margin = margin(t = 20),
                               lineheight = 1.2)
  ) +
  guides(fill = guide_legend(nrow = 2, byrow = TRUE,
                            title.position = "top", title.hjust = 0.5)) +
  labs(
    title = "Global Stroke DALYs Average Annual Percentage Change (1990-2021)",
    subtitle = "Population Aged ≥35 Years | 10-Level Decile Classification | Blue=Decline, Red=Increase",
    caption = paste(
      "Study Population: Global population aged ≥35 years | Indicator: Stroke DALYs | Period: 1990-2021",
      paste0("AAPC Range: ", round(min(country_aapc_35plus$aapc, na.rm = TRUE), 2), "% to ",
             round(max(country_aapc_35plus$aapc, na.rm = TRUE), 2), "% | ", nrow(country_aapc_35plus), " countries | Data Source: GBD 2021"),
      "Note: Taiwan and Mainland China display unified color following One-China principle",
      sep = "\n"
    )
  )

# 7. 创建区域放大地图函数
create_regional_map_35plus <- function(xlim, ylim, title) {
  ggplot(world_with_aapc_35plus) +
    geom_sf(aes(fill = aapc_decile), color = "white", size = 0.15) +
    scale_fill_manual(values = ultra_fine_colors_35plus, na.value = "grey85", guide = "none") +
    coord_sf(xlim = xlim, ylim = ylim, expand = FALSE) +
    theme_void() +
    theme(
      plot.title = element_text(size = 11, hjust = 0.5, face = "bold"),
      plot.margin = margin(3, 3, 3, 3, "mm"),
      panel.border = element_rect(color = "grey70", fill = NA, linewidth = 0.5)
    ) +
    ggtitle(title)
}

# 8. 创建6个区域放大地图
cat("Creating 35+ population regional zoom maps...\n")

caribbean_35plus <- create_regional_map_35plus(c(-95, -55), c(5, 35), "Caribbean and Central America")
persian_gulf_35plus <- create_regional_map_35plus(c(45, 65), c(20, 40), "Persian Gulf")
balkans_35plus <- create_regional_map_35plus(c(10, 30), c(35, 50), "Balkan Peninsula")
southeast_asia_35plus <- create_regional_map_35plus(c(90, 140), c(-15, 25), "South East Asia")
west_africa_med_35plus <- create_regional_map_35plus(c(-20, 45), c(0, 40), "West Africa & Eastern Mediterranean")
northern_europe_35plus <- create_regional_map_35plus(c(-10, 35), c(50, 75), "Northern Europe")

# 9. 组合所有地图
cat("Combining all 35+ population maps...\n")

regional_grid_35plus <- grid.arrange(
  caribbean_35plus, persian_gulf_35plus, balkans_35plus,
  southeast_asia_35plus, west_africa_med_35plus, northern_europe_35plus,
  ncol = 3, nrow = 2
)

final_plot_35plus <- grid.arrange(
  main_map_35plus,
  regional_grid_35plus,
  ncol = 1,
  heights = c(2.8, 1),
  top = textGrob("Global Stroke DALYs AAPC in Population Aged ≥35 Years (1990-2021)\nCountry-Level Decile Classification | Taiwan and Mainland China Unified Coloring",
                gp = gpar(fontsize = 16, fontface = "bold"))
)

# 10. 保存地图
cat("Saving 35+ population maps...\n")

ggsave("stroke_dalys_35plus_ultra_fine_complete_layout_1990_2021.png", 
       final_plot_35plus, width = 22, height = 18, dpi = 300, bg = "white")

ggsave("stroke_dalys_35plus_ultra_fine_main_map_1990_2021.png", 
       main_map_35plus, width = 20, height = 14, dpi = 300, bg = "white")

# 保存图例信息
legend_35plus <- data.frame(
  decile = 1:10,
  aapc_range = decile_labels_35plus,
  color_hex = ultra_fine_colors_35plus,
  countries_per_decile = as.numeric(decile_counts_35plus),
  percentage_of_total = round(as.numeric(decile_counts_35plus) / sum(decile_counts_35plus) * 100, 1)
)

write.csv(legend_35plus, "stroke_dalys_35plus_ultra_fine_legend_1990_2021.csv", row.names = FALSE)

cat("=== 35+ Population Stroke DALYs Visualization Completed Successfully! ===\n")
cat("Key Improvements Applied:\n")
cat("✅ Country-level data with comprehensive Chinese-English mapping\n")
cat("✅ Taiwan and Mainland China unified coloring (One-China principle)\n")
cat("✅ English titles, legends, and descriptions\n")
cat("✅ Improved country matching:", matched_count, "/", total_count, "countries\n")
cat("✅ 10-level decile classification ensuring balanced color distribution\n")
cat("✅ Consistent legend style with reference script\n")

cat("\nFiles saved:\n")
cat("- stroke_dalys_35plus_ultra_fine_complete_layout_1990_2021.png\n")
cat("- stroke_dalys_35plus_ultra_fine_main_map_1990_2021.png\n")
cat("- stroke_dalys_35plus_ultra_fine_legend_1990_2021.csv\n")

cat("\nData Summary:\n")
cat("- Total countries after merging:", nrow(country_aapc_35plus), "\n")
cat("- AAPC range:", round(min(country_aapc_35plus$aapc, na.rm = TRUE), 2), "% to",
    round(max(country_aapc_35plus$aapc, na.rm = TRUE), 2), "%\n")
cat("- Countries with AAPC decline:", sum(country_aapc_35plus$aapc < 0, na.rm = TRUE), "\n")
cat("- Countries with AAPC increase:", sum(country_aapc_35plus$aapc > 0, na.rm = TRUE), "\n")

cat("\nPerfect country-level visualization achieved!\n")
