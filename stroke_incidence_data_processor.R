# 脑卒中65岁以上人群发病率数据处理器
# 从GBD数据库提取1990-2021年完整数据

library(dplyr)
library(readr)

cat("=== 脑卒中65岁以上人群发病率数据处理 ===\n")

# 读取发病率数据文件
cat("读取发病率数据文件...\n")
incidence_data5 <- read.csv("死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-5/IHME-GBD_2021_DATA-336925ca-5.csv", 
                           stringsAsFactors = FALSE)
incidence_data6 <- read.csv("死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-6/IHME-GBD_2021_DATA-336925ca-6.csv", 
                           stringsAsFactors = FALSE)

# 合并数据
cat("合并发病率数据...\n")
all_incidence_data <- rbind(incidence_data5, incidence_data6)

# 筛选脑卒中65岁以上人群数据
cat("筛选脑卒中65岁以上人群数据...\n")
stroke_65plus <- all_incidence_data %>%
  filter(
    cause_name == "脑卒中",
    measure_name == "发病率",
    age_name %in% c("65-69岁", "70-74岁", "75-79岁", "80-84岁", "85岁以上"),
    metric_name == "率"  # 使用年龄标准化率
  ) %>%
  select(
    location_name,
    sex_name,
    age_name,
    year,
    val,
    upper,
    lower
  )

cat("数据筛选完成，共", nrow(stroke_65plus), "行数据\n")

# 检查数据覆盖情况
cat("=== 数据覆盖情况检查 ===\n")
cat("年份范围:", range(stroke_65plus$year), "\n")
cat("国家数量:", length(unique(stroke_65plus$location_name)), "\n")
cat("性别类型:", unique(stroke_65plus$sex_name), "\n")
cat("年龄组:", unique(stroke_65plus$age_name), "\n")

# 保存筛选后的数据
write.csv(stroke_65plus, "stroke_incidence_65plus_1990_2021.csv", 
          row.names = FALSE, fileEncoding = "UTF-8")

cat("数据已保存到: stroke_incidence_65plus_1990_2021.csv\n")

# 计算全球汇总数据
cat("=== 计算全球汇总数据 ===\n")

# 按年份、性别、年龄组计算全球平均值
global_summary <- stroke_65plus %>%
  group_by(year, sex_name, age_name) %>%
  summarise(
    global_rate = mean(val, na.rm = TRUE),
    global_upper = mean(upper, na.rm = TRUE),
    global_lower = mean(lower, na.rm = TRUE),
    .groups = 'drop'
  )

# 保存全球汇总数据
write.csv(global_summary, "stroke_incidence_global_summary_1990_2021.csv", 
          row.names = FALSE, fileEncoding = "UTF-8")

cat("全球汇总数据已保存到: stroke_incidence_global_summary_1990_2021.csv\n")

cat("=== 数据处理完成 ===\n")
