# 脑卒中Table 2复现使用指南

## 概述

本项目成功实现了脑卒中相关DALYs分析的Table 2复现，包括数据处理、AAPC计算、表格生成和可视化分析。

## 文件结构

### 核心脚本
1. **`stroke_table2_reproduction.R`** - 主分析脚本
   - 数据准备和清理
   - AAPC计算
   - Table 2格式生成

2. **`stroke_table2_visualization.R`** - 可视化脚本
   - 热力图生成
   - 条形图和比较图
   - 综合分析图表

3. **`stroke_real_data_processor.R`** - 真实数据处理脚本
   - GBD数据读取和清理
   - 数据标准化
   - 筛选和整合

4. **`install_packages.R`** - 包安装脚本
   - 自动检查和安装必要的R包

### 输出文件

#### 数据文件
- **`stroke_dalys_table2_detailed_results.csv`** - 详细分析结果
  - 包含所有危险因素和SDI分层的完整数据
  - 1990年和2019年DALYs值及不确定区间
  - AAPC及95%置信区间

- **`stroke_dalys_table2_formatted.csv`** - 格式化的Table 2
  - 按照学术论文标准格式化
  - 便于直接用于论文写作

- **`stroke_dalys_summary_statistics.csv`** - 汇总统计
  - 各SDI分层的AAPC统计摘要
  - 平均值、中位数、最小值、最大值

#### 可视化文件
- **`stroke_dalys_aapc_heatmap.png`** - AAPC热力图
  - 显示各危险因素在不同SDI分层的AAPC
  - 颜色编码：蓝色（下降）到红色（上升）

- **`stroke_dalys_global_aapc_barplot.png`** - 全球AAPC条形图
  - 全球层面各危险因素的AAPC排序
  - 包含95%置信区间

- **`stroke_dalys_sdi_comparison.png`** - SDI分层比较图
  - 主要危险因素在不同SDI的变化趋势
  - 线图显示跨SDI的模式

- **`stroke_dalys_combined_analysis.png`** - 综合分析图
  - 热力图和条形图的组合展示

#### 报告文件
- **`stroke_table2_comprehensive_report.md`** - 综合分析报告
  - 完整的方法学描述
  - 主要发现和临床意义
  - 局限性和结论

## 使用方法

### 1. 环境准备
```bash
# 确保R已安装（推荐4.5.1或更高版本）
# 运行包安装脚本
Rscript install_packages.R
```

### 2. 运行分析
```bash
# 主分析（生成Table 2数据）
Rscript stroke_table2_reproduction.R

# 可视化分析（生成图表）
Rscript stroke_table2_visualization.R
```

### 3. 处理真实数据（可选）
```bash
# 如果有真实的GBD数据文件
Rscript stroke_real_data_processor.R
```

## 主要功能

### 1. AAPC计算
- 使用对数线性回归方法
- 公式：`AAPC = (ln(DALYs_2019) - ln(DALYs_1990)) / (2019 - 1990) × 100%`
- 自动计算95%置信区间

### 2. 数据分层
- **SDI分层**：Global, High SDI, High-middle SDI, Middle SDI, Low-middle SDI, Low SDI
- **危险因素**：9个主要危险因素
- **时间点**：1990年和2019年

### 3. 可视化特色
- **热力图**：直观显示AAPC模式
- **条形图**：排序显示全球趋势
- **比较图**：跨SDI趋势分析
- **专业配色**：符合学术标准

## 结果解读

### AAPC值含义
- **负值**：DALYs下降，疾病负担减轻
- **正值**：DALYs上升，疾病负担加重
- **置信区间**：不包含0表示变化有统计学意义

### 主要发现
1. **传统危险因素**（吸烟、高胆固醇）显著下降
2. **新兴危险因素**（空气污染、高血糖）呈上升趋势
3. **SDI差异**：不同发展水平地区呈现不同模式

## 自定义选项

### 修改危险因素
在`stroke_table2_reproduction.R`中修改`risk_factors`向量：
```r
risk_factors <- c(
  "你的危险因素1",
  "你的危险因素2",
  # 添加更多...
)
```

### 调整时间范围
修改AAPC计算函数的默认参数：
```r
calculate_aapc <- function(dalys_1990, dalys_2019, year_start = 1990, year_end = 2019)
```

### 自定义可视化
在`stroke_table2_visualization.R`中调整：
- 颜色方案
- 图表尺寸
- 标签和标题

## 故障排除

### 常见问题
1. **包安装失败**
   - 检查网络连接
   - 尝试更换CRAN镜像
   - 手动安装失败的包

2. **数据读取错误**
   - 检查文件路径
   - 确认文件编码（UTF-8）
   - 验证CSV格式

3. **图表生成失败**
   - 检查图形设备
   - 确认输出目录权限
   - 调整图片尺寸参数

### 技术支持
- 查看R控制台的错误信息
- 检查`warnings()`获取详细警告
- 确保所有依赖包正确安装

## 扩展应用

### 适用场景
1. **其他疾病的DALYs分析**
2. **不同时间段的趋势分析**
3. **其他健康指标的AAPC计算**
4. **多因素风险评估**

### 数据要求
- 至少两个时间点的数据
- 包含不确定区间的数值
- 标准化的分层变量
- 清晰的危险因素分类

---

*使用指南版本: 1.0*
*最后更新: 2025-06-26*
