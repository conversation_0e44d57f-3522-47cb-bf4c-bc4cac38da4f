# 脑卒中65岁以上人群DALYs分析摘要报告 (1990-2021)

library(dplyr)

# 加载AAPC结果
country_aapc <- read.csv("stroke_dalys_65plus_aapc_results_1990_2021.csv")

cat("=== 脑卒中65岁以上人群DALYs年均变化率分析报告 (1990-2021) ===\n\n")

# 1. 基本统计
cat("1. 基本统计信息:\n")
cat("   分析国家数量:", nrow(country_aapc), "\n")
cat("   AAPC范围:", round(min(country_aapc$aapc), 2), "% 到", round(max(country_aapc$aapc), 2), "%\n")
cat("   平均AAPC:", round(mean(country_aapc$aapc), 2), "%\n")
cat("   中位数AAPC:", round(median(country_aapc$aapc), 2), "%\n")
cat("   标准差:", round(sd(country_aapc$aapc), 2), "%\n\n")

# 2. 分布分析
cat("2. AAPC分布分析:\n")
negative_count <- sum(country_aapc$aapc < 0)
positive_count <- sum(country_aapc$aapc > 0)
zero_count <- sum(country_aapc$aapc == 0)

cat("   下降趋势国家 (AAPC < 0):", negative_count, "个 (", 
    round(negative_count/nrow(country_aapc)*100, 1), "%)\n")
cat("   上升趋势国家 (AAPC > 0):", positive_count, "个 (", 
    round(positive_count/nrow(country_aapc)*100, 1), "%)\n")
cat("   无变化国家 (AAPC = 0):", zero_count, "个\n\n")

# 3. 极值分析
cat("3. 极值分析:\n")
cat("   下降最快的5个国家:\n")
top_decreasing <- country_aapc %>% 
  arrange(aapc) %>% 
  head(5)
for(i in 1:nrow(top_decreasing)) {
  cat("     ", i, ".", top_decreasing$location_name[i], ":", 
      round(top_decreasing$aapc[i], 2), "%\n")
}

cat("\n   上升最快的5个国家:\n")
top_increasing <- country_aapc %>% 
  arrange(desc(aapc)) %>% 
  head(5)
for(i in 1:nrow(top_increasing)) {
  cat("     ", i, ".", top_increasing$location_name[i], ":", 
      round(top_increasing$aapc[i], 2), "%\n")
}

# 4. 分位数分析
cat("\n4. 分位数分析:\n")
quantiles <- quantile(country_aapc$aapc, probs = c(0.1, 0.25, 0.5, 0.75, 0.9))
cat("   10%分位数:", round(quantiles[1], 2), "%\n")
cat("   25%分位数:", round(quantiles[2], 2), "%\n")
cat("   50%分位数 (中位数):", round(quantiles[3], 2), "%\n")
cat("   75%分位数:", round(quantiles[4], 2), "%\n")
cat("   90%分位数:", round(quantiles[5], 2), "%\n\n")

# 5. 按变化幅度分类
cat("5. 按变化幅度分类:\n")
country_aapc$change_category <- cut(country_aapc$aapc, 
                                   breaks = c(-Inf, -3, -2, -1, 0, 1, Inf),
                                   labels = c("大幅下降(< -3%)", "中度下降(-3% to -2%)", 
                                            "轻度下降(-2% to -1%)", "轻微下降(-1% to 0%)",
                                            "轻微上升(0% to 1%)", "中度上升(> 1%)"))

category_summary <- table(country_aapc$change_category)
for(i in 1:length(category_summary)) {
  cat("   ", names(category_summary)[i], ":", category_summary[i], "个国家\n")
}

# 6. 保存详细结果
detailed_results <- country_aapc %>%
  arrange(aapc) %>%
  mutate(
    rank = row_number(),
    change_direction = ifelse(aapc < 0, "下降", "上升"),
    change_magnitude = case_when(
      abs(aapc) >= 3 ~ "大幅",
      abs(aapc) >= 2 ~ "中度", 
      abs(aapc) >= 1 ~ "轻度",
      TRUE ~ "轻微"
    )
  ) %>%
  select(rank, location_name, aapc, change_direction, change_magnitude, change_category)

write.csv(detailed_results, "stroke_dalys_65plus_detailed_results_1990_2021.csv", row.names = FALSE)

cat("\n6. 文件输出:\n")
cat("   详细结果已保存至: stroke_dalys_65plus_detailed_results_1990_2021.csv\n")
cat("   地图文件:\n")
cat("   - stroke_dalys_65plus_balanced_colors_complete_layout_1990_2021.png\n")
cat("   - stroke_dalys_65plus_balanced_colors_main_map_1990_2021.png\n\n")

cat("=== 分析完成 ===\n")

# 显示前10个结果
cat("\n前10个国家详细信息:\n")
print(detailed_results[1:10, c("rank", "location_name", "aapc", "change_direction", "change_magnitude")])

cat("\n后10个国家详细信息:\n")
print(detailed_results[(nrow(detailed_results)-9):nrow(detailed_results), 
                      c("rank", "location_name", "aapc", "change_direction", "change_magnitude")])
