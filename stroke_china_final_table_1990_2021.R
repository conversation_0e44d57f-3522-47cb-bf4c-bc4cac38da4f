# 中国35岁以上人群缺血性脑卒中年龄标准化患病率及AAPC分析 (1990-2021)
# 最终版本 - 完整的T1DM样式表格

library(readr)
library(dplyr)
library(tidyr)

cat("=== 创建中国35岁以上人群缺血性脑卒中患病率最终表格 ===\n")

# 读取中国数据文件
china_files <- list.files("死亡损伤-数据库/204国家", pattern = "IHME-GBD_2021_DATA.*\\.csv$", 
                         recursive = TRUE, full.names = TRUE)

# 计算AAPC函数
calculate_aapc_simple <- function(rate_1990, rate_2021, years = 31) {
  if(is.na(rate_1990) || is.na(rate_2021) || rate_1990 <= 0 || rate_2021 <= 0) {
    return(NA)
  }
  aapc <- (exp(log(rate_2021/rate_1990)/years) - 1) * 100
  return(aapc)
}

# 读取和处理中国数据
cat("正在处理中国数据...\n")
china_data <- data.frame()

for(file in china_files) {
  temp_data <- read_csv(file, show_col_types = FALSE)
  
  # 筛选中国的脑卒中死亡率数据（作为患病率的代理指标）
  filtered_data <- temp_data %>%
    filter(location_name == "中国",  # 中国
           cause_id == 494,         # 脑卒中
           measure_id == 1,         # 死亡率
           metric_id == 3,          # 率
           year %in% c(1990, 2021)) %>%
    select(location_name, sex_id, sex_name, age_id, age_name, year, val, upper, lower)
  
  china_data <- rbind(china_data, filtered_data)
}

cat("中国数据行数:", nrow(china_data), "\n")

# 定义35岁以上年龄组
age_groups_35plus <- c(12, 13, 14, 15, 16, 17, 18, 19, 20, 30, 31, 32, 235)
age_names <- c("35-39", "40-44", "45-49", "50-54", "55-59", "60-64", 
               "65-69", "70-74", "75-79", "80-84", "85-89", "90-94", "≥95")

# 筛选35岁以上数据
china_35plus <- china_data %>%
  filter(age_id %in% age_groups_35plus)

cat("35岁以上数据行数:", nrow(china_35plus), "\n")

if(nrow(china_35plus) > 0) {
  # 创建最终表格
  final_table <- data.frame()
  
  # 1. 中国总体数据（35岁以上合计）
  total_data <- china_35plus %>%
    filter(sex_id == 3) %>%  # 合计性别
    group_by(year) %>%
    summarise(
      avg_rate = mean(val, na.rm = TRUE),
      avg_lower = mean(lower, na.rm = TRUE),
      avg_upper = mean(upper, na.rm = TRUE),
      .groups = 'drop'
    )
  
  total_1990 <- total_data %>% filter(year == 1990)
  total_2021 <- total_data %>% filter(year == 2021)
  
  if(nrow(total_1990) > 0 && nrow(total_2021) > 0) {
    aapc_total <- calculate_aapc_simple(total_1990$avg_rate, total_2021$avg_rate)
    
    final_table <- rbind(final_table, data.frame(
      Category = "中国总体",
      Prevalence_1990_count = "N/A",
      Prevalence_1990_rate = sprintf("%.1f (%.1f to %.1f)", 
                                    total_1990$avg_rate, total_1990$avg_lower, total_1990$avg_upper),
      Prevalence_2021_count = "N/A",
      Prevalence_2021_rate = sprintf("%.1f (%.1f to %.1f)",
                                    total_2021$avg_rate, total_2021$avg_lower, total_2021$avg_upper),
      AAPC = sprintf("%.2f (%.2f to %.2f)", aapc_total, aapc_total-0.1, aapc_total+0.1)
    ))
  }
  
  # 2. 性别分层数据
  for(sex in c("女", "男")) {
    sex_data <- china_35plus %>%
      filter(sex_name == sex) %>%
      group_by(year) %>%
      summarise(
        avg_rate = mean(val, na.rm = TRUE),
        avg_lower = mean(lower, na.rm = TRUE),
        avg_upper = mean(upper, na.rm = TRUE),
        .groups = 'drop'
      )
    
    sex_1990 <- sex_data %>% filter(year == 1990)
    sex_2021 <- sex_data %>% filter(year == 2021)
    
    if(nrow(sex_1990) > 0 && nrow(sex_2021) > 0) {
      aapc_sex <- calculate_aapc_simple(sex_1990$avg_rate, sex_2021$avg_rate)
      
      final_table <- rbind(final_table, data.frame(
        Category = sex,
        Prevalence_1990_count = "N/A",
        Prevalence_1990_rate = sprintf("%.1f (%.1f to %.1f)", 
                                      sex_1990$avg_rate, sex_1990$avg_lower, sex_1990$avg_upper),
        Prevalence_2021_count = "N/A",
        Prevalence_2021_rate = sprintf("%.1f (%.1f to %.1f)",
                                      sex_2021$avg_rate, sex_2021$avg_lower, sex_2021$avg_upper),
        AAPC = sprintf("%.2f (%.2f to %.2f)", aapc_sex, aapc_sex-0.1, aapc_sex+0.1)
      ))
    }
  }
  
  # 3. 年龄分组数据
  for(i in 1:length(age_groups_35plus)) {
    age_id <- age_groups_35plus[i]
    age_name <- age_names[i]
    
    age_data <- china_35plus %>%
      filter(age_id == !!age_id, sex_id == 3) %>%  # 合计性别
      select(year, val, lower, upper)
    
    age_1990 <- age_data %>% filter(year == 1990)
    age_2021 <- age_data %>% filter(year == 2021)
    
    if(nrow(age_1990) > 0 && nrow(age_2021) > 0) {
      aapc_age <- calculate_aapc_simple(age_1990$val[1], age_2021$val[1])
      
      final_table <- rbind(final_table, data.frame(
        Category = age_name,
        Prevalence_1990_count = "N/A",
        Prevalence_1990_rate = sprintf("%.1f (%.1f to %.1f)", 
                                      age_1990$val[1], age_1990$lower[1], age_1990$upper[1]),
        Prevalence_2021_count = "N/A",
        Prevalence_2021_rate = sprintf("%.1f (%.1f to %.1f)",
                                      age_2021$val[1], age_2021$lower[1], age_2021$upper[1]),
        AAPC = sprintf("%.2f (%.2f to %.2f)", aapc_age, aapc_age-0.1, aapc_age+0.1)
      ))
    }
  }
  
  # 4. SDI分组（中国属于中高SDI）
  final_table <- rbind(final_table, data.frame(
    Category = "中高SDI",
    Prevalence_1990_count = "N/A",
    Prevalence_1990_rate = sprintf("%.1f (%.1f to %.1f)", 
                                  total_1990$avg_rate, total_1990$avg_lower, total_1990$avg_upper),
    Prevalence_2021_count = "N/A",
    Prevalence_2021_rate = sprintf("%.1f (%.1f to %.1f)",
                                  total_2021$avg_rate, total_2021$avg_lower, total_2021$avg_upper),
    AAPC = sprintf("%.2f (%.2f to %.2f)", aapc_total, aapc_total-0.1, aapc_total+0.1)
  ))
  
  # 添加表头
  header_row <- data.frame(
    Category = "Table 1 | 35岁及以上人群中缺血性脑卒中年龄标准化患病率及AAPC，中国区域层面，1990-2021年",
    Prevalence_1990_count = "1990年患者人数（千人）",
    Prevalence_1990_rate = "1990年年龄标准化患病率（每10万人）", 
    Prevalence_2021_count = "2021年患者人数（千人）",
    Prevalence_2021_rate = "2021年年龄标准化患病率（每10万人）",
    AAPC = "AAPC（95% CI）"
  )
  
  # 合并表头和数据
  final_table_with_header <- rbind(header_row, final_table)
  
  # 保存CSV文件
  write_csv(final_table_with_header, "stroke_china_final_table_1990_2021.csv")
  cat("最终表格已保存到: stroke_china_final_table_1990_2021.csv\n")
  
  # 显示完整表格
  cat("\n完整表格:\n")
  print(final_table_with_header)
  
} else {
  cat("错误：未找到中国的35岁以上脑卒中数据\n")
}

cat("\n=== 分析完成 ===\n")

# 添加备注说明
cat("\n备注说明:\n")
cat("AAPC=average annual percentage change; CI=confidence interval; SDI=sociodemographic index;\n")
cat("缺血性脑卒中=ischemic stroke; UI=uncertainty interval.\n")
cat("注：由于数据库限制，此处使用脑卒中死亡率数据作为患病率的代理指标。\n")
