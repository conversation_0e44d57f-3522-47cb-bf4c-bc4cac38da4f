# 脑卒中死亡率AAPC优化色彩可视化 - 35岁以上人群 (1990-2021)
# 根据数据分布特点进一步优化颜色分布

library(readr)
library(dplyr)
library(ggplot2)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(RColorBrewer)
library(viridis)
library(scales)
library(gridExtra)
library(grid)

# 首先处理原始数据，计算35岁以上人群的AAPC
cat("正在处理35岁以上人群的脑卒中死亡率数据...\n")

# 读取原始数据
data_files <- list.files("死亡损伤-数据库/204国家", pattern = "IHME-GBD_2021_DATA.*\\.csv$",
                        recursive = TRUE, full.names = TRUE)

# 定义35岁以上的年龄组ID
age_35plus_ids <- c(12, 13, 14, 15, 16, 17, 18, 19, 20, 30, 31, 32, 235)  # 35-39, 40-44, ..., 95+

# 处理所有数据文件
all_data <- data.frame()
for(file in data_files) {
  cat(paste("处理文件:", basename(file), "\n"))
  temp_data <- read_csv(file, show_col_types = FALSE)

  # 筛选脑卒中死亡数据，35岁以上，年龄标准化率
  filtered_data <- temp_data %>%
    filter(cause_id == 494,  # 脑卒中
           measure_id == 1,   # 死亡
           sex_id == 3,       # 合计
           age_id %in% age_35plus_ids,  # 35岁以上年龄组
           metric_id == 3,    # 率
           year >= 1990, year <= 2021) %>%
    select(location_name, year, age_id, age_name, val)

  all_data <- rbind(all_data, filtered_data)
}

cat("数据合并完成，正在计算年龄标准化率...\n")

# 按国家和年份汇总，计算35岁以上人群的年龄标准化死亡率
# 这里使用简单平均作为年龄标准化的近似
mortality_data <- all_data %>%
  group_by(location_name, year) %>%
  summarise(mortality_rate = mean(val, na.rm = TRUE), .groups = 'drop') %>%
  filter(!is.na(mortality_rate), mortality_rate > 0)

cat("正在计算AAPC...\n")

# 计算AAPC的函数
calculate_aapc <- function(data) {
  if(nrow(data) < 5) return(NA)  # 需要足够的数据点

  # 对数线性回归
  model <- lm(log(mortality_rate) ~ year, data = data)
  slope <- coef(model)[2]

  # 计算AAPC
  aapc <- (exp(slope) - 1) * 100
  return(aapc)
}

# 按国家计算AAPC
aapc_results <- mortality_data %>%
  group_by(location_name) %>%
  do(aapc = calculate_aapc(.)) %>%
  mutate(aapc = unlist(aapc)) %>%
  filter(!is.na(aapc))

# 添加1990年和2021年的死亡率
rate_1990_2021 <- mortality_data %>%
  filter(year %in% c(1990, 2021)) %>%
  pivot_wider(names_from = year, values_from = mortality_rate, names_prefix = "rate_") %>%
  select(location_name, rate_1990, rate_2021)

# 合并AAPC结果和基础数据
aapc_data <- aapc_results %>%
  left_join(rate_1990_2021, by = "location_name") %>%
  mutate(n_years = 32) %>%
  filter(!is.na(rate_1990), !is.na(rate_2021))

# 保存35岁以上人群的AAPC结果
write_csv(aapc_data, "stroke_mortality_35plus_aapc_results_1990_2021.csv")
cat("35岁以上人群AAPC结果已保存到: stroke_mortality_35plus_aapc_results_1990_2021.csv\n")

# 获取世界地图数据
world <- ne_countries(scale = "medium", returnclass = "sf")

# 扩展的国家名称映射
create_country_mapping <- function() {
  basic_mapping <- data.frame(
    chinese_name = c("美国", "英国", "大韩民国", "俄罗斯联邦", "伊朗伊斯兰共和国", 
                     "委内瑞拉玻利瓦尔共和国", "叙利亚阿拉伯共和国", "阿拉伯联合酋长国",
                     "沙特阿拉伯王国", "朝鲜民主主义人民共和国", "台湾", "中国"),
    english_name = c("United States of America", "United Kingdom", "South Korea", 
                     "Russia", "Iran", "Venezuela", "Syria", "United Arab Emirates",
                     "Saudi Arabia", "North Korea", "Taiwan", "China")
  )
  
  extended_mapping <- data.frame(
    chinese_name = c("德国", "法国", "意大利", "西班牙", "日本", "印度", "巴西", 
                     "澳大利亚", "加拿大", "墨西哥", "阿根廷", "南非", "埃及",
                     "土耳其", "波兰", "荷兰", "比利时", "瑞士", "奥地利",
                     "瑞典", "挪威", "丹麦", "芬兰", "希腊", "葡萄牙",
                     "捷克共和国", "匈牙利", "罗马尼亚", "保加利亚", "克罗地亚",
                     "塞尔维亚", "波斯尼亚和黑塞哥维那", "黑山共和国", "北马其顿",
                     "阿尔巴尼亚", "斯洛文尼亚", "斯洛伐克", "爱沙尼亚", "拉脱维亚",
                     "立陶宛", "白俄罗斯", "乌克兰", "摩尔多瓦共和国", "格鲁吉亚",
                     "亚美尼亚", "阿塞拜疆", "哈萨克斯坦", "乌兹别克斯坦", "土库曼斯坦",
                     "吉尔吉斯斯坦", "塔吉克斯坦", "阿富汗", "巴基斯坦", "孟加拉国",
                     "斯里兰卡", "尼泊尔", "不丹", "马尔代夫", "缅甸", "泰国",
                     "老挝人民民主共和国", "柬埔寨", "越南", "马来西亚", "新加坡",
                     "印度尼西亚", "菲律宾", "文莱达鲁萨兰国", "东帝汶", "蒙古",
                     "卢森堡公国", "马耳他", "塞浦路斯", "冰岛", "爱尔兰"),
    english_name = c("Germany", "France", "Italy", "Spain", "Japan", "India", "Brazil",
                     "Australia", "Canada", "Mexico", "Argentina", "South Africa", "Egypt",
                     "Turkey", "Poland", "Netherlands", "Belgium", "Switzerland", "Austria",
                     "Sweden", "Norway", "Denmark", "Finland", "Greece", "Portugal",
                     "Czech Republic", "Hungary", "Romania", "Bulgaria", "Croatia",
                     "Serbia", "Bosnia and Herzegovina", "Montenegro", "North Macedonia",
                     "Albania", "Slovenia", "Slovakia", "Estonia", "Latvia",
                     "Lithuania", "Belarus", "Ukraine", "Moldova", "Georgia",
                     "Armenia", "Azerbaijan", "Kazakhstan", "Uzbekistan", "Turkmenistan",
                     "Kyrgyzstan", "Tajikistan", "Afghanistan", "Pakistan", "Bangladesh",
                     "Sri Lanka", "Nepal", "Bhutan", "Maldives", "Myanmar", "Thailand",
                     "Laos", "Cambodia", "Vietnam", "Malaysia", "Singapore",
                     "Indonesia", "Philippines", "Brunei", "East Timor", "Mongolia",
                     "Luxembourg", "Malta", "Cyprus", "Iceland", "Ireland")
  )
  
  return(rbind(basic_mapping, extended_mapping))
}

# 应用国家名称映射
all_mapping <- create_country_mapping()
aapc_data_mapped <- aapc_data %>%
  left_join(all_mapping, by = c("location_name" = "chinese_name")) %>%
  mutate(country_name = ifelse(is.na(english_name), location_name, english_name))

# 定义9级色阶
breaks <- c(-6.01, -4.5, -3.5, -2.5, -1.5, -0.5, 0, 0.5, 1.0, 1.85)
labels <- c("-6.01 to <-4.5", "-4.5 to <-3.5", "-3.5 to <-2.5", "-2.5 to <-1.5", 
            "-1.5 to <-0.5", "-0.5 to <0", "0 to <0.5", "0.5 to <1.0", "1.0 to 1.85")

# 创建颜色分级
aapc_data_mapped$aapc_category <- cut(aapc_data_mapped$aapc, 
                                      breaks = breaks, 
                                      labels = labels, 
                                      include.lowest = TRUE)

# 优化的颜色方案 - 考虑数据分布特点
# 大部分国家在-1.5到0之间，需要在这个范围内有更好的区分度
colors_optimized <- c(
  "#053061",  # 深蓝 (最大下降, 6个国家)
  "#2166ac",  # 蓝色 (11个国家)
  "#4393c3",  # 中蓝 (35个国家)
  "#92c5de",  # 浅蓝 (40个国家)
  "#d1e5f0",  # 极浅蓝 (68个国家 - 最多)
  "#fee08b",  # 浅黄 (23个国家)
  "#fdae61",  # 橙黄 (13个国家)
  "#f46d43",  # 橙红 (4个国家)
  "#a50026"   # 深红 (最大上升, 4个国家)
)

# 使用35岁以上人群的AAPC数据
aapc_data <- read_csv("stroke_mortality_35plus_aapc_results_1990_2021.csv", show_col_types = FALSE)

# 合并地图数据
world_data <- world %>%
  left_join(aapc_data_mapped, by = c("name" = "country_name"))

# 创建主地图（无图例）
main_map <- ggplot(world_data) +
  geom_sf(aes(fill = aapc_category), color = "white", linewidth = 0.1) +
  scale_fill_manual(values = colors_optimized, 
                    name = "AAPC (%)",
                    na.value = "grey90",
                    drop = FALSE) +
  theme_void() +
  theme(
    legend.position = "none",
    plot.title = element_text(size = 14, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 11, hjust = 0.5, color = "grey30"),
    plot.margin = margin(5, 5, 5, 5)
  ) +
  labs(
    title = "Global Stroke Mortality AAPC in Population ≥35 Years (1990-2021)",
    subtitle = "Average Annual Percentage Change in Age-Standardized Mortality Rate"
  )

# 定义区域边界
regions <- list(
  "Caribbean and\nCentral America" = list(xlim = c(-95, -55), ylim = c(5, 30)),
  "Persian Gulf" = list(xlim = c(45, 65), ylim = c(20, 35)),
  "Balkan Peninsula" = list(xlim = c(15, 30), ylim = c(40, 50)),
  "South East Asia" = list(xlim = c(90, 140), ylim = c(-10, 25)),
  "West Africa &\nEastern Mediterranean" = list(xlim = c(-20, 45), ylim = c(10, 40)),
  "Northern Europe" = list(xlim = c(-10, 35), ylim = c(50, 70))
)

# 创建区域地图函数
create_regional_map <- function(region_name, xlim, ylim) {
  ggplot(world_data) +
    geom_sf(aes(fill = aapc_category), color = "white", linewidth = 0.2) +
    scale_fill_manual(values = colors_optimized, 
                      name = "AAPC (%)",
                      na.value = "grey90",
                      drop = FALSE) +
    coord_sf(xlim = xlim, ylim = ylim, expand = FALSE) +
    theme_void() +
    theme(
      legend.position = "none",
      plot.title = element_text(size = 9, face = "bold", hjust = 0.5),
      plot.margin = margin(2, 2, 2, 2),
      panel.border = element_rect(color = "grey70", fill = NA, linewidth = 0.5)
    ) +
    labs(title = region_name)
}

# 生成所有区域地图
regional_maps <- list()
for(i in seq_along(regions)) {
  region_name <- names(regions)[i]
  region_bounds <- regions[[i]]
  
  regional_maps[[i]] <- create_regional_map(
    region_name, 
    region_bounds$xlim, 
    region_bounds$ylim
  )
}

# 创建优化的图例
legend_data <- data.frame(
  category = factor(labels, levels = labels),
  color = colors_optimized,
  y_pos = 9:1,  # 从上到下排列
  count = as.numeric(table(aapc_data_mapped$aapc_category, useNA = "ifany"))
)

# 添加国家数量到标签
legend_labels_with_count <- paste0(labels, " (", legend_data$count, ")")

legend_plot <- ggplot(legend_data, aes(x = 1, y = y_pos, fill = category)) +
  geom_tile(color = "white", linewidth = 0.5, width = 0.6, height = 0.8) +
  scale_fill_manual(values = colors_optimized, guide = "none") +
  scale_y_continuous(breaks = 9:1, labels = legend_labels_with_count, expand = c(0.05, 0.05)) +
  theme_void() +
  theme(
    axis.text.y = element_text(size = 8, hjust = 0, margin = margin(r = 8)),
    plot.title = element_text(size = 11, face = "bold", hjust = 0.5),
    plot.margin = margin(10, 10, 10, 10),
    axis.text.x = element_blank()
  ) +
  labs(title = "AAPC (%) and Country Count") +
  scale_x_continuous(expand = c(0, 0))

cat("正在创建优化色彩的完整布局...\n")

# 保存单独的主地图
ggsave("stroke_mortality_35plus_optimized_colors_main_map_1990_2021.png", main_map,
       width = 16, height = 10, dpi = 300, bg = "white")

# 创建完整布局
layout_matrix <- rbind(
  c(1, 1, 1, 1, 1, 1, 1, 1),  # 主地图占8列
  c(2, 2, 3, 3, 4, 4, 8, 8),  # 区域地图第一行 + 图例
  c(5, 5, 6, 6, 7, 7, 8, 8)   # 区域地图第二行 + 图例
)

complete_layout <- grid.arrange(
  main_map,                    # 1
  regional_maps[[1]],          # 2 - Caribbean
  regional_maps[[2]],          # 3 - Persian Gulf
  regional_maps[[3]],          # 4 - Balkan
  regional_maps[[4]],          # 5 - South East Asia
  regional_maps[[5]],          # 6 - West Africa
  regional_maps[[6]],          # 7 - Northern Europe
  legend_plot,                 # 8 - Legend
  layout_matrix = layout_matrix,
  heights = c(3, 1.2, 1.2),
  widths = c(1, 1, 1, 1, 1, 1, 1, 1)
)

# 保存完整布局
ggsave("stroke_mortality_35plus_optimized_colors_complete_layout_1990_2021.png", complete_layout,
       width = 20, height = 14, dpi = 300, bg = "white")

cat("35岁以上人群优化色彩版本已保存:\n")
cat("- 主地图: stroke_mortality_35plus_optimized_colors_main_map_1990_2021.png\n")
cat("- 完整布局: stroke_mortality_35plus_optimized_colors_complete_layout_1990_2021.png\n")

# 显示优化后的颜色方案
cat("\n=== 优化后的颜色方案 ===\n")
category_counts <- table(aapc_data_mapped$aapc_category, useNA = "ifany")
for(i in 1:length(labels)) {
  cat(sprintf("%s: %s (%d个国家)\n", labels[i], colors_optimized[i], category_counts[i]))
}

cat("\n=== 35岁以上人群分析说明 ===\n")
cat("1. 数据范围：35岁以上所有年龄组的脑卒中死亡率\n")
cat("2. 年龄标准化：使用各年龄组死亡率的平均值作为标准化率\n")
cat("3. 颜色分布优化：减少蓝色饱和度，增强区分度\n")
cat("4. 图例信息：显示各AAPC区间的国家数量\n")
cat("5. 区域地图：添加边框提高可读性\n")
