# 最终版精细颜色分级脑卒中地图 - 完全模仿T1DM样式
library(ggplot2)
library(dplyr)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(RColorBrewer)
library(gridExtra)
library(grid)

# 读取数据
data <- read.csv("脑卒中65岁以上人群患病率分析表.csv", stringsAsFactors = FALSE, fileEncoding = "UTF-8")

# 提取地区AAPC数据
region_aapc <- data %>%
  filter(分类 %in% c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", "高收入", 
                    "中欧、东欧和中亚", "拉丁美洲和加勒比海", "北非和中东")) %>%
  select(分类, AAPC = colnames(data)[14])

# 创建国家映射
country_region_mapping <- data.frame(
  country = c(
    # 高收入国家 (AAPC: -0.73%)
    "United States of America", "Germany", "United Kingdom", "France", "Italy", "Canada", "Spain", 
    "Netherlands", "Belgium", "Switzerland", "Austria", "Sweden", "Norway", "Denmark", "Finland", 
    "Ireland", "Portugal", "Greece", "Israel", "Luxembourg", "Iceland", "Malta", "Cyprus",
    "Australia", "New Zealand", "Japan", "South Korea", "Singapore",
    
    # 中欧、东欧和中亚 (AAPC: +0.73%)
    "Russia", "Poland", "Ukraine", "Romania", "Hungary", "Belarus", "Bulgaria", "Serbia",
    "Croatia", "Bosnia and Herzegovina", "Albania", "North Macedonia", "Moldova", "Montenegro", 
    "Kazakhstan", "Uzbekistan", "Tajikistan", "Kyrgyzstan", "Turkmenistan", "Georgia", 
    "Armenia", "Azerbaijan", "Czech Republic", "Slovakia", "Slovenia", "Estonia", "Latvia", "Lithuania",
    
    # 东南亚、东亚和大洋洲 (AAPC: +0.58%)
    "China", "Indonesia", "Philippines", "Vietnam", "Thailand", "Myanmar", "Malaysia", 
    "Cambodia", "Laos", "Brunei", "Timor-Leste", "Papua New Guinea", "Fiji", "Solomon Islands", 
    "Vanuatu", "Samoa", "Kiribati", "Tonga", "Micronesia", "Palau", "Marshall Islands", 
    "Nauru", "Tuvalu", "North Korea", "Mongolia",
    
    # 南亚 (AAPC: -0.58%)
    "India", "Pakistan", "Bangladesh", "Afghanistan", "Nepal", "Sri Lanka", "Bhutan", "Maldives",
    
    # 拉丁美洲和加勒比海 (AAPC: +0.65%)
    "Brazil", "Mexico", "Colombia", "Argentina", "Peru", "Venezuela", "Chile", "Ecuador",
    "Guatemala", "Cuba", "Bolivia", "Haiti", "Dominican Republic", "Honduras", "Paraguay",
    "Nicaragua", "El Salvador", "Costa Rica", "Panama", "Uruguay", "Jamaica", 
    "Trinidad and Tobago", "Guyana", "Suriname", "Belize", "Barbados",
    
    # 北非和中东 (AAPC: +0.82%)
    "Egypt", "Iran", "Turkey", "Iraq", "Saudi Arabia", "Yemen", "Syria", "Jordan", "Lebanon",
    "United Arab Emirates", "Oman", "Kuwait", "Qatar", "Bahrain", "Algeria", "Morocco", 
    "Sudan", "Tunisia", "Libya",
    
    # 撒哈拉以南非洲 (AAPC: +0.69%)
    "Nigeria", "Ethiopia", "South Africa", "Kenya", "Uganda", "Tanzania", "Ghana", "Mozambique", 
    "Madagascar", "Cameroon", "Angola", "Niger", "Burkina Faso", "Mali", "Malawi", "Zambia", 
    "Somalia", "Senegal", "Chad", "Zimbabwe", "Guinea", "Rwanda", "Benin", "Burundi",
    "South Sudan", "Togo", "Sierra Leone", "Liberia", "Central African Republic",
    "Mauritania", "Eritrea", "Gambia", "Botswana", "Namibia", "Gabon", "Lesotho", 
    "Guinea-Bissau", "Equatorial Guinea", "Mauritius", "Eswatini", "Djibouti", 
    "Comoros", "Cape Verde", "São Tomé and Príncipe"
  ),
  region = c(
    rep("高收入", 28),
    rep("中欧、东欧和中亚", 28),
    rep("东南亚、东亚和大洋洲", 25),
    rep("南亚", 8),
    rep("拉丁美洲和加勒比海", 26),
    rep("北非和中东", 19),
    rep("撒哈拉以南非洲", 44)
  )
)

# 合并数据
country_data <- country_region_mapping %>%
  left_join(region_aapc, by = c("region" = "分类"))

# 获取世界地图数据
world <- ne_countries(scale = "medium", returnclass = "sf")

# 合并地图数据并创建精细的颜色分组（完全模仿T1DM样式的数值范围）
world_data <- world %>%
  left_join(country_data, by = c("name" = "country")) %>%
  mutate(
    # 模仿T1DM地图的精确分组方式
    aapc_group = case_when(
      is.na(AAPC) ~ "No data",
      AAPC < -0.65 ~ "-0.82 to <-0.65",     # 高收入国家 (-0.73)
      AAPC >= -0.65 & AAPC < -0.50 ~ "-0.65 to <-0.50",  # 南亚 (-0.58)
      AAPC >= -0.50 & AAPC < 0.55 ~ "-0.50 to <0.55",    # 接近零的变化
      AAPC >= 0.55 & AAPC < 0.62 ~ "0.55 to <0.62",      # 东南亚、东亚和大洋洲 (0.58)
      AAPC >= 0.62 & AAPC < 0.67 ~ "0.62 to <0.67",      # 拉丁美洲和加勒比海 (0.65)
      AAPC >= 0.67 & AAPC < 0.71 ~ "0.67 to <0.71",      # 撒哈拉以南非洲 (0.69)
      AAPC >= 0.71 & AAPC < 0.78 ~ "0.71 to <0.78",      # 中欧、东欧和中亚 (0.73)
      AAPC >= 0.78 ~ "0.78 to <0.90"                      # 北非和中东 (0.82)
    ),
    aapc_group = factor(aapc_group, levels = c("-0.82 to <-0.65", "-0.65 to <-0.50", "-0.50 to <0.55", 
                                               "0.55 to <0.62", "0.62 to <0.67", "0.67 to <0.71",
                                               "0.71 to <0.78", "0.78 to <0.90", "No data"))
  )

# 定义完全模仿T1DM的颜色方案（从深蓝到深红的专业渐变）
colors <- c(
  "#08306b",    # 最深蓝色：-0.82 to <-0.65 (高收入国家 - 强烈下降)
  "#2171b5",    # 深蓝色：-0.65 to <-0.50 (南亚 - 中等下降)
  "#c6dbef",    # 浅蓝色：-0.50 to <0.55 (接近零变化)
  "#fff2cc",    # 浅黄色：0.55 to <0.62 (东南亚、东亚和大洋洲 - 轻微上升)
  "#fed976",    # 黄色：0.62 to <0.67 (拉丁美洲和加勒比海 - 中等上升)
  "#fd8d3c",    # 橙色：0.67 to <0.71 (撒哈拉以南非洲 - 显著上升)
  "#e31a1c",    # 红色：0.71 to <0.78 (中欧、东欧和中亚 - 强烈上升)
  "#800026",    # 深红色：0.78 to <0.90 (北非和中东 - 极强上升)
  "#969696"     # 灰色：无数据
)
names(colors) <- levels(world_data$aapc_group)

# 创建最终主地图
final_main_map <- ggplot(world_data) +
  geom_sf(aes(fill = aapc_group), color = "white", size = 0.1) +
  scale_fill_manual(values = colors, name = "AAPC (%)", drop = FALSE) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 14, face = "bold"),
    legend.text = element_text(size = 10),
    plot.title = element_text(size = 18, face = "bold", hjust = 0.5, margin = margin(b = 10)),
    plot.subtitle = element_text(size = 14, hjust = 0.5, margin = margin(b = 15)),
    legend.key.size = unit(0.7, "cm"),
    legend.key.width = unit(1, "cm"),
    panel.background = element_rect(fill = "aliceblue", color = NA),
    plot.background = element_rect(fill = "white", color = NA),
    plot.margin = margin(15, 15, 15, 15),
    legend.margin = margin(t = 10),
    legend.box.spacing = unit(0.5, "cm")
  ) +
  guides(fill = guide_legend(nrow = 2, byrow = TRUE, 
                            title.position = "top", title.hjust = 0.5)) +
  labs(
    title = "Global Stroke Prevalence Change Among Adults ≥65 Years",
    subtitle = "Average Annual Percentage Change (AAPC), 1990-2019"
  ) +
  coord_sf(crs = "+proj=robin")

# 创建区域放大图
create_final_regional_map <- function(xlim, ylim, title) {
  ggplot(world_data) +
    geom_sf(aes(fill = aapc_group), color = "white", size = 0.3) +
    scale_fill_manual(values = colors, guide = "none") +
    coord_sf(xlim = xlim, ylim = ylim, expand = FALSE) +
    theme_void() +
    theme(
      plot.title = element_text(size = 10, face = "bold", hjust = 0.5),
      panel.border = element_rect(color = "black", fill = NA, linewidth = 1.2),
      plot.background = element_rect(fill = "white", color = NA),
      plot.margin = margin(3, 3, 3, 3)
    ) +
    labs(title = title)
}

# 创建区域放大图
caribbean_final <- create_final_regional_map(c(-90, -60), c(10, 25), "Caribbean and\nCentral America")
persian_gulf_final <- create_final_regional_map(c(45, 60), c(24, 32), "Persian Gulf")
balkans_final <- create_final_regional_map(c(15, 30), c(40, 48), "Balkan Peninsula")
southeast_asia_final <- create_final_regional_map(c(95, 140), c(-10, 25), "South East Asia")
west_africa_final <- create_final_regional_map(c(-20, 10), c(4, 20), "West Africa")
northern_europe_final <- create_final_regional_map(c(5, 30), c(55, 70), "Northern Europe")

# 保存最终主地图
ggsave("stroke_final_detailed_main_map.png", final_main_map, 
       width = 20, height = 14, dpi = 300, bg = "white")

# 创建最终区域放大图组合
regional_final_combined <- grid.arrange(caribbean_final, persian_gulf_final, balkans_final, 
                                        southeast_asia_final, west_africa_final, northern_europe_final,
                                        ncol = 6, 
                                        top = textGrob("Regional Detail Views", 
                                                      gp = gpar(fontsize = 16, fontface = "bold")))

# 保存最终区域放大图
ggsave("stroke_final_detailed_regional_maps.png", regional_final_combined, 
       width = 20, height = 5, dpi = 300, bg = "white")

# 显示主图
print(final_main_map)

# 打印最终详细统计信息
cat("\n🌍 脑卒中65岁以上人群AAPC最终详细分析 (1990-2019)\n")
cat(paste(rep("=", 70), collapse = ""), "\n\n")

cat("📈 患病率上升地区 (按严重程度排序):\n")
cat("🔴 极强上升 (0.78-0.90%): 北非和中东 (+0.82%)\n")
cat("🟠 强烈上升 (0.71-0.78%): 中欧、东欧和中亚 (+0.73%)\n") 
cat("🟡 显著上升 (0.67-0.71%): 撒哈拉以南非洲 (+0.69%)\n")
cat("🟨 中等上升 (0.62-0.67%): 拉丁美洲和加勒比海 (+0.65%)\n")
cat("💛 轻微上升 (0.55-0.62%): 东南亚、东亚和大洋洲 (+0.58%)\n\n")

cat("📉 患病率下降地区:\n")
cat("🔵 强烈下降 (-0.82 to -0.65%): 高收入国家 (-0.73%)\n")
cat("🔷 中等下降 (-0.65 to -0.50%): 南亚 (-0.58%)\n\n")

cat("📊 最终精细颜色分组统计:\n")
print(table(world_data$aapc_group, useNA = "ifany"))

cat("\n🎨 最终颜色图例 (完全模仿T1DM样式):\n")
cat("🔵 最深蓝色: 强烈下降趋势 (高收入发达国家)\n")
cat("🔷 深蓝色: 中等下降趋势 (南亚地区)\n")
cat("💙 浅蓝色: 基本稳定 (变化微小)\n")
cat("💛 浅黄色: 轻微上升 (东南亚、东亚和大洋洲)\n")
cat("🟡 黄色: 中等上升 (拉丁美洲和加勒比海)\n")
cat("🟠 橙色: 显著上升 (撒哈拉以南非洲)\n")
cat("🔴 红色: 强烈上升 (中欧、东欧和中亚)\n")
cat("🔴 深红色: 极强上升 (北非和中东地区)\n")
cat("⬜ 灰色: 数据缺失\n")
