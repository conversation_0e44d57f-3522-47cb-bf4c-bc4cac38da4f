# 脑卒中死亡率AAPC全球地图可视化 (1990-2021)
# 参考T1DM分析的9级色阶设计

library(readr)
library(dplyr)
library(ggplot2)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(RColorBrewer)
library(viridis)
library(scales)

# 读取AAPC结果
aapc_data <- read_csv("stroke_mortality_aapc_results_1990_2021.csv", show_col_types = FALSE)

# 获取世界地图数据
world <- ne_countries(scale = "medium", returnclass = "sf")

# 国家名称映射（中文到英文）
country_mapping <- data.frame(
  chinese_name = c("美国", "英国", "大韩民国", "俄罗斯联邦", "伊朗伊斯兰共和国", 
                   "委内瑞拉玻利瓦尔共和国", "叙利亚阿拉伯共和国", "阿拉伯联合酋长国",
                   "沙特阿拉伯王国", "朝鲜民主主义人民共和国", "台湾", "中国"),
  english_name = c("United States of America", "United Kingdom", "South Korea", 
                   "Russia", "Iran", "Venezuela", "Syria", "United Arab Emirates",
                   "Saudi Arabia", "North Korea", "Taiwan", "China")
)

# 更多国家名称映射
additional_mapping <- data.frame(
  chinese_name = c("德国", "法国", "意大利", "西班牙", "日本", "印度", "巴西", 
                   "澳大利亚", "加拿大", "墨西哥", "阿根廷", "南非", "埃及",
                   "土耳其", "波兰", "荷兰", "比利时", "瑞士", "奥地利",
                   "瑞典", "挪威", "丹麦", "芬兰", "希腊", "葡萄牙",
                   "捷克共和国", "匈牙利", "罗马尼亚", "保加利亚", "克罗地亚",
                   "塞尔维亚", "波斯尼亚和黑塞哥维那", "黑山共和国", "北马其顿",
                   "阿尔巴尼亚", "斯洛文尼亚", "斯洛伐克", "爱沙尼亚", "拉脱维亚",
                   "立陶宛", "白俄罗斯", "乌克兰", "摩尔多瓦共和国", "格鲁吉亚",
                   "亚美尼亚", "阿塞拜疆", "哈萨克斯坦", "乌兹别克斯坦", "土库曼斯坦",
                   "吉尔吉斯斯坦", "塔吉克斯坦", "阿富汗", "巴基斯坦", "孟加拉国",
                   "斯里兰卡", "尼泊尔", "不丹", "马尔代夫", "缅甸", "泰国",
                   "老挝人民民主共和国", "柬埔寨", "越南", "马来西亚", "新加坡",
                   "印度尼西亚", "菲律宾", "文莱达鲁萨兰国", "东帝汶", "蒙古"),
  english_name = c("Germany", "France", "Italy", "Spain", "Japan", "India", "Brazil",
                   "Australia", "Canada", "Mexico", "Argentina", "South Africa", "Egypt",
                   "Turkey", "Poland", "Netherlands", "Belgium", "Switzerland", "Austria",
                   "Sweden", "Norway", "Denmark", "Finland", "Greece", "Portugal",
                   "Czech Republic", "Hungary", "Romania", "Bulgaria", "Croatia",
                   "Serbia", "Bosnia and Herzegovina", "Montenegro", "North Macedonia",
                   "Albania", "Slovenia", "Slovakia", "Estonia", "Latvia",
                   "Lithuania", "Belarus", "Ukraine", "Moldova", "Georgia",
                   "Armenia", "Azerbaijan", "Kazakhstan", "Uzbekistan", "Turkmenistan",
                   "Kyrgyzstan", "Tajikistan", "Afghanistan", "Pakistan", "Bangladesh",
                   "Sri Lanka", "Nepal", "Bhutan", "Maldives", "Myanmar", "Thailand",
                   "Laos", "Cambodia", "Vietnam", "Malaysia", "Singapore",
                   "Indonesia", "Philippines", "Brunei", "East Timor", "Mongolia")
)

# 合并映射表
all_mapping <- rbind(country_mapping, additional_mapping)

# 应用国家名称映射
aapc_data_mapped <- aapc_data %>%
  left_join(all_mapping, by = c("location_name" = "chinese_name")) %>%
  mutate(country_name = ifelse(is.na(english_name), location_name, english_name))

# 定义9级色阶（基于实际AAPC范围：-6.01 to 1.85）
breaks <- c(-6.01, -4.5, -3.5, -2.5, -1.5, -0.5, 0, 0.5, 1.0, 1.85)
labels <- c("-6.01 to <-4.5", "-4.5 to <-3.5", "-3.5 to <-2.5", "-2.5 to <-1.5", 
            "-1.5 to <-0.5", "-0.5 to <0", "0 to <0.5", "0.5 to <1.0", "1.0 to 1.85")

# 创建颜色分级
aapc_data_mapped$aapc_category <- cut(aapc_data_mapped$aapc, 
                                      breaks = breaks, 
                                      labels = labels, 
                                      include.lowest = TRUE)

# 定义颜色（深蓝到红色渐变）
colors <- c("#08306b", "#2171b5", "#4292c6", "#6baed6", "#9ecae1", 
            "#c6dbef", "#fee391", "#fec44f", "#d94801")

# 合并地图数据
world_data <- world %>%
  left_join(aapc_data_mapped, by = c("name" = "country_name"))

# 创建主地图
main_map <- ggplot(world_data) +
  geom_sf(aes(fill = aapc_category), color = "white", size = 0.1) +
  scale_fill_manual(values = colors, 
                    name = "AAPC (%)",
                    na.value = "grey90",
                    drop = FALSE) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 10),
    legend.key.size = unit(0.8, "cm"),
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 12, hjust = 0.5)
  ) +
  labs(
    title = "Global Stroke Mortality AAPC in Population ≥65 Years (1990-2021)",
    subtitle = "Average Annual Percentage Change in Age-Standardized Mortality Rate"
  ) +
  guides(fill = guide_legend(nrow = 1, byrow = TRUE))

# 保存主地图
ggsave("stroke_mortality_global_aapc_map_1990_2021.png", main_map, 
       width = 16, height = 10, dpi = 300, bg = "white")

cat("主地图已保存: stroke_mortality_global_aapc_map_1990_2021.png\n")

# 显示数据统计
cat("\n=== 数据统计 ===\n")
cat("总国家数:", nrow(aapc_data), "\n")
cat("成功映射国家数:", sum(!is.na(world_data$aapc)), "\n")
cat("AAPC范围:", round(min(aapc_data$aapc, na.rm = TRUE), 2), "到", 
    round(max(aapc_data$aapc, na.rm = TRUE), 2), "\n")

# 显示各分级的国家数量
cat("\n=== 各分级国家数量 ===\n")
print(table(aapc_data_mapped$aapc_category, useNA = "ifany"))

# 创建6个区域放大图
cat("\n正在创建区域放大图...\n")

# 定义区域边界
regions <- list(
  "Caribbean and Central America" = list(xlim = c(-95, -55), ylim = c(5, 30)),
  "Persian Gulf" = list(xlim = c(45, 65), ylim = c(20, 35)),
  "Balkan Peninsula" = list(xlim = c(15, 30), ylim = c(40, 50)),
  "South East Asia" = list(xlim = c(90, 140), ylim = c(-10, 25)),
  "West Africa & Eastern Mediterranean" = list(xlim = c(-20, 45), ylim = c(10, 40)),
  "Northern Europe" = list(xlim = c(-10, 35), ylim = c(50, 70))
)

# 创建区域地图函数
create_regional_map <- function(region_name, xlim, ylim) {
  ggplot(world_data) +
    geom_sf(aes(fill = aapc_category), color = "white", size = 0.2) +
    scale_fill_manual(values = colors,
                      name = "AAPC (%)",
                      na.value = "grey90",
                      drop = FALSE) +
    coord_sf(xlim = xlim, ylim = ylim, expand = FALSE) +
    theme_void() +
    theme(
      legend.position = "none",
      plot.title = element_text(size = 10, face = "bold", hjust = 0.5),
      axis.text = element_blank(),
      axis.ticks = element_blank(),
      panel.grid = element_blank()
    ) +
    labs(title = region_name)
}

# 生成所有区域地图
regional_maps <- list()
for(i in 1:length(regions)) {
  region_name <- names(regions)[i]
  region_bounds <- regions[[i]]

  regional_maps[[i]] <- create_regional_map(
    region_name,
    region_bounds$xlim,
    region_bounds$ylim
  )

  cat("创建区域地图:", region_name, "\n")
}

# 使用gridExtra组合区域地图
library(gridExtra)

# 创建区域地图组合
regional_combined <- grid.arrange(
  regional_maps[[1]], regional_maps[[2]], regional_maps[[3]],
  regional_maps[[4]], regional_maps[[5]], regional_maps[[6]],
  ncol = 3, nrow = 2
)

# 保存区域地图
ggsave("stroke_mortality_regional_maps_1990_2021.png", regional_combined,
       width = 15, height = 10, dpi = 300, bg = "white")

cat("区域地图已保存: stroke_mortality_regional_maps_1990_2021.png\n")
