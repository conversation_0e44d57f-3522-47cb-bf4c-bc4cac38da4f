# 改进图例的脑卒中发病率地图可视化

library(ggplot2)
library(dplyr)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(RColorBrewer)
library(gridExtra)
library(grid)
library(scales)

cat("=== 创建改进图例的脑卒中发病率地图 ===\n")

# 读取数据
stroke_data <- read.csv("脑卒中65岁以上人群发病率最终分析表_1990_2021.csv", 
                       stringsAsFactors = FALSE, 
                       fileEncoding = "UTF-8")

region_aapc <- stroke_data %>%
  filter(分类 %in% c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", 
                    "高收入", "中欧、东欧和中亚", "拉丁美洲和加勒比海", 
                    "北非和中东")) %>%
  select(地区 = 分类, AAPC = `AAPC...`)

# 获取世界地图
world <- ne_countries(scale = "medium", returnclass = "sf")

# 创建国家映射
create_detailed_country_mapping <- function(world_data) {
  country_names <- world_data$name_en
  
  mapping <- data.frame(
    country = country_names,
    region = case_when(
      country_names %in% c("United States of America", "Canada", "Germany", "France", "United Kingdom", 
                          "Italy", "Spain", "Netherlands", "Belgium", "Switzerland", 
                          "Austria", "Sweden", "Norway", "Denmark", "Finland", "Ireland", 
                          "Portugal", "Greece", "Australia", "New Zealand", "Japan", 
                          "South Korea", "Israel", "Luxembourg", "Iceland", "Czech Republic",
                          "Slovenia", "Estonia", "Latvia", "Lithuania", "Slovakia", "Poland",
                          "Hungary", "Croatia", "Cyprus", "Malta") ~ "高收入",
      
      country_names %in% c("Nigeria", "Ethiopia", "South Africa", "Kenya", "Uganda", "Tanzania", 
                          "Ghana", "Mozambique", "Madagascar", "Cameroon", "Angola", "Niger", 
                          "Burkina Faso", "Mali", "Malawi", "Zambia", "Somalia", "Senegal", 
                          "Chad", "Zimbabwe", "Guinea", "Rwanda", "Benin", "Burundi", 
                          "South Sudan", "Togo", "Sierra Leone", "Liberia", "Botswana", 
                          "Namibia", "Gabon", "Lesotho", "Gambia", "Guinea-Bissau", 
                          "Equatorial Guinea", "Mauritius", "Eswatini", "Djibouti", 
                          "Comoros", "Cape Verde", "São Tomé and Príncipe", "Seychelles",
                          "Central African Republic", "Republic of the Congo", 
                          "Democratic Republic of the Congo", "Ivory Coast", "Mauritania") ~ "撒哈拉以南非洲",
      
      country_names %in% c("China", "Indonesia", "Philippines", "Vietnam", "Thailand", 
                          "Myanmar", "Malaysia", "Cambodia", "Laos", "Singapore", 
                          "Brunei", "East Timor", "Papua New Guinea", "Fiji", 
                          "Solomon Islands", "Vanuatu", "Samoa", "Tonga", "Kiribati", 
                          "Tuvalu", "Nauru", "Palau", "Marshall Islands", 
                          "Federated States of Micronesia", "Mongolia", "North Korea") ~ "东南亚、东亚和大洋洲",
      
      country_names %in% c("India", "Pakistan", "Bangladesh", "Afghanistan", "Nepal", 
                          "Sri Lanka", "Bhutan", "Maldives") ~ "南亚",
      
      country_names %in% c("Russia", "Ukraine", "Belarus", "Moldova", "Romania", "Bulgaria", 
                          "Serbia", "Bosnia and Herzegovina", "Montenegro", "Albania", 
                          "North Macedonia", "Kosovo", "Kazakhstan", "Uzbekistan", 
                          "Turkmenistan", "Kyrgyzstan", "Tajikistan", "Armenia", 
                          "Azerbaijan", "Georgia") ~ "中欧、东欧和中亚",
      
      country_names %in% c("Brazil", "Mexico", "Argentina", "Colombia", "Peru", "Venezuela", 
                          "Chile", "Ecuador", "Guatemala", "Cuba", "Bolivia", "Haiti", 
                          "Dominican Republic", "Honduras", "Paraguay", "Nicaragua", 
                          "Costa Rica", "Panama", "Uruguay", "Jamaica", "Trinidad and Tobago", 
                          "Bahamas", "Belize", "Barbados", "Saint Lucia", "Grenada", 
                          "Saint Vincent and the Grenadines", "Antigua and Barbuda", 
                          "Dominica", "Saint Kitts and Nevis", "Suriname", "Guyana",
                          "El Salvador") ~ "拉丁美洲和加勒比海",
      
      country_names %in% c("Egypt", "Iran", "Turkey", "Iraq", "Saudi Arabia", "Yemen", 
                          "Syria", "Jordan", "Lebanon", "Libya", "Tunisia", "Algeria", 
                          "Morocco", "Sudan", "Oman", "Kuwait", "United Arab Emirates", 
                          "Qatar", "Bahrain") ~ "北非和中东",
      
      TRUE ~ "其他"
    ),
    stringsAsFactors = FALSE
  )
  
  return(mapping)
}

country_mapping <- create_detailed_country_mapping(world)
world_with_aapc <- world %>%
  left_join(country_mapping, by = c("name_en" = "country")) %>%
  left_join(region_aapc, by = c("region" = "地区"))

# 创建改进的颜色方案和图例
aapc_range <- range(region_aapc$AAPC, na.rm = TRUE)
cat("AAPC范围:", round(aapc_range[1], 2), "到", round(aapc_range[2], 2), "\n")

# 创建更细致的分级
breaks <- c(-2.5, -2.0, -1.5, -1.0, -0.5, 0)
break_labels <- c("≤ -2.0%", "-2.0% to -1.5%", "-1.5% to -1.0%", 
                  "-1.0% to -0.5%", "-0.5% to 0%", "> 0%")

# 创建对应的颜色
colors <- c("#08519C", "#3182BD", "#6BAED6", "#9ECAE1", "#C6DBEF", "#F7FBFF")

# 创建主地图（改进图例版本）
main_map_enhanced <- ggplot(world_with_aapc) +
  geom_sf(aes(fill = AAPC), color = "white", size = 0.1) +
  scale_fill_gradientn(
    colors = colors,
    values = scales::rescale(breaks),
    name = "年均百分比变化 (AAPC)\n1990-2021年",
    na.value = "grey90",
    breaks = c(-2.0, -1.5, -1.0, -0.5),
    labels = c("-2.0%", "-1.5%", "-1.0%", "-0.5%"),
    guide = guide_colorbar(
      title.position = "top",
      title.hjust = 0.5,
      title.theme = element_text(size = 14, face = "bold"),
      label.theme = element_text(size = 12),
      barwidth = 20,
      barheight = 1.5,
      frame.colour = "black",
      frame.linewidth = 0.5,
      ticks.colour = "black",
      ticks.linewidth = 0.5
    )
  ) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.margin = margin(t = 20, b = 10),
    plot.title = element_text(size = 18, face = "bold", hjust = 0.5, margin = margin(b = 10)),
    plot.subtitle = element_text(size = 14, hjust = 0.5, margin = margin(b = 15)),
    plot.caption = element_text(size = 11, hjust = 0.5, margin = margin(t = 15)),
    plot.margin = margin(10, 10, 10, 10)
  ) +
  labs(
    title = "脑卒中65岁以上人群发病率变化趋势 (1990-2021)",
    subtitle = "年均百分比变化 (AAPC) 按地区分布",
    caption = "负值表示发病率下降 | 基于32年完整时间序列数据 | 数据来源: GBD 2021"
  )

# 保存改进图例的主地图
ggsave("stroke_incidence_enhanced_legend_main_map_1990_2021.png", 
       main_map_enhanced, 
       width = 18, height = 12, dpi = 300, bg = "white")

cat("改进图例的主地图已保存\n")

# 创建详细的图例说明图
legend_explanation <- ggplot() +
  geom_rect(aes(xmin = 0, xmax = 1, ymin = 0, ymax = 1), 
            fill = "white", color = "black", size = 1) +
  annotate("text", x = 0.5, y = 0.9, 
           label = "AAPC (年均百分比变化) 解释", 
           size = 8, fontface = "bold", hjust = 0.5) +
  annotate("text", x = 0.05, y = 0.75, 
           label = "• AAPC < 0: 发病率下降趋势", 
           size = 6, hjust = 0, color = "#08519C") +
  annotate("text", x = 0.05, y = 0.65, 
           label = "• AAPC = 0: 发病率保持稳定", 
           size = 6, hjust = 0, color = "black") +
  annotate("text", x = 0.05, y = 0.55, 
           label = "• AAPC > 0: 发病率上升趋势", 
           size = 6, hjust = 0, color = "#B10026") +
  annotate("text", x = 0.05, y = 0.4, 
           label = "颜色越深蓝 = 下降越快", 
           size = 6, hjust = 0, color = "#08519C", fontface = "bold") +
  annotate("text", x = 0.05, y = 0.3, 
           label = "颜色越浅 = 变化越小", 
           size = 6, hjust = 0, color = "#6BAED6") +
  annotate("text", x = 0.05, y = 0.15, 
           label = "例如: AAPC = -1.0% 表示每年平均下降1%", 
           size = 5, hjust = 0, color = "darkgreen", fontface = "italic") +
  theme_void() +
  theme(plot.margin = margin(10, 10, 10, 10)) +
  coord_cartesian(xlim = c(0, 1), ylim = c(0, 1))

# 保存图例说明
ggsave("stroke_aapc_legend_explanation.png", 
       legend_explanation, 
       width = 8, height = 6, dpi = 300, bg = "white")

cat("图例说明已保存\n")

# 创建带数值标签的AAPC条形图（改进版）
aapc_bar_data <- region_aapc %>%
  arrange(AAPC) %>%
  mutate(
    地区 = factor(地区, levels = 地区),
    颜色 = case_when(
      AAPC <= -2.0 ~ "快速下降 (≤-2.0%)",
      AAPC <= -1.0 ~ "中等下降 (-2.0% to -1.0%)",
      AAPC <= -0.5 ~ "缓慢下降 (-1.0% to -0.5%)",
      TRUE ~ "极缓慢下降 (>-0.5%)"
    )
  )

enhanced_bar_plot <- ggplot(aapc_bar_data, aes(x = 地区, y = AAPC, fill = 颜色)) +
  geom_col(width = 0.7) +
  geom_text(aes(label = paste0(AAPC, "%")), 
            hjust = -0.1, size = 4, fontface = "bold") +
  geom_hline(yintercept = 0, linetype = "dashed", color = "black", size = 0.8) +
  scale_fill_manual(
    values = c("快速下降 (≤-2.0%)" = "#08519C",
               "中等下降 (-2.0% to -1.0%)" = "#3182BD",
               "缓慢下降 (-1.0% to -0.5%)" = "#6BAED6",
               "极缓慢下降 (>-0.5%)" = "#9ECAE1"),
    name = "变化程度"
  ) +
  coord_flip() +
  theme_minimal() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 10),
    axis.title = element_text(size = 12, face = "bold"),
    axis.text = element_text(size = 11),
    axis.text.y = element_text(size = 10),
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 12, hjust = 0.5),
    plot.caption = element_text(size = 10, hjust = 0.5),
    panel.grid.major.y = element_line(color = "grey90"),
    panel.grid.minor = element_blank(),
    plot.margin = margin(15, 15, 15, 15)
  ) +
  labs(
    title = "各地区脑卒中65岁以上人群发病率AAPC (1990-2021)",
    subtitle = "年均百分比变化 - 数值标注版",
    x = "地区",
    y = "年均百分比变化 (AAPC, %)",
    caption = "所有地区均呈下降趋势，但下降速度存在显著差异"
  ) +
  scale_y_continuous(limits = c(-2.5, 0.2), breaks = seq(-2.5, 0, 0.5))

# 保存改进的条形图
ggsave("stroke_incidence_enhanced_aapc_barplot_1990_2021.png", 
       enhanced_bar_plot, 
       width = 14, height = 10, dpi = 300, bg = "white")

cat("改进的AAPC条形图已保存\n")

cat("=== 改进图例的可视化完成 ===\n")
cat("生成的文件:\n")
cat("1. stroke_incidence_enhanced_legend_main_map_1990_2021.png - 改进图例的主地图\n")
cat("2. stroke_aapc_legend_explanation.png - 图例说明图\n")
cat("3. stroke_incidence_enhanced_aapc_barplot_1990_2021.png - 改进的条形图\n")
