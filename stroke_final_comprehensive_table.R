# 创建最终的脑卒中65岁以上人群发病率综合分析表
# 基于1990-2021年完整32年时间序列数据

library(dplyr)
library(readr)

cat("=== 创建最终综合分析表 ===\n")

# 读取数据
global_data <- read.csv("stroke_incidence_global_summary_1990_2021.csv", 
                       stringsAsFactors = FALSE)
country_data <- read.csv("stroke_incidence_65plus_1990_2021.csv", 
                        stringsAsFactors = FALSE)

# 改进的AAPC计算函数
calculate_aapc_with_ci <- function(data, value_col = "global_rate") {
  if(nrow(data) < 3) return(list(aapc = NA, ci_lower = NA, ci_upper = NA))
  
  years <- data$year
  values <- data[[value_col]]
  
  valid_idx <- !is.na(values) & values > 0
  if(sum(valid_idx) < 3) return(list(aapc = NA, ci_lower = NA, ci_upper = NA))
  
  years <- years[valid_idx]
  values <- values[valid_idx]
  
  # 对数线性回归
  log_values <- log(values)
  model <- lm(log_values ~ years)
  
  # 获取斜率和置信区间
  slope <- coef(model)[2]
  slope_ci <- confint(model)[2, ]
  
  # 计算AAPC和置信区间
  aapc <- (exp(slope) - 1) * 100
  ci_lower <- (exp(slope_ci[1]) - 1) * 100
  ci_upper <- (exp(slope_ci[2]) - 1) * 100
  
  return(list(aapc = aapc, ci_lower = ci_lower, ci_upper = ci_upper))
}

# 计算全球总体数据
global_overall_data <- global_data %>%
  filter(sex_name == "合计") %>%
  group_by(year) %>%
  summarise(global_rate = mean(global_rate, na.rm = TRUE), .groups = "drop")

global_overall_stats <- calculate_aapc_with_ci(global_overall_data)

# 计算性别分组数据
sex_data <- global_data %>%
  filter(sex_name %in% c("男", "女")) %>%
  group_by(sex_name, year) %>%
  summarise(global_rate = mean(global_rate, na.rm = TRUE), .groups = "drop") %>%
  group_by(sex_name) %>%
  group_modify(~ {
    stats <- calculate_aapc_with_ci(.x)
    data.frame(
      rate_1990 = .x$global_rate[.x$year == 1990],
      rate_2021 = .x$global_rate[.x$year == 2021],
      aapc = stats$aapc,
      ci_lower = stats$ci_lower,
      ci_upper = stats$ci_upper
    )
  })

# 计算年龄组数据
age_data <- global_data %>%
  filter(sex_name == "合计") %>%
  group_by(age_name) %>%
  group_modify(~ {
    stats <- calculate_aapc_with_ci(.x)
    data.frame(
      rate_1990 = .x$global_rate[.x$year == 1990],
      rate_2021 = .x$global_rate[.x$year == 2021],
      aapc = stats$aapc,
      ci_lower = stats$ci_lower,
      ci_upper = stats$ci_upper
    )
  })

# 计算地区数据（如果有super region数据）
region_data <- NULL
if(file.exists("死亡损伤-数据库/GBD super region/IHME-GBD_2021_DATA-26cc51e1-1.csv")) {
  super_region_data <- read.csv("死亡损伤-数据库/GBD super region/IHME-GBD_2021_DATA-26cc51e1-1.csv", 
                               stringsAsFactors = FALSE)
  
  if("发病率" %in% unique(super_region_data$measure_name)) {
    region_incidence <- super_region_data %>%
      filter(
        cause_name == "脑卒中",
        measure_name == "发病率",
        age_name %in% c("65-69岁", "70-74岁", "75-79岁", "80-84岁", "85岁以上"),
        metric_name == "率",
        sex_name == "合计"
      ) %>%
      group_by(location_name, year) %>%
      summarise(avg_rate = mean(val, na.rm = TRUE), .groups = "drop") %>%
      group_by(location_name) %>%
      group_modify(~ {
        stats <- calculate_aapc_with_ci(.x, "avg_rate")
        data.frame(
          rate_1990 = .x$avg_rate[.x$year == 1990],
          rate_2021 = .x$avg_rate[.x$year == 2021],
          aapc = stats$aapc,
          ci_lower = stats$ci_lower,
          ci_upper = stats$ci_upper
        )
      })
    
    # 筛选主要地区
    main_regions <- c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", 
                     "高收入", "中欧、东欧和中亚", "拉丁美洲和加勒比海", 
                     "北非和中东")
    
    region_data <- region_incidence %>%
      filter(location_name %in% main_regions)
  }
}

# 创建最终分析表
analysis_table <- data.frame(
  分类 = character(),
  "1990年发病率" = numeric(),
  "2021年发病率" = numeric(),
  "AAPC" = numeric(),
  "95%CI下限" = numeric(),
  "95%CI上限" = numeric(),
  "数据年数" = character(),
  stringsAsFactors = FALSE
)

# 添加全球数据
analysis_table <- rbind(analysis_table, data.frame(
  分类 = "全球",
  "X1990年发病率" = round(global_overall_data$global_rate[global_overall_data$year == 1990], 2),
  "X2021年发病率" = round(global_overall_data$global_rate[global_overall_data$year == 2021], 2),
  "AAPC" = round(global_overall_stats$aapc, 2),
  "X95.CI下限" = round(global_overall_stats$ci_lower, 2),
  "X95.CI上限" = round(global_overall_stats$ci_upper, 2),
  "数据年数" = "32年 (1990-2021)"
))

# 添加性别数据
analysis_table <- rbind(analysis_table, data.frame(
  分类 = "性别",
  "X1990年发病率" = NA,
  "X2021年发病率" = NA,
  "AAPC" = NA,
  "X95.CI下限" = NA,
  "X95.CI上限" = NA,
  "数据年数" = ""
))

for(i in 1:nrow(sex_data)) {
  sex_name <- ifelse(sex_data$sex_name[i] == "男", "男性", "女性")
  analysis_table <- rbind(analysis_table, data.frame(
    分类 = sex_name,
    "X1990年发病率" = round(sex_data$rate_1990[i], 2),
    "X2021年发病率" = round(sex_data$rate_2021[i], 2),
    "AAPC" = round(sex_data$aapc[i], 2),
    "X95.CI下限" = round(sex_data$ci_lower[i], 2),
    "X95.CI上限" = round(sex_data$ci_upper[i], 2),
    "数据年数" = "32年 (1990-2021)"
  ))
}

# 添加年龄组数据
analysis_table <- rbind(analysis_table, data.frame(
  分类 = "年龄组(岁)",
  "X1990年发病率" = NA,
  "X2021年发病率" = NA,
  "AAPC" = NA,
  "X95.CI下限" = NA,
  "X95.CI上限" = NA,
  "数据年数" = ""
))

for(i in 1:nrow(age_data)) {
  analysis_table <- rbind(analysis_table, data.frame(
    分类 = age_data$age_name[i],
    "X1990年发病率" = round(age_data$rate_1990[i], 2),
    "X2021年发病率" = round(age_data$rate_2021[i], 2),
    "AAPC" = round(age_data$aapc[i], 2),
    "X95.CI下限" = round(age_data$ci_lower[i], 2),
    "X95.CI上限" = round(age_data$ci_upper[i], 2),
    "数据年数" = "32年 (1990-2021)"
  ))
}

# 添加地区数据（如果有）
if(!is.null(region_data) && nrow(region_data) > 0) {
  analysis_table <- rbind(analysis_table, data.frame(
    分类 = "地区",
    "X1990年发病率" = NA,
    "X2021年发病率" = NA,
    "AAPC" = NA,
    "X95.CI下限" = NA,
    "X95.CI上限" = NA,
    "数据年数" = ""
  ))
  
  for(i in 1:nrow(region_data)) {
    analysis_table <- rbind(analysis_table, data.frame(
      分类 = region_data$location_name[i],
      "X1990年发病率" = round(region_data$rate_1990[i], 2),
      "X2021年发病率" = round(region_data$rate_2021[i], 2),
      "AAPC" = round(region_data$aapc[i], 2),
      "X95.CI下限" = round(region_data$ci_lower[i], 2),
      "X95.CI上限" = round(region_data$ci_upper[i], 2),
      "数据年数" = "32年 (1990-2021)"
    ))
  }
}

# 修正列名
names(analysis_table) <- c("分类", "1990年发病率(每10万人)", "2021年发病率(每10万人)", 
                          "AAPC(%)", "95%CI下限", "95%CI上限", "数据年数")

# 保存最终分析表
write.csv(analysis_table, "脑卒中65岁以上人群发病率最终分析表_1990_2021.csv", 
          row.names = FALSE, fileEncoding = "UTF-8")

cat("最终分析表已保存: 脑卒中65岁以上人群发病率最终分析表_1990_2021.csv\n")

# 显示分析表
cat("\n=== 最终分析表 ===\n")
print(analysis_table)

cat("\n=== 说明 ===\n")
cat("- 所有AAPC计算均基于1990-2021年完整32年时间序列数据\n")
cat("- 使用对数线性回归方法计算年均百分比变化\n")
cat("- 95%置信区间反映了估计的不确定性\n")
cat("- 发病率单位：每10万人年龄标准化发病率\n")

cat("\n=== 分析完成 ===\n")
