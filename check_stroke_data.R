# 检查脑卒中DALYs数据
library(dplyr)

# 检查所有数据文件中的measure_name
files <- list.files('死亡损伤-数据库/204国家', pattern='IHME-GBD_2021_DATA.*\\.csv$', recursive=TRUE, full.names=TRUE)

cat("Found", length(files), "data files\n\n")

for(i in 1:length(files)) {
  file <- files[i]
  cat('File', i, ':', basename(file), '\n')
  
  # 读取前几行检查结构
  data <- read.csv(file, nrows=100)
  cat('Unique measure_name:', paste(unique(data$measure_name), collapse=", "), '\n')
  cat('Unique cause_name:', paste(unique(data$cause_name), collapse=", "), '\n')
  cat('Year range:', min(data$year), '-', max(data$year), '\n')
  cat('Age groups:', paste(unique(data$age_name)[1:5], collapse=", "), '...\n\n')
}
