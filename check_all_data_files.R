# 检查所有数据文件的年份范围
library(readr)
library(dplyr)

# 数据文件路径
data_files <- c(
  '死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-1/IHME-GBD_2021_DATA-336925ca-1.csv',
  '死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-2/IHME-GBD_2021_DATA-336925ca-2.csv',
  '死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-3/IHME-GBD_2021_DATA-336925ca-3.csv',
  '死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-4/IHME-GBD_2021_DATA-336925ca-4.csv',
  '死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-5/IHME-GBD_2021_DATA-336925ca-5.csv',
  '死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-6/IHME-GBD_2021_DATA-336925ca-6.csv'
)

all_stroke_data <- data.frame()

for(i in 1:length(data_files)) {
  cat("正在检查文件", i, ":", basename(data_files[i]), "\n")
  
  if(file.exists(data_files[i])) {
    data <- read_csv(data_files[i], show_col_types = FALSE)
    
    cat("  年份范围:", min(data$year), "-", max(data$year), "\n")
    cat("  脑卒中数据行数:", sum(data$cause_name == "脑卒中"), "\n")
    
    # 筛选脑卒中65岁以上死亡率数据
    stroke_subset <- data %>%
      filter(cause_name == "脑卒中", 
             metric_name == "率",
             sex_name == "合计") %>%
      filter(grepl("65|70|75|80|85|90|95", age_name))
    
    if(nrow(stroke_subset) > 0) {
      all_stroke_data <- rbind(all_stroke_data, stroke_subset)
      cat("  65岁以上脑卒中死亡率数据:", nrow(stroke_subset), "行\n")
    }
  } else {
    cat("  文件不存在\n")
  }
  cat("\n")
}

# 汇总所有数据
if(nrow(all_stroke_data) > 0) {
  cat("=== 汇总结果 ===\n")
  cat("总数据行数:", nrow(all_stroke_data), "\n")
  cat("完整年份范围:", min(all_stroke_data$year), "-", max(all_stroke_data$year), "\n")
  cat("国家数量:", length(unique(all_stroke_data$location_name)), "\n")
  cat("年龄组:", paste(unique(all_stroke_data$age_name), collapse=", "), "\n")
  
  # 检查年份完整性
  years <- sort(unique(all_stroke_data$year))
  cat("可用年份:", paste(years, collapse=", "), "\n")
  
  # 保存完整数据
  write.csv(all_stroke_data, "stroke_mortality_65plus_complete_1990_2021.csv", row.names = FALSE)
  cat("\n完整数据已保存到: stroke_mortality_65plus_complete_1990_2021.csv\n")
} else {
  cat("未找到符合条件的数据\n")
}
