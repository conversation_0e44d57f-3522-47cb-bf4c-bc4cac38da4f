# 精细颜色分级的脑卒中T1DM样式地图
library(ggplot2)
library(dplyr)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(RColorBrewer)
library(gridExtra)
library(grid)

# 读取数据
data <- read.csv("脑卒中65岁以上人群患病率分析表.csv", stringsAsFactors = FALSE, fileEncoding = "UTF-8")

# 提取地区AAPC数据
region_aapc <- data %>%
  filter(分类 %in% c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", "高收入", 
                    "中欧、东欧和中亚", "拉丁美洲和加勒比海", "北非和中东")) %>%
  select(分类, AAPC = colnames(data)[14])

print("脑卒中AAPC数据详情:")
print(region_aapc)

# 创建国家映射（更详细的分类）
country_region_mapping <- data.frame(
  country = c(
    # 高收入国家 (AAPC: -0.73%)
    "United States of America", "Germany", "United Kingdom", "France", "Italy", "Canada", "Spain", 
    "Netherlands", "Belgium", "Switzerland", "Austria", "Sweden", "Norway", "Denmark", "Finland", 
    "Ireland", "Portugal", "Greece", "Israel", "Luxembourg", "Iceland", "Malta", "Cyprus",
    "Australia", "New Zealand", "Japan", "South Korea", "Singapore",
    
    # 中欧、东欧和中亚 (AAPC: +0.73%)
    "Russia", "Poland", "Ukraine", "Romania", "Hungary", "Belarus", "Bulgaria", "Serbia",
    "Croatia", "Bosnia and Herzegovina", "Albania", "North Macedonia", "Moldova", "Montenegro", 
    "Kazakhstan", "Uzbekistan", "Tajikistan", "Kyrgyzstan", "Turkmenistan", "Georgia", 
    "Armenia", "Azerbaijan", "Czech Republic", "Slovakia", "Slovenia", "Estonia", "Latvia", "Lithuania",
    
    # 东南亚、东亚和大洋洲 (AAPC: +0.58%)
    "China", "Indonesia", "Philippines", "Vietnam", "Thailand", "Myanmar", "Malaysia", 
    "Cambodia", "Laos", "Brunei", "Timor-Leste", "Papua New Guinea", "Fiji", "Solomon Islands", 
    "Vanuatu", "Samoa", "Kiribati", "Tonga", "Micronesia", "Palau", "Marshall Islands", 
    "Nauru", "Tuvalu", "North Korea", "Mongolia",
    
    # 南亚 (AAPC: -0.58%)
    "India", "Pakistan", "Bangladesh", "Afghanistan", "Nepal", "Sri Lanka", "Bhutan", "Maldives",
    
    # 拉丁美洲和加勒比海 (AAPC: +0.65%)
    "Brazil", "Mexico", "Colombia", "Argentina", "Peru", "Venezuela", "Chile", "Ecuador",
    "Guatemala", "Cuba", "Bolivia", "Haiti", "Dominican Republic", "Honduras", "Paraguay",
    "Nicaragua", "El Salvador", "Costa Rica", "Panama", "Uruguay", "Jamaica", 
    "Trinidad and Tobago", "Guyana", "Suriname", "Belize", "Barbados",
    
    # 北非和中东 (AAPC: +0.82%)
    "Egypt", "Iran", "Turkey", "Iraq", "Saudi Arabia", "Yemen", "Syria", "Jordan", "Lebanon",
    "United Arab Emirates", "Oman", "Kuwait", "Qatar", "Bahrain", "Algeria", "Morocco", 
    "Sudan", "Tunisia", "Libya",
    
    # 撒哈拉以南非洲 (AAPC: +0.69%)
    "Nigeria", "Ethiopia", "South Africa", "Kenya", "Uganda", "Tanzania", "Ghana", "Mozambique", 
    "Madagascar", "Cameroon", "Angola", "Niger", "Burkina Faso", "Mali", "Malawi", "Zambia", 
    "Somalia", "Senegal", "Chad", "Zimbabwe", "Guinea", "Rwanda", "Benin", "Burundi",
    "South Sudan", "Togo", "Sierra Leone", "Liberia", "Central African Republic",
    "Mauritania", "Eritrea", "Gambia", "Botswana", "Namibia", "Gabon", "Lesotho", 
    "Guinea-Bissau", "Equatorial Guinea", "Mauritius", "Eswatini", "Djibouti", 
    "Comoros", "Cape Verde", "São Tomé and Príncipe"
  ),
  region = c(
    rep("高收入", 28),
    rep("中欧、东欧和中亚", 28),
    rep("东南亚、东亚和大洋洲", 25),
    rep("南亚", 8),
    rep("拉丁美洲和加勒比海", 26),
    rep("北非和中东", 19),
    rep("撒哈拉以南非洲", 44)
  )
)

# 合并数据
country_data <- country_region_mapping %>%
  left_join(region_aapc, by = c("region" = "分类"))

# 获取世界地图数据
world <- ne_countries(scale = "medium", returnclass = "sf")

# 合并地图数据并创建精细的颜色分组
world_data <- world %>%
  left_join(country_data, by = c("name" = "country")) %>%
  mutate(
    # 创建更精细的颜色分组（基于实际AAPC值的精确范围）
    aapc_group = case_when(
      is.na(AAPC) ~ "No data",
      AAPC <= -0.70 ~ "≤ -0.70",           # 高收入国家 (-0.73)
      AAPC > -0.70 & AAPC <= -0.55 ~ "-0.70 to -0.55",  # 南亚 (-0.58)
      AAPC > -0.55 & AAPC <= 0.55 ~ "-0.55 to 0.55",    # 接近零的变化
      AAPC > 0.55 & AAPC <= 0.60 ~ "0.55 to 0.60",      # 东南亚、东亚和大洋洲 (0.58)
      AAPC > 0.60 & AAPC <= 0.67 ~ "0.60 to 0.67",      # 拉丁美洲和加勒比海 (0.65)
      AAPC > 0.67 & AAPC <= 0.71 ~ "0.67 to 0.71",      # 撒哈拉以南非洲 (0.69)
      AAPC > 0.71 & AAPC <= 0.75 ~ "0.71 to 0.75",      # 中欧、东欧和中亚 (0.73)
      AAPC > 0.75 ~ "> 0.75"                             # 北非和中东 (0.82)
    ),
    aapc_group = factor(aapc_group, levels = c("≤ -0.70", "-0.70 to -0.55", "-0.55 to 0.55", 
                                               "0.55 to 0.60", "0.60 to 0.67", "0.67 to 0.71",
                                               "0.71 to 0.75", "> 0.75", "No data"))
  )

# 定义精细的颜色方案（从深蓝到深红的渐变）
colors <- c(
  "#08519c",    # 深蓝色：≤ -0.70 (高收入国家)
  "#3182bd",    # 蓝色：-0.70 to -0.55 (南亚)
  "#c6dbef",    # 浅蓝色：-0.55 to 0.55 (接近零)
  "#fee391",    # 浅黄色：0.55 to 0.60 (东南亚、东亚和大洋洲)
  "#fec44f",    # 黄色：0.60 to 0.67 (拉丁美洲和加勒比海)
  "#fe9929",    # 橙色：0.67 to 0.71 (撒哈拉以南非洲)
  "#ec7014",    # 深橙色：0.71 to 0.75 (中欧、东欧和中亚)
  "#cc4c02",    # 深红色：> 0.75 (北非和中东)
  "#969696"     # 灰色：无数据
)
names(colors) <- levels(world_data$aapc_group)

# 创建主地图
create_detailed_main_map <- function() {
  ggplot(world_data) +
    geom_sf(aes(fill = aapc_group), color = "white", size = 0.1) +
    scale_fill_manual(values = colors, name = "AAPC (%)", drop = FALSE,
                      labels = c("≤ -0.70% (Strong decrease)", 
                                "-0.70 to -0.55% (Moderate decrease)",
                                "-0.55 to 0.55% (Stable)", 
                                "0.55 to 0.60% (Slight increase)",
                                "0.60 to 0.67% (Moderate increase)",
                                "0.67 to 0.71% (Notable increase)",
                                "0.71 to 0.75% (Strong increase)",
                                "> 0.75% (Very strong increase)",
                                "No data")) +
    theme_void() +
    theme(
      legend.position = "bottom",
      legend.title = element_text(size = 12, face = "bold"),
      legend.text = element_text(size = 9),
      plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
      plot.subtitle = element_text(size = 14, hjust = 0.5),
      legend.key.size = unit(0.6, "cm"),
      legend.key.width = unit(0.8, "cm"),
      panel.background = element_rect(fill = "lightblue", color = NA),
      plot.background = element_rect(fill = "white", color = NA),
      plot.margin = margin(10, 10, 10, 10),
      # 调整图例布局
      legend.box = "horizontal",
      legend.direction = "horizontal"
    ) +
    guides(fill = guide_legend(nrow = 3, byrow = TRUE)) +
    labs(
      title = "Average Annual Percentage Change in Stroke Prevalence",
      subtitle = "Among people aged ≥65 years, 1990-2019 (Detailed Color Classification)"
    ) +
    coord_sf(crs = "+proj=robin")
}

# 创建区域放大图
create_detailed_regional_map <- function(xlim, ylim, title) {
  ggplot(world_data) +
    geom_sf(aes(fill = aapc_group), color = "white", size = 0.2) +
    scale_fill_manual(values = colors, guide = "none") +
    coord_sf(xlim = xlim, ylim = ylim, expand = FALSE) +
    theme_void() +
    theme(
      plot.title = element_text(size = 9, face = "bold", hjust = 0.5),
      panel.border = element_rect(color = "black", fill = NA, linewidth = 1),
      plot.background = element_rect(fill = "white", color = NA),
      plot.margin = margin(2, 2, 2, 2)
    ) +
    labs(title = title)
}

# 创建主地图
main_map <- create_detailed_main_map()

# 创建区域放大图
caribbean <- create_detailed_regional_map(c(-90, -60), c(10, 25), "Caribbean and\nCentral America")
persian_gulf <- create_detailed_regional_map(c(45, 60), c(24, 32), "Persian Gulf")
balkans <- create_detailed_regional_map(c(15, 30), c(40, 48), "Balkan Peninsula")
southeast_asia <- create_detailed_regional_map(c(95, 140), c(-10, 25), "South East Asia")
west_africa <- create_detailed_regional_map(c(-20, 10), c(4, 20), "West Africa")
northern_europe <- create_detailed_regional_map(c(5, 30), c(55, 70), "Northern Europe")

# 保存主地图
ggsave("stroke_detailed_color_main_map.png", main_map, 
       width = 18, height = 12, dpi = 300, bg = "white")

# 创建区域放大图组合
regional_combined <- grid.arrange(caribbean, persian_gulf, balkans, 
                                 southeast_asia, west_africa, northern_europe,
                                 ncol = 6, 
                                 top = textGrob("Regional Detail Maps", 
                                               gp = gpar(fontsize = 14, fontface = "bold")))

# 保存区域放大图
ggsave("stroke_detailed_color_regional_maps.png", regional_combined, 
       width = 18, height = 4, dpi = 300, bg = "white")

# 显示主图
print(main_map)

# 打印详细统计信息
cat("\n🌍 脑卒中65岁以上人群AAPC详细数据摘要 (1990-2019):\n")
cat(paste(rep("=", 60), collapse = ""), "\n")
for(i in 1:nrow(region_aapc)) {
  aapc_val <- region_aapc$AAPC[i]
  trend <- ifelse(aapc_val > 0, "↗️ 上升", "↘️ 下降")
  intensity <- case_when(
    abs(aapc_val) >= 0.7 ~ "强烈",
    abs(aapc_val) >= 0.6 ~ "显著", 
    abs(aapc_val) >= 0.5 ~ "中等",
    TRUE ~ "轻微"
  )
  cat(sprintf("📍 %-20s: %+.2f%% (%s %s)\n", 
              region_aapc$分类[i], aapc_val, intensity, trend))
}

cat("\n📊 精细颜色分组统计:\n")
print(table(world_data$aapc_group, useNA = "ifany"))

cat("\n🎨 精细颜色图例说明:\n")
cat("🔵 深蓝色 (≤ -0.70%): 患病率强烈下降 - 高收入国家\n")
cat("🔷 蓝色 (-0.70 to -0.55%): 患病率中等下降 - 南亚\n")
cat("💙 浅蓝色 (-0.55 to 0.55%): 患病率基本稳定\n")
cat("💛 浅黄色 (0.55 to 0.60%): 患病率轻微上升 - 东南亚、东亚和大洋洲\n")
cat("🟡 黄色 (0.60 to 0.67%): 患病率中等上升 - 拉丁美洲和加勒比海\n")
cat("🟠 橙色 (0.67 to 0.71%): 患病率显著上升 - 撒哈拉以南非洲\n")
cat("🔶 深橙色 (0.71 to 0.75%): 患病率强烈上升 - 中欧、东欧和中亚\n")
cat("🔴 深红色 (> 0.75%): 患病率极强上升 - 北非和中东\n")
cat("⬜ 灰色: 无数据\n")
