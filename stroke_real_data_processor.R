# 脑卒中真实GBD数据处理器
# 处理风险因素数据库中的真实数据

library(dplyr)
library(readr)
library(purrr)
library(stringr)

# 1. 数据读取和清理函数
read_gbd_data <- function(file_path, encoding = "UTF-8") {
  tryCatch({
    # 尝试不同的编码方式读取
    data <- read_csv(file_path, locale = locale(encoding = encoding))
    return(data)
  }, error = function(e) {
    tryCatch({
      # 如果UTF-8失败，尝试GBK
      data <- read_csv(file_path, locale = locale(encoding = "GBK"))
      return(data)
    }, error = function(e2) {
      # 如果都失败，尝试系统默认编码
      data <- read_csv(file_path)
      return(data)
    })
  })
}

# 2. 批量读取SDI分层数据
read_sdi_risk_data <- function() {
  sdi_files <- list.files("风险因素-数据库/gbd-sd", recursive = TRUE, pattern = "\\.csv$", full.names = TRUE)
  
  all_data <- list()
  
  for (file in sdi_files) {
    cat("正在读取:", basename(file), "\n")
    
    tryCatch({
      data <- read_gbd_data(file)
      
      # 检查数据结构
      if (ncol(data) > 10) {  # 确保有足够的列
        all_data[[basename(file)]] <- data
        cat("✓ 成功读取:", basename(file), "- 行数:", nrow(data), "\n")
      } else {
        cat("⚠ 跳过文件（列数不足）:", basename(file), "\n")
      }
    }, error = function(e) {
      cat("✗ 读取失败:", basename(file), "- 错误:", e$message, "\n")
    })
  }
  
  return(all_data)
}

# 3. 数据标准化和清理
standardize_gbd_data <- function(raw_data_list) {
  standardized_data <- list()
  
  for (file_name in names(raw_data_list)) {
    data <- raw_data_list[[file_name]]
    
    # 检查必要的列是否存在
    required_cols <- c("measure_name", "location_name", "sex_name", "age_name", 
                       "cause_name", "rei_name", "year", "val", "upper", "lower")
    
    # 获取实际的列名（可能有编码问题）
    actual_cols <- colnames(data)
    
    # 尝试映射列名
    col_mapping <- list()
    for (req_col in required_cols) {
      # 寻找最相似的列名
      similar_col <- actual_cols[which.max(sapply(actual_cols, function(x) {
        sum(utf8ToInt(req_col) %in% utf8ToInt(x)) / max(length(utf8ToInt(req_col)), length(utf8ToInt(x)))
      }))]
      
      if (length(similar_col) > 0) {
        col_mapping[[req_col]] <- similar_col
      }
    }
    
    # 如果找到了足够的列映射，进行数据处理
    if (length(col_mapping) >= 6) {
      processed_data <- data %>%
        select(any_of(unlist(col_mapping))) %>%
        # 重命名列
        rename_with(~ names(col_mapping)[match(.x, unlist(col_mapping))], .cols = everything())
      
      standardized_data[[file_name]] <- processed_data
      cat("✓ 标准化完成:", file_name, "\n")
    } else {
      cat("⚠ 跳过标准化（列映射不足）:", file_name, "\n")
    }
  }
  
  return(standardized_data)
}

# 4. 筛选脑卒中相关数据
filter_stroke_data <- function(standardized_data) {
  stroke_keywords <- c("stroke", "脑卒中", "中风", "Stroke")
  
  stroke_data <- list()
  
  for (file_name in names(standardized_data)) {
    data <- standardized_data[[file_name]]
    
    # 筛选脑卒中相关数据
    if ("cause_name" %in% colnames(data)) {
      filtered_data <- data %>%
        filter(
          str_detect(tolower(cause_name), paste(tolower(stroke_keywords), collapse = "|")) |
          str_detect(cause_name, paste(stroke_keywords, collapse = "|"))
        )
      
      if (nrow(filtered_data) > 0) {
        stroke_data[[file_name]] <- filtered_data
        cat("✓ 筛选到脑卒中数据:", file_name, "- 行数:", nrow(filtered_data), "\n")
      }
    }
  }
  
  return(stroke_data)
}

# 5. 提取65岁以上人群数据
filter_elderly_data <- function(stroke_data) {
  elderly_keywords <- c("65", "70", "75", "80", "85", "90", "95+", "65+", "65-69", "70-74", "75-79", "80-84", "85+")
  
  elderly_data <- list()
  
  for (file_name in names(stroke_data)) {
    data <- stroke_data[[file_name]]
    
    if ("age_name" %in% colnames(data)) {
      filtered_data <- data %>%
        filter(
          str_detect(age_name, paste(elderly_keywords, collapse = "|")) |
          str_detect(age_name, "65") |
          str_detect(age_name, "老年") |
          str_detect(age_name, "elderly")
        )
      
      if (nrow(filtered_data) > 0) {
        elderly_data[[file_name]] <- filtered_data
        cat("✓ 筛选到65岁以上数据:", file_name, "- 行数:", nrow(filtered_data), "\n")
      }
    }
  }
  
  return(elderly_data)
}

# 6. 提取DALYs数据
filter_dalys_data <- function(elderly_data) {
  dalys_keywords <- c("DALYs", "DALY", "伤残调整生命年", "disability-adjusted life years")
  
  dalys_data <- list()
  
  for (file_name in names(elderly_data)) {
    data <- elderly_data[[file_name]]
    
    if ("measure_name" %in% colnames(data)) {
      filtered_data <- data %>%
        filter(
          str_detect(tolower(measure_name), paste(tolower(dalys_keywords), collapse = "|")) |
          str_detect(measure_name, paste(dalys_keywords, collapse = "|"))
        )
      
      if (nrow(filtered_data) > 0) {
        dalys_data[[file_name]] <- filtered_data
        cat("✓ 筛选到DALYs数据:", file_name, "- 行数:", nrow(filtered_data), "\n")
      }
    }
  }
  
  return(dalys_data)
}

# 7. 合并所有数据并创建最终数据集
create_final_dataset <- function(dalys_data) {
  if (length(dalys_data) == 0) {
    cat("⚠ 没有找到符合条件的DALYs数据\n")
    return(NULL)
  }
  
  # 合并所有数据
  combined_data <- bind_rows(dalys_data, .id = "source_file")
  
  # 数据清理和标准化
  final_data <- combined_data %>%
    filter(
      !is.na(val), !is.na(upper), !is.na(lower),
      year %in% c(1990, 2019)
    ) %>%
    mutate(
      # 标准化位置名称为SDI分层
      sdi_level = case_when(
        str_detect(tolower(location_name), "global") ~ "Global",
        str_detect(tolower(location_name), "high sdi") ~ "High SDI",
        str_detect(tolower(location_name), "high-middle sdi") ~ "High-middle SDI", 
        str_detect(tolower(location_name), "middle sdi") ~ "Middle SDI",
        str_detect(tolower(location_name), "low-middle sdi") ~ "Low-middle SDI",
        str_detect(tolower(location_name), "low sdi") ~ "Low SDI",
        TRUE ~ location_name
      ),
      # 清理风险因素名称
      risk_factor = rei_name,
      # 确保数值类型正确
      dalys_value = as.numeric(val),
      dalys_upper = as.numeric(upper),
      dalys_lower = as.numeric(lower)
    ) %>%
    select(source_file, sdi_level, risk_factor, year, sex_name, age_name,
           dalys_value, dalys_upper, dalys_lower) %>%
    filter(!is.na(dalys_value))
  
  return(final_data)
}

# 8. 主处理函数
process_real_gbd_data <- function() {
  cat("开始处理真实GBD数据...\n")
  
  # 步骤1：读取原始数据
  cat("\n=== 步骤1：读取原始数据 ===\n")
  raw_data <- read_sdi_risk_data()
  
  if (length(raw_data) == 0) {
    cat("⚠ 没有成功读取任何数据文件\n")
    return(NULL)
  }
  
  # 步骤2：标准化数据
  cat("\n=== 步骤2：标准化数据 ===\n")
  standardized_data <- standardize_gbd_data(raw_data)
  
  # 步骤3：筛选脑卒中数据
  cat("\n=== 步骤3：筛选脑卒中数据 ===\n")
  stroke_data <- filter_stroke_data(standardized_data)
  
  # 步骤4：筛选65岁以上人群
  cat("\n=== 步骤4：筛选65岁以上人群 ===\n")
  elderly_data <- filter_elderly_data(stroke_data)
  
  # 步骤5：筛选DALYs数据
  cat("\n=== 步骤5：筛选DALYs数据 ===\n")
  dalys_data <- filter_dalys_data(elderly_data)
  
  # 步骤6：创建最终数据集
  cat("\n=== 步骤6：创建最终数据集 ===\n")
  final_dataset <- create_final_dataset(dalys_data)
  
  if (!is.null(final_dataset) && nrow(final_dataset) > 0) {
    # 保存处理后的数据
    write_csv(final_dataset, "stroke_real_gbd_dalys_data.csv")
    cat("✓ 最终数据集已保存: stroke_real_gbd_dalys_data.csv\n")
    cat("数据集包含", nrow(final_dataset), "行数据\n")
    
    # 显示数据概览
    cat("\n=== 数据概览 ===\n")
    cat("SDI分层:", paste(unique(final_dataset$sdi_level), collapse = ", "), "\n")
    cat("风险因素数量:", length(unique(final_dataset$risk_factor)), "\n")
    cat("年份:", paste(unique(final_dataset$year), collapse = ", "), "\n")
    
    return(final_dataset)
  } else {
    cat("⚠ 未能创建有效的最终数据集\n")
    return(NULL)
  }
}

# 执行数据处理
if (!interactive()) {
  processed_data <- process_real_gbd_data()
}
