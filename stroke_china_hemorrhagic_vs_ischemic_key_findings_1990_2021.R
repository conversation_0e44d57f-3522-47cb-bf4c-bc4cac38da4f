# 中国1990-2021年脑卒中出血与缺血关系比较 - 关键发现图
# 简化版可视化，突出主要发现

# 加载必要的包
library(ggplot2)
library(dplyr)
library(tidyr)
library(gridExtra)
library(scales)
library(grid)

# 设置中文字体
if (.Platform$OS.type == "windows") {
  windowsFonts(SimSun = windowsFont("SimSun"))
  par(family = "SimSun")
}

# 读取数据
ischemic_data <- read.csv("stroke_china_ischemic_stroke_prevalence_35plus_1990_2021.csv", 
                         stringsAsFactors = FALSE, fileEncoding = "UTF-8")
hemorrhagic_data <- read.csv("stroke_china_hemorrhagic_stroke_prevalence_35plus_1990_2021.csv", 
                            stringsAsFactors = FALSE, fileEncoding = "UTF-8")

# 数据处理函数
clean_rate_data <- function(rate_str) {
  rate_num <- as.numeric(gsub("\\s*\\(.*\\)", "", rate_str))
  return(rate_num)
}

clean_aapc_data <- function(aapc_str) {
  aapc_num <- as.numeric(gsub("\\s*\\(.*\\)", "", aapc_str))
  return(aapc_num)
}

# 处理数据
ischemic_clean <- ischemic_data %>%
  filter(!is.na(Category) & Category != "" & 
         !grepl("Table|1990年|2021年", Category)) %>%
  mutate(
    Rate_1990 = clean_rate_data(Prevalence_1990_rate),
    Rate_2021 = clean_rate_data(Prevalence_2021_rate),
    AAPC = clean_aapc_data(AAPC),
    Type = "缺血性脑卒中"
  ) %>%
  select(Category, Rate_1990, Rate_2021, AAPC, Type)

hemorrhagic_clean <- hemorrhagic_data %>%
  filter(!is.na(Category) & Category != "" & 
         !grepl("Table|1990年|2021年", Category)) %>%
  mutate(
    Rate_1990 = clean_rate_data(Prevalence_1990_rate),
    Rate_2021 = clean_rate_data(Prevalence_2021_rate),
    AAPC = clean_aapc_data(AAPC),
    Type = "出血性脑卒中"
  ) %>%
  select(Category, Rate_1990, Rate_2021, AAPC, Type)

# 合并数据
combined_data <- rbind(ischemic_clean, hemorrhagic_clean)

# 筛选总体数据
overall_data <- combined_data %>% filter(Category == "中国总体")

# 创建时间序列数据
time_series_data <- data.frame(
  Year = rep(c(1990, 2021), 2),
  Rate = c(overall_data$Rate_1990[overall_data$Type == "缺血性脑卒中"],
           overall_data$Rate_2021[overall_data$Type == "缺血性脑卒中"],
           overall_data$Rate_1990[overall_data$Type == "出血性脑卒中"],
           overall_data$Rate_2021[overall_data$Type == "出血性脑卒中"]),
  Type = rep(c("缺血性脑卒中", "出血性脑卒中"), each = 2)
)

# 1. 时间趋势对比图
p1 <- ggplot(time_series_data, aes(x = Year, y = Rate, color = Type, group = Type)) +
  geom_line(linewidth = 2) +
  geom_point(size = 4) +
  geom_text(aes(label = paste0(round(Rate, 1), "/10万")), 
            vjust = -1, size = 4, show.legend = FALSE) +
  labs(title = "中国35岁以上人群脑卒中患病率变化趋势 (1990-2021)",
       x = "年份", y = "年龄标准化患病率（每10万人）",
       color = "脑卒中类型") +
  scale_color_manual(values = c("缺血性脑卒中" = "#2E86AB", "出血性脑卒中" = "#A23B72")) +
  scale_x_continuous(breaks = c(1990, 2021)) +
  theme_minimal() +
  theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
        legend.position = "bottom",
        legend.title = element_text(size = 12),
        legend.text = element_text(size = 11))

# 2. AAPC对比图
p2 <- ggplot(overall_data, aes(x = Type, y = AAPC, fill = Type)) +
  geom_col(alpha = 0.8, width = 0.6) +
  geom_text(aes(label = paste0(round(AAPC, 2), "%/年")), 
            vjust = -0.5, size = 5, fontface = "bold") +
  labs(title = "平均年度百分比变化对比 (1990-2021)",
       x = "脑卒中类型", y = "AAPC（%/年）") +
  scale_fill_manual(values = c("缺血性脑卒中" = "#2E86AB", "出血性脑卒中" = "#A23B72")) +
  theme_minimal() +
  theme(legend.position = "none",
        plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
        axis.text.x = element_text(size = 11),
        axis.text.y = element_text(size = 11))

# 3. 比值变化图
ratio_data <- data.frame(
  Year = c("1990", "2021"),
  Ratio = c(
    overall_data$Rate_1990[overall_data$Type == "缺血性脑卒中"] / 
    overall_data$Rate_1990[overall_data$Type == "出血性脑卒中"],
    overall_data$Rate_2021[overall_data$Type == "缺血性脑卒中"] / 
    overall_data$Rate_2021[overall_data$Type == "出血性脑卒中"]
  )
)

p3 <- ggplot(ratio_data, aes(x = Year, y = Ratio, group = 1)) +
  geom_line(color = "#F18F01", linewidth = 3) +
  geom_point(color = "#F18F01", size = 6) +
  geom_text(aes(label = paste0("比值: ", round(Ratio, 1), ":1")), 
            vjust = -1.5, size = 5, fontface = "bold") +
  labs(title = "缺血性与出血性脑卒中患病率比值",
       x = "年份", y = "缺血性/出血性患病率比值") +
  ylim(0, max(ratio_data$Ratio) * 1.3) +
  theme_minimal() +
  theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
        axis.text = element_text(size = 11))

# 4. 绝对下降量对比
decline_data <- data.frame(
  Type = c("缺血性脑卒中", "出血性脑卒中"),
  Decline = c(
    overall_data$Rate_1990[overall_data$Type == "缺血性脑卒中"] - 
    overall_data$Rate_2021[overall_data$Type == "缺血性脑卒中"],
    overall_data$Rate_1990[overall_data$Type == "出血性脑卒中"] - 
    overall_data$Rate_2021[overall_data$Type == "出血性脑卒中"]
  )
)

p4 <- ggplot(decline_data, aes(x = Type, y = Decline, fill = Type)) +
  geom_col(alpha = 0.8, width = 0.6) +
  geom_text(aes(label = paste0(round(Decline, 1), "/10万")), 
            vjust = -0.5, size = 5, fontface = "bold") +
  labs(title = "绝对下降量对比 (1990-2021)",
       x = "脑卒中类型", y = "患病率下降量（每10万人）") +
  scale_fill_manual(values = c("缺血性脑卒中" = "#2E86AB", "出血性脑卒中" = "#A23B72")) +
  theme_minimal() +
  theme(legend.position = "none",
        plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
        axis.text.x = element_text(size = 11),
        axis.text.y = element_text(size = 11))

# 创建综合布局
png("stroke_china_hemorrhagic_vs_ischemic_key_findings_1990_2021.png", 
    width = 14, height = 10, units = "in", res = 300)

grid.arrange(
  arrangeGrob(p1, ncol = 1),
  arrangeGrob(p2, p3, p4, ncol = 3),
  ncol = 1,
  heights = c(2, 1.5),
  top = textGrob("中国1990-2021年脑卒中出血与缺血关系比较 - 关键发现", 
                 gp = gpar(fontsize = 18, fontface = "bold"))
)

dev.off()

# 输出关键数据摘要
cat("=== 中国脑卒中出血与缺血关系比较 - 关键发现 ===\n\n")

cat("1. 患病率水平 (每10万人):\n")
cat("   1990年 - 缺血性:", round(overall_data$Rate_1990[overall_data$Type == "缺血性脑卒中"], 1), 
    " | 出血性:", round(overall_data$Rate_1990[overall_data$Type == "出血性脑卒中"], 1), "\n")
cat("   2021年 - 缺血性:", round(overall_data$Rate_2021[overall_data$Type == "缺血性脑卒中"], 1), 
    " | 出血性:", round(overall_data$Rate_2021[overall_data$Type == "出血性脑卒中"], 1), "\n\n")

cat("2. 变化趋势 (AAPC):\n")
cat("   缺血性脑卒中:", round(overall_data$AAPC[overall_data$Type == "缺血性脑卒中"], 2), "%/年\n")
cat("   出血性脑卒中:", round(overall_data$AAPC[overall_data$Type == "出血性脑卒中"], 2), "%/年\n\n")

cat("3. 比值稳定性:\n")
cat("   1990年比值:", round(ratio_data$Ratio[1], 1), ":1\n")
cat("   2021年比值:", round(ratio_data$Ratio[2], 1), ":1\n\n")

cat("4. 绝对下降量 (每10万人):\n")
cat("   缺血性脑卒中:", round(decline_data$Decline[1], 1), "\n")
cat("   出血性脑卒中:", round(decline_data$Decline[2], 1), "\n\n")

cat("关键发现图表已保存为: stroke_china_hemorrhagic_vs_ischemic_key_findings_1990_2021.png\n")
