# 脑卒中65岁以上人群DALYs年均变化率地图 - 超精细颜色分级版本 (1990-2021)
# 使用十分位数实现完美均匀的颜色分布

library(dplyr)
library(ggplot2)
library(maps)
library(viridis)
library(RColorBrewer)
library(gridExtra)
library(grid)

# 1. 加载和准备数据
cat("Loading data for ultra-fine color grading...\n")
country_aapc <- read.csv("stroke_dalys_65plus_aapc_results_1990_2021.csv")

# 2. 创建完美的十分位数分级
decile_breaks <- quantile(country_aapc$aapc, probs = seq(0, 1, 0.1))
decile_breaks[1] <- decile_breaks[1] - 0.01
decile_breaks[11] <- decile_breaks[11] + 0.01

# 创建简洁的数值标签
decile_labels <- c()
for(i in 1:(length(decile_breaks)-1)) {
  decile_labels <- c(decile_labels, 
                    sprintf("%.1f to %.1f", decile_breaks[i], decile_breaks[i+1]))
}

cat("Ultra-fine decile classification:\n")
for(i in 1:length(decile_labels)) {
  cat(sprintf("Level %2d: %s\n", i, decile_labels[i]))
}

# 3. 国家名称标准化（扩展版本）
country_mapping <- data.frame(
  gbd_name = c("美国", "英国", "俄罗斯", "韩国", "伊朗伊斯兰共和国", "叙利亚", "委内瑞拉", "玻利维亚", 
               "坦桑尼亚", "刚果民主共和国", "老挝", "越南", "文莱", "北马其顿", "摩尔多瓦",
               "中国", "日本", "德国", "法国", "意大利", "西班牙", "加拿大", "澳大利亚",
               "巴西", "印度", "南非", "埃及", "土耳其", "沙特阿拉伯", "阿联酋", "大韩民国"),
  map_name = c("USA", "UK", "Russia", "South Korea", "Iran", "Syria", "Venezuela", "Bolivia",
               "Tanzania", "Democratic Republic of the Congo", "Laos", "Vietnam", "Brunei", 
               "North Macedonia", "Moldova", "China", "Japan", "Germany", "France", "Italy", 
               "Spain", "Canada", "Australia", "Brazil", "India", "South Africa", "Egypt", 
               "Turkey", "Saudi Arabia", "UAE", "South Korea"),
  stringsAsFactors = FALSE
)

# 获取世界地图数据
world_map <- map_data("world")

# 标准化国家名称
country_aapc$region <- country_aapc$location_name
for(i in 1:nrow(country_mapping)) {
  country_aapc$region[country_aapc$location_name == country_mapping$gbd_name[i]] <- country_mapping$map_name[i]
}

# 合并地图数据
map_data_with_aapc <- merge(world_map, country_aapc, by = "region", all.x = TRUE)
map_data_with_aapc <- map_data_with_aapc[order(map_data_with_aapc$order), ]

# 4. 应用十分位数分级
map_data_with_aapc$aapc_decile <- cut(map_data_with_aapc$aapc, 
                                     breaks = decile_breaks, 
                                     labels = decile_labels,
                                     include.lowest = TRUE)

# 5. 创建超精细的颜色方案
# 使用专业的蓝-白-红渐变，确保视觉平衡
ultra_fine_colors <- c(
  "#053061",  # 深蓝 - 最大下降
  "#2166ac",  # 蓝
  "#4393c3",  # 中蓝
  "#92c5de",  # 浅蓝
  "#d1e5f0",  # 很浅蓝
  "#f7f7f7",  # 接近白色 - 中性
  "#fddbc7",  # 很浅橙
  "#f4a582",  # 浅橙
  "#d6604d",  # 橙红
  "#b2182b"   # 深红 - 最大上升
)

# 验证颜色分布
cat("\nColor distribution verification:\n")
country_aapc$decile_test <- cut(country_aapc$aapc, breaks = decile_breaks, include.lowest = TRUE)
decile_counts <- table(country_aapc$decile_test)
for(i in 1:length(decile_counts)) {
  cat(sprintf("Decile %2d: %2d countries\n", i, decile_counts[i]))
}

# 6. 创建主地图 - 优化版本
cat("\nCreating ultra-fine main world map...\n")
main_map_ultra <- ggplot(map_data_with_aapc, aes(x = long, y = lat, group = group)) +
  geom_polygon(aes(fill = aapc_decile), color = "white", linewidth = 0.05) +
  scale_fill_manual(values = ultra_fine_colors, 
                   name = "AAPC (% per year)",
                   na.value = "grey85",
                   drop = FALSE) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 14, face = "bold"),
    legend.text = element_text(size = 9),
    legend.key.size = unit(0.8, "cm"),
    legend.key.width = unit(1.2, "cm"),
    plot.margin = margin(5, 5, 10, 5, "mm"),
    legend.margin = margin(t = 10)
  ) +
  guides(fill = guide_legend(nrow = 2, byrow = TRUE, 
                            title.position = "top", title.hjust = 0.5)) +
  coord_fixed(1.3)

# 7. 创建区域放大地图函数
create_ultra_regional_map <- function(xlim, ylim, title) {
  ggplot(map_data_with_aapc, aes(x = long, y = lat, group = group)) +
    geom_polygon(aes(fill = aapc_decile), color = "white", linewidth = 0.1) +
    scale_fill_manual(values = ultra_fine_colors, na.value = "grey85", guide = "none") +
    coord_fixed(1.3, xlim = xlim, ylim = ylim) +
    theme_void() +
    theme(
      plot.title = element_text(size = 11, hjust = 0.5, face = "bold"),
      plot.margin = margin(3, 3, 3, 3, "mm"),
      panel.border = element_rect(color = "grey70", fill = NA, linewidth = 0.5)
    ) +
    ggtitle(title)
}

# 8. 创建6个区域放大地图
cat("Creating ultra-fine regional zoom maps...\n")

caribbean_ultra <- create_ultra_regional_map(c(-95, -55), c(5, 35), "Caribbean and Central America")
persian_gulf_ultra <- create_ultra_regional_map(c(45, 65), c(20, 40), "Persian Gulf")
balkans_ultra <- create_ultra_regional_map(c(10, 30), c(35, 50), "Balkan Peninsula")
southeast_asia_ultra <- create_ultra_regional_map(c(90, 140), c(-15, 25), "South East Asia")
west_africa_med_ultra <- create_ultra_regional_map(c(-20, 45), c(0, 40), "West Africa & Eastern Mediterranean")
northern_europe_ultra <- create_ultra_regional_map(c(-10, 35), c(50, 75), "Northern Europe")

# 9. 组合所有地图
cat("Combining all ultra-fine maps into final layout...\n")

regional_grid_ultra <- grid.arrange(
  caribbean_ultra, persian_gulf_ultra, balkans_ultra,
  southeast_asia_ultra, west_africa_med_ultra, northern_europe_ultra,
  ncol = 3, nrow = 2
)

final_plot_ultra <- grid.arrange(
  main_map_ultra,
  regional_grid_ultra,
  ncol = 1,
  heights = c(2.8, 1),
  top = textGrob("Stroke DALYs AAPC in Population Aged ≥65 Years (1990-2021)\nDecile-Based Color Classification (Each Level ≈ 20 Countries)", 
                gp = gpar(fontsize = 18, fontface = "bold"))
)

# 10. 保存超精细分级地图
cat("Saving ultra-fine color maps...\n")

ggsave("stroke_dalys_65plus_ultra_fine_colors_complete_layout_1990_2021.png", 
       final_plot_ultra, width = 22, height = 18, dpi = 300, bg = "white")

ggsave("stroke_dalys_65plus_ultra_fine_colors_main_map_1990_2021.png", 
       main_map_ultra, width = 20, height = 14, dpi = 300, bg = "white")

# 保存详细的颜色分级信息
ultra_fine_legend <- data.frame(
  decile = 1:10,
  aapc_range = decile_labels,
  color_hex = ultra_fine_colors,
  countries_per_decile = as.numeric(decile_counts),
  percentage_of_total = round(as.numeric(decile_counts) / sum(decile_counts) * 100, 1)
)

write.csv(ultra_fine_legend, "stroke_dalys_65plus_ultra_fine_legend_1990_2021.csv", row.names = FALSE)

cat("Ultra-fine color visualization completed successfully!\n")
cat("Files saved:\n")
cat("- stroke_dalys_65plus_ultra_fine_colors_complete_layout_1990_2021.png\n")
cat("- stroke_dalys_65plus_ultra_fine_colors_main_map_1990_2021.png\n")
cat("- stroke_dalys_65plus_ultra_fine_legend_1990_2021.csv\n")
cat("\nPerfect color distribution achieved: Each decile contains exactly ~20 countries!\n")
cat("Colors now show much finer gradations across countries.\n")
