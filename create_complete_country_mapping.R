# 创建完整的中英文国家名称映射表
library(dplyr)

cat("=== 创建完整的中英文国家名称映射表 ===\n")

# 读取AAPC数据
country_aapc <- read.csv("stroke_dalys_35plus_aapc_results_1990_2021.csv", 
                        stringsAsFactors = FALSE, 
                        fileEncoding = "UTF-8")

# 显示所有中文国家名称
cat("所有中文国家名称:\n")
chinese_names <- unique(country_aapc$location_name)
for(i in 1:length(chinese_names)) {
  cat(sprintf("%3d: %s\n", i, chinese_names[i]))
}

# 创建更完整的映射表
complete_mapping <- data.frame(
  chinese_name = c(
    "中国", "美国", "印度", "俄罗斯", "日本", "德国", "英国", "法国", "意大利", "巴西",
    "加拿大", "韩国", "西班牙", "澳大利亚", "墨西哥", "印度尼西亚", "荷兰", "沙特阿拉伯",
    "土耳其", "瑞士", "比利时", "爱尔兰", "以色列", "奥地利", "尼日利亚", "阿根廷",
    "南非", "埃及", "孟加拉国", "越南", "智利", "芬兰", "罗马尼亚", "捷克共和国",
    "新西兰", "秘鲁", "希腊", "葡萄牙", "伊拉克", "阿尔及利亚", "卡塔尔", "哈萨克斯坦",
    "匈牙利", "科威特", "摩洛哥", "斯洛伐克", "厄瓜多尔", "古巴", "阿联酋", "白俄罗斯",
    "阿塞拜疆", "斯里兰卡", "缅甸", "乌兹别克斯坦", "多米尼加共和国", "乌拉圭", "哥斯达黎加",
    "斯洛文尼亚", "立陶宛", "巴拿马", "保加利亚", "克罗地亚", "约旦", "塞尔维亚",
    "黎巴嫩", "新加坡", "挪威", "丹麦", "瑞典", "阿富汗", "乌干达", "乌克兰",
    "阿尔巴尼亚", "巴林", "亚美尼亚", "蒙古", "牙买加", "纳米比亚", "博茨瓦纳", 
    "加蓬", "莱索托", "毛里求斯", "斯威士兰", "冰岛", "马耳他", "塞浦路斯", 
    "文莱", "巴巴多斯", "马尔代夫", "卢森堡公国", "不丹", "东帝汶民主共和国",
    "中非共和国", "乍得", "也门", "伊朗伊斯兰共和国", "伯利兹城", "佛得角", 
    "津巴布韦", "洪都拉斯", "莫桑比克", "黑山共和国", "利比亚", "肯尼亚", 
    "台湾", "大韩民国", "爱沙尼亚", "拉脱维亚", "立陶宛", "波兰", "匈牙利",
    "阿拉伯联合酋长国", "阿曼", "巴基斯坦", "尼泊尔", "马来西亚", "泰国", "菲律宾",
    "老挝", "柬埔寨", "巴布亚新几内亚", "斐济", "所罗门群岛", "瓦努阿图", "萨摩亚",
    "汤加", "基里巴斯", "图瓦卢", "瑙鲁", "帕劳", "马绍尔群岛", "密克罗尼西亚联邦",
    "委内瑞拉", "哥伦比亚", "玻利维亚", "巴拉圭", "苏里南", "圭亚那", "法属圭亚那"
  ),
  english_name = c(
    "China", "United States of America", "India", "Russia", "Japan", "Germany", "United Kingdom", "France", "Italy", "Brazil",
    "Canada", "South Korea", "Spain", "Australia", "Mexico", "Indonesia", "Netherlands", "Saudi Arabia",
    "Turkey", "Switzerland", "Belgium", "Ireland", "Israel", "Austria", "Nigeria", "Argentina",
    "South Africa", "Egypt", "Bangladesh", "Vietnam", "Chile", "Finland", "Romania", "Czech Republic",
    "New Zealand", "Peru", "Greece", "Portugal", "Iraq", "Algeria", "Qatar", "Kazakhstan",
    "Hungary", "Kuwait", "Morocco", "Slovakia", "Ecuador", "Cuba", "United Arab Emirates", "Belarus",
    "Azerbaijan", "Sri Lanka", "Myanmar", "Uzbekistan", "Dominican Republic", "Uruguay", "Costa Rica",
    "Slovenia", "Lithuania", "Panama", "Bulgaria", "Croatia", "Jordan", "Serbia",
    "Lebanon", "Singapore", "Norway", "Denmark", "Sweden", "Afghanistan", "Uganda", "Ukraine",
    "Albania", "Bahrain", "Armenia", "Mongolia", "Jamaica", "Namibia", "Botswana",
    "Gabon", "Lesotho", "Mauritius", "Eswatini", "Iceland", "Malta", "Cyprus",
    "Brunei", "Barbados", "Maldives", "Luxembourg", "Bhutan", "East Timor",
    "Central African Republic", "Chad", "Yemen", "Iran", "Belize", "Cape Verde",
    "Zimbabwe", "Honduras", "Mozambique", "Montenegro", "Libya", "Kenya",
    "Taiwan", "South Korea", "Estonia", "Latvia", "Lithuania", "Poland", "Hungary",
    "United Arab Emirates", "Oman", "Pakistan", "Nepal", "Malaysia", "Thailand", "Philippines",
    "Laos", "Cambodia", "Papua New Guinea", "Fiji", "Solomon Islands", "Vanuatu", "Samoa",
    "Tonga", "Kiribati", "Tuvalu", "Nauru", "Palau", "Marshall Islands", "Micronesia",
    "Venezuela", "Colombia", "Bolivia", "Paraguay", "Suriname", "Guyana", "French Guiana"
  ),
  stringsAsFactors = FALSE
)

# 保存映射表
write.csv(complete_mapping, "complete_country_name_mapping.csv", row.names = FALSE, fileEncoding = "UTF-8")

cat("\n完整映射表已保存到: complete_country_name_mapping.csv\n")
cat("映射表包含", nrow(complete_mapping), "个国家\n")

# 检查哪些中文名称没有映射
unmapped_names <- chinese_names[!chinese_names %in% complete_mapping$chinese_name]
if(length(unmapped_names) > 0) {
  cat("\n未映射的中文国家名称:\n")
  for(name in unmapped_names) {
    cat("-", name, "\n")
  }
} else {
  cat("\n所有中文国家名称都已映射！\n")
}

cat("\n=== 映射表创建完成 ===\n")
