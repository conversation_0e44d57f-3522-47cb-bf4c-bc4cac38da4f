# 基于真实GBD数据的脑卒中DALYs分析 - Table 2复现
# 使用处理后的真实数据进行分析

library(dplyr)
library(tidyr)
library(ggplot2)

# 1. 读取真实数据
load_real_data <- function() {
  cat("读取真实GBD数据...\n")
  
  # 读取处理后的真实数据
  real_data <- read.csv("stroke_real_gbd_dalys_data.csv", stringsAsFactors = FALSE)
  
  cat("数据概览:\n")
  cat("- 总行数:", nrow(real_data), "\n")
  cat("- 风险因素数量:", length(unique(real_data$risk_factor)), "\n")
  cat("- SDI分层数量:", length(unique(real_data$sdi_level)), "\n")
  cat("- 年份:", paste(unique(real_data$year), collapse = ", "), "\n")
  
  return(real_data)
}

# 2. 数据清理和标准化
clean_real_data <- function(real_data) {
  cat("清理和标准化数据...\n")
  
  # 标准化SDI分层名称
  cleaned_data <- real_data %>%
    mutate(
      # 标准化SDI分层
      sdi_level_standard = case_when(
        grepl("高SDI|High SDI", sdi_level) ~ "High SDI",
        grepl("高中SDI|High-middle SDI", sdi_level) ~ "High-middle SDI", 
        grepl("中SDI|Middle SDI", sdi_level) ~ "Middle SDI",
        grepl("中低SDI|Low-middle SDI", sdi_level) ~ "Low-middle SDI",
        grepl("低SDI|Low SDI", sdi_level) ~ "Low SDI",
        grepl("全球|Global|世界", sdi_level) ~ "Global",
        TRUE ~ sdi_level
      ),
      
      # 标准化风险因素名称
      risk_factor_standard = case_when(
        grepl("烟草|吸烟|Smoking", risk_factor) ~ "吸烟 (Smoking)",
        grepl("饮酒|Alcohol", risk_factor) ~ "饮酒 (Alcohol use)",
        grepl("高血压|High blood pressure", risk_factor) ~ "高血压 (High blood pressure)",
        grepl("高血糖|High fasting plasma glucose", risk_factor) ~ "高血糖 (High fasting plasma glucose)",
        grepl("饮食|Diet", risk_factor) ~ "不健康饮食 (Diet risks)",
        grepl("身体活动|Physical activity", risk_factor) ~ "身体活动不足 (Low physical activity)",
        grepl("BMI|肥胖", risk_factor) ~ "高BMI (High BMI)",
        grepl("体温|温度|Temperature", risk_factor) ~ "温度暴露 (Temperature exposure)",
        grepl("胆固醇|Cholesterol", risk_factor) ~ "高胆固醇 (High cholesterol)",
        grepl("空气污染|Air pollution", risk_factor) ~ "空气污染 (Air pollution)",
        grepl("环境|Environmental", risk_factor) ~ "其他环境因素 (Other environmental risks)",
        TRUE ~ risk_factor
      ),
      
      # 确保数值类型正确
      dalys_value = as.numeric(dalys_value),
      dalys_upper = as.numeric(dalys_upper),
      dalys_lower = as.numeric(dalys_lower)
    ) %>%
    filter(
      !is.na(dalys_value),
      !is.na(dalys_upper), 
      !is.na(dalys_lower),
      dalys_value > 0,
      year %in% c(1990, 2019)
    )
  
  cat("清理后数据行数:", nrow(cleaned_data), "\n")
  return(cleaned_data)
}

# 3. 聚合数据到SDI分层水平
aggregate_to_sdi <- function(cleaned_data) {
  cat("聚合数据到SDI分层水平...\n")
  
  # 筛选标准SDI分层
  standard_sdi <- c("Global", "High SDI", "High-middle SDI", "Middle SDI", "Low-middle SDI", "Low SDI")
  
  aggregated_data <- cleaned_data %>%
    filter(sdi_level_standard %in% standard_sdi) %>%
    group_by(risk_factor_standard, sdi_level_standard, year) %>%
    summarise(
      dalys_mean = mean(dalys_value, na.rm = TRUE),
      dalys_lower_mean = mean(dalys_lower, na.rm = TRUE),
      dalys_upper_mean = mean(dalys_upper, na.rm = TRUE),
      dalys_sum = sum(dalys_value, na.rm = TRUE),
      dalys_lower_sum = sum(dalys_lower, na.rm = TRUE),
      dalys_upper_sum = sum(dalys_upper, na.rm = TRUE),
      n_records = n(),
      .groups = 'drop'
    ) %>%
    # 选择合适的聚合方式（这里使用平均值）
    select(risk_factor_standard, sdi_level_standard, year, 
           dalys_value = dalys_mean, 
           dalys_lower = dalys_lower_mean, 
           dalys_upper = dalys_upper_mean,
           n_records)
  
  cat("聚合后数据行数:", nrow(aggregated_data), "\n")
  return(aggregated_data)
}

# 4. 计算AAPC
calculate_real_aapc <- function(aggregated_data) {
  cat("计算AAPC...\n")
  
  # 转换为宽格式
  wide_data <- aggregated_data %>%
    pivot_wider(
      names_from = year,
      values_from = c(dalys_value, dalys_lower, dalys_upper),
      names_sep = "_"
    ) %>%
    filter(
      !is.na(dalys_value_1990), !is.na(dalys_value_2019),
      dalys_value_1990 > 0, dalys_value_2019 > 0
    )
  
  # 计算AAPC
  aapc_results <- wide_data %>%
    mutate(
      # 主要AAPC
      aapc = (log(dalys_value_2019) - log(dalys_value_1990)) / (2019 - 1990) * 100,
      
      # 置信区间（保守估计）
      aapc_lower = (log(dalys_lower_2019) - log(dalys_upper_1990)) / (2019 - 1990) * 100,
      aapc_upper = (log(dalys_upper_2019) - log(dalys_lower_1990)) / (2019 - 1990) * 100
    ) %>%
    select(risk_factor_standard, sdi_level_standard, 
           dalys_1990 = dalys_value_1990, dalys_1990_lower = dalys_lower_1990, dalys_1990_upper = dalys_upper_1990,
           dalys_2019 = dalys_value_2019, dalys_2019_lower = dalys_lower_2019, dalys_2019_upper = dalys_upper_2019,
           aapc, aapc_lower, aapc_upper)
  
  cat("AAPC计算完成，结果行数:", nrow(aapc_results), "\n")
  return(aapc_results)
}

# 5. 创建Table 2格式
create_real_table2 <- function(aapc_results) {
  cat("创建Table 2格式...\n")
  
  # 格式化数值显示
  format_dalys <- function(value, lower, upper) {
    paste0(round(value, 1), " (", round(lower, 1), "-", round(upper, 1), ")")
  }
  
  format_aapc <- function(aapc, lower, upper) {
    paste0(round(aapc, 2), " (", round(lower, 2), " to ", round(upper, 2), ")")
  }
  
  # 创建格式化表格
  table2_data <- aapc_results %>%
    mutate(
      dalys_1990_formatted = format_dalys(dalys_1990, dalys_1990_lower, dalys_1990_upper),
      dalys_2019_formatted = format_dalys(dalys_2019, dalys_2019_lower, dalys_2019_upper),
      aapc_formatted = format_aapc(aapc, aapc_lower, aapc_upper)
    ) %>%
    select(risk_factor_standard, sdi_level_standard, 
           dalys_1990_formatted, dalys_2019_formatted, aapc_formatted)
  
  # 转换为宽格式表格
  table2_wide <- table2_data %>%
    pivot_longer(cols = c(dalys_1990_formatted, dalys_2019_formatted, aapc_formatted),
                 names_to = "metric", values_to = "value") %>%
    mutate(
      metric = case_when(
        metric == "dalys_1990_formatted" ~ "1990年DALYs",
        metric == "dalys_2019_formatted" ~ "2019年DALYs",
        metric == "aapc_formatted" ~ "AAPC (%)"
      )
    ) %>%
    pivot_wider(names_from = sdi_level_standard, values_from = value) %>%
    arrange(risk_factor_standard, metric)
  
  return(table2_wide)
}

# 6. 生成汇总统计
generate_real_summary <- function(aapc_results) {
  cat("生成汇总统计...\n")
  
  # 全球趋势汇总
  global_summary <- aapc_results %>%
    filter(sdi_level_standard == "Global") %>%
    arrange(aapc) %>%
    mutate(
      trend = ifelse(aapc < 0, "下降", "上升"),
      significance = ifelse(aapc_lower * aapc_upper > 0, "显著", "不显著")
    )
  
  # SDI分层汇总
  sdi_summary <- aapc_results %>%
    group_by(sdi_level_standard) %>%
    summarise(
      avg_aapc = mean(aapc, na.rm = TRUE),
      median_aapc = median(aapc, na.rm = TRUE),
      min_aapc = min(aapc, na.rm = TRUE),
      max_aapc = max(aapc, na.rm = TRUE),
      declining_factors = sum(aapc < 0, na.rm = TRUE),
      increasing_factors = sum(aapc > 0, na.rm = TRUE),
      .groups = 'drop'
    )
  
  # 风险因素汇总
  risk_summary <- aapc_results %>%
    group_by(risk_factor_standard) %>%
    summarise(
      avg_aapc = mean(aapc, na.rm = TRUE),
      median_aapc = median(aapc, na.rm = TRUE),
      min_aapc = min(aapc, na.rm = TRUE),
      max_aapc = max(aapc, na.rm = TRUE),
      .groups = 'drop'
    ) %>%
    arrange(avg_aapc)
  
  return(list(
    global_summary = global_summary,
    sdi_summary = sdi_summary,
    risk_summary = risk_summary
  ))
}

# 7. 主分析函数
main_real_analysis <- function() {
  cat("=== 开始基于真实数据的脑卒中DALYs分析 ===\n")
  
  # 步骤1：读取数据
  real_data <- load_real_data()
  
  # 步骤2：清理数据
  cleaned_data <- clean_real_data(real_data)
  
  # 步骤3：聚合数据
  aggregated_data <- aggregate_to_sdi(cleaned_data)
  
  # 步骤4：计算AAPC
  aapc_results <- calculate_real_aapc(aggregated_data)
  
  # 步骤5：创建Table 2
  table2 <- create_real_table2(aapc_results)
  
  # 步骤6：生成汇总统计
  summary_stats <- generate_real_summary(aapc_results)
  
  # 保存结果
  write.csv(aapc_results, "stroke_real_dalys_detailed_results.csv", row.names = FALSE)
  write.csv(table2, "stroke_real_dalys_table2_formatted.csv", row.names = FALSE)
  write.csv(summary_stats$global_summary, "stroke_real_global_summary.csv", row.names = FALSE)
  write.csv(summary_stats$sdi_summary, "stroke_real_sdi_summary.csv", row.names = FALSE)
  write.csv(summary_stats$risk_summary, "stroke_real_risk_summary.csv", row.names = FALSE)
  
  cat("=== 分析完成！===\n")
  cat("生成的文件：\n")
  cat("- stroke_real_dalys_detailed_results.csv\n")
  cat("- stroke_real_dalys_table2_formatted.csv\n")
  cat("- stroke_real_global_summary.csv\n")
  cat("- stroke_real_sdi_summary.csv\n")
  cat("- stroke_real_risk_summary.csv\n")
  
  return(list(
    aapc_results = aapc_results,
    table2 = table2,
    summary_stats = summary_stats
  ))
}

# 执行分析
if (!interactive()) {
  results <- main_real_analysis()
}
