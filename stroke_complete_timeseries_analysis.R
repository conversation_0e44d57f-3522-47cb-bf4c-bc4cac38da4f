# 脑卒中65岁以上人群发病率完整时间序列分析 (1990-2021)
# 使用所有32年数据计算AAPC

library(dplyr)
library(readr)
library(ggplot2)

cat("=== 脑卒中65岁以上人群发病率完整时间序列分析 ===\n")

# 读取全球汇总数据
global_data <- read.csv("stroke_incidence_global_summary_1990_2021.csv", 
                       stringsAsFactors = FALSE)

cat("数据读取完成，时间范围:", range(global_data$year), "\n")
cat("总数据点:", nrow(global_data), "个\n")

# 验证数据完整性
years_available <- sort(unique(global_data$year))
cat("可用年份:", paste(years_available, collapse = ", "), "\n")
cat("数据完整性: 共", length(years_available), "年数据\n")

# 改进的AAPC计算函数，显示更多统计信息
calculate_detailed_aapc <- function(data, value_col = "global_rate") {
  if(nrow(data) < 3) return(list(aapc = NA, r_squared = NA, p_value = NA, n_years = nrow(data)))
  
  # 使用对数线性回归计算AAPC
  years <- data$year
  values <- data[[value_col]]
  
  # 过滤掉NA值和非正值
  valid_idx <- !is.na(values) & values > 0
  if(sum(valid_idx) < 3) return(list(aapc = NA, r_squared = NA, p_value = NA, n_years = sum(valid_idx)))
  
  years <- years[valid_idx]
  values <- values[valid_idx]
  
  # 对数线性回归
  log_values <- log(values)
  model <- lm(log_values ~ years)
  
  # 提取统计信息
  slope <- coef(model)[2]
  r_squared <- summary(model)$r.squared
  p_value <- summary(model)$coefficients[2, 4]  # p-value for slope
  
  # 计算AAPC
  aapc <- (exp(slope) - 1) * 100
  
  return(list(
    aapc = aapc,
    r_squared = r_squared,
    p_value = p_value,
    n_years = length(years),
    start_rate = values[1],
    end_rate = values[length(values)]
  ))
}

# 计算全球总体AAPC（所有年龄组合计）
cat("\n=== 计算全球总体AAPC ===\n")
global_overall_data <- global_data %>%
  filter(sex_name == "合计") %>%
  group_by(year) %>%
  summarise(
    global_rate = mean(global_rate, na.rm = TRUE),
    .groups = "drop"
  )

global_overall_stats <- calculate_detailed_aapc(global_overall_data)
cat("全球总体 (1990-2021):\n")
cat("  - 数据年数:", global_overall_stats$n_years, "年\n")
cat("  - 1990年发病率:", round(global_overall_stats$start_rate, 2), "每10万人\n")
cat("  - 2021年发病率:", round(global_overall_stats$end_rate, 2), "每10万人\n")
cat("  - AAPC:", round(global_overall_stats$aapc, 2), "%\n")
cat("  - R²:", round(global_overall_stats$r_squared, 4), "\n")
cat("  - P值:", format(global_overall_stats$p_value, scientific = TRUE), "\n")

# 计算性别分组AAPC
cat("\n=== 计算性别分组AAPC ===\n")
sex_stats <- global_data %>%
  filter(sex_name %in% c("男", "女")) %>%
  group_by(sex_name, year) %>%
  summarise(
    global_rate = mean(global_rate, na.rm = TRUE),
    .groups = "drop"
  ) %>%
  group_by(sex_name) %>%
  group_modify(~ {
    stats <- calculate_detailed_aapc(.x)
    data.frame(
      n_years = stats$n_years,
      start_rate = stats$start_rate,
      end_rate = stats$end_rate,
      aapc = stats$aapc,
      r_squared = stats$r_squared,
      p_value = stats$p_value
    )
  })

for(i in 1:nrow(sex_stats)) {
  sex_name <- ifelse(sex_stats$sex_name[i] == "男", "男性", "女性")
  cat(sex_name, "(1990-2021):\n")
  cat("  - 数据年数:", sex_stats$n_years[i], "年\n")
  cat("  - 1990年发病率:", round(sex_stats$start_rate[i], 2), "每10万人\n")
  cat("  - 2021年发病率:", round(sex_stats$end_rate[i], 2), "每10万人\n")
  cat("  - AAPC:", round(sex_stats$aapc[i], 2), "%\n")
  cat("  - R²:", round(sex_stats$r_squared[i], 4), "\n")
  cat("  - P值:", format(sex_stats$p_value[i], scientific = TRUE), "\n")
}

# 计算年龄组AAPC
cat("\n=== 计算年龄组AAPC ===\n")
age_stats <- global_data %>%
  filter(sex_name == "合计") %>%
  group_by(age_name) %>%
  group_modify(~ {
    stats <- calculate_detailed_aapc(.x)
    data.frame(
      n_years = stats$n_years,
      start_rate = stats$start_rate,
      end_rate = stats$end_rate,
      aapc = stats$aapc,
      r_squared = stats$r_squared,
      p_value = stats$p_value
    )
  })

for(i in 1:nrow(age_stats)) {
  cat(age_stats$age_name[i], "(1990-2021):\n")
  cat("  - 数据年数:", age_stats$n_years[i], "年\n")
  cat("  - 1990年发病率:", round(age_stats$start_rate[i], 2), "每10万人\n")
  cat("  - 2021年发病率:", round(age_stats$end_rate[i], 2), "每10万人\n")
  cat("  - AAPC:", round(age_stats$aapc[i], 2), "%\n")
  cat("  - R²:", round(age_stats$r_squared[i], 4), "\n")
  cat("  - P值:", format(age_stats$p_value[i], scientific = TRUE), "\n")
}

# 创建时间序列可视化
cat("\n=== 创建时间序列可视化 ===\n")

# 全球总体趋势图
global_trend_plot <- ggplot(global_overall_data, aes(x = year, y = global_rate)) +
  geom_line(color = "blue", size = 1) +
  geom_point(color = "blue", size = 2) +
  geom_smooth(method = "lm", se = TRUE, color = "red", linetype = "dashed") +
  labs(
    title = "脑卒中65岁以上人群全球发病率趋势 (1990-2021)",
    subtitle = paste0("AAPC = ", round(global_overall_stats$aapc, 2), "% (R² = ", 
                     round(global_overall_stats$r_squared, 3), ")"),
    x = "年份",
    y = "年龄标准化发病率 (每10万人)"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(size = 14, face = "bold"),
    plot.subtitle = element_text(size = 12),
    axis.title = element_text(size = 12),
    axis.text = element_text(size = 10)
  )

ggsave("stroke_global_trend_1990_2021.png", global_trend_plot, 
       width = 12, height = 8, dpi = 300, bg = "white")

# 性别分组趋势图
sex_trend_data <- global_data %>%
  filter(sex_name %in% c("男", "女")) %>%
  group_by(sex_name, year) %>%
  summarise(
    global_rate = mean(global_rate, na.rm = TRUE),
    .groups = "drop"
  ) %>%
  mutate(sex_name = ifelse(sex_name == "男", "男性", "女性"))

sex_trend_plot <- ggplot(sex_trend_data, aes(x = year, y = global_rate, color = sex_name)) +
  geom_line(size = 1) +
  geom_point(size = 2) +
  geom_smooth(method = "lm", se = TRUE, linetype = "dashed") +
  labs(
    title = "脑卒中65岁以上人群发病率性别趋势 (1990-2021)",
    x = "年份",
    y = "年龄标准化发病率 (每10万人)",
    color = "性别"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(size = 14, face = "bold"),
    axis.title = element_text(size = 12),
    axis.text = element_text(size = 10),
    legend.title = element_text(size = 12),
    legend.text = element_text(size = 10)
  )

ggsave("stroke_sex_trend_1990_2021.png", sex_trend_plot, 
       width = 12, height = 8, dpi = 300, bg = "white")

cat("时间序列图已保存:\n")
cat("  - stroke_global_trend_1990_2021.png\n")
cat("  - stroke_sex_trend_1990_2021.png\n")

cat("\n=== 完整时间序列分析完成 ===\n")
cat("所有AAPC计算均基于1990-2021年完整32年数据\n")
