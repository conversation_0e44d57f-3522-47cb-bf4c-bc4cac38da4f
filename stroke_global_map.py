#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脑卒中65岁以上人群患病率AAPC全球地图可视化
基于1990-2019年数据
"""

import pandas as pd
import matplotlib.pyplot as plt
import geopandas as gpd
import numpy as np
from matplotlib.colors import ListedColormap
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_stroke_map():
    """创建脑卒中AAPC全球地图"""
    
    # 读取数据
    try:
        data = pd.read_csv("脑卒中65岁以上人群患病率分析表.csv", encoding='utf-8')
    except:
        data = pd.read_csv("脑卒中65岁以上人群患病率分析表.csv", encoding='gbk')
    
    # 提取地区AAPC数据
    region_data = data[data['分类'].isin([
        '撒哈拉以南非洲', '东南亚、东亚和大洋洲', '南亚', '高收入',
        '中欧、东欧和中亚', '拉丁美洲和加勒比海', '北非和中东'
    ])][['分类', 'AAPC(%)']].copy()
    
    # 创建国家到地区的映射
    country_region_mapping = {
        # 撒哈拉以南非洲
        'Nigeria': '撒哈拉以南非洲', 'Ethiopia': '撒哈拉以南非洲', 'South Africa': '撒哈拉以南非洲',
        'Kenya': '撒哈拉以南非洲', 'Uganda': '撒哈拉以南非洲', 'Tanzania': '撒哈拉以南非洲',
        'Ghana': '撒哈拉以南非洲', 'Mozambique': '撒哈拉以南非洲', 'Madagascar': '撒哈拉以南非洲',
        'Cameroon': '撒哈拉以南非洲', 'Angola': '撒哈拉以南非洲', 'Niger': '撒哈拉以南非洲',
        'Burkina Faso': '撒哈拉以南非洲', 'Mali': '撒哈拉以南非洲', 'Malawi': '撒哈拉以南非洲',
        'Zambia': '撒哈拉以南非洲', 'Somalia': '撒哈拉以南非洲', 'Senegal': '撒哈拉以南非洲',
        'Chad': '撒哈拉以南非洲', 'Zimbabwe': '撒哈拉以南非洲', 'Guinea': '撒哈拉以南非洲',
        'Rwanda': '撒哈拉以南非洲', 'Benin': '撒哈拉以南非洲', 'Burundi': '撒哈拉以南非洲',
        'South Sudan': '撒哈拉以南非洲', 'Togo': '撒哈拉以南非洲', 'Sierra Leone': '撒哈拉以南非洲',
        'Liberia': '撒哈拉以南非洲', 'Central African Republic': '撒哈拉以南非洲',
        'Mauritania': '撒哈拉以南非洲', 'Eritrea': '撒哈拉以南非洲', 'Gambia': '撒哈拉以南非洲',
        'Botswana': '撒哈拉以南非洲', 'Namibia': '撒哈拉以南非洲', 'Gabon': '撒哈拉以南非洲',
        'Lesotho': '撒哈拉以南非洲', 'Guinea-Bissau': '撒哈拉以南非洲', 'Equatorial Guinea': '撒哈拉以南非洲',
        'Mauritius': '撒哈拉以南非洲', 'Eswatini': '撒哈拉以南非洲', 'Djibouti': '撒哈拉以南非洲',
        'Comoros': '撒哈拉以南非洲', 'Cape Verde': '撒哈拉以南非洲', 'São Tomé and Príncipe': '撒哈拉以南非洲',
        
        # 东南亚、东亚和大洋洲
        'China': '东南亚、东亚和大洋洲', 'Indonesia': '东南亚、东亚和大洋洲', 'Japan': '东南亚、东亚和大洋洲',
        'Philippines': '东南亚、东亚和大洋洲', 'Vietnam': '东南亚、东亚和大洋洲', 'Thailand': '东南亚、东亚和大洋洲',
        'Myanmar': '东南亚、东亚和大洋洲', 'South Korea': '东南亚、东亚和大洋洲', 'Malaysia': '东南亚、东亚和大洋洲',
        'Cambodia': '东南亚、东亚和大洋洲', 'Laos': '东南亚、东亚和大洋洲', 'Singapore': '东南亚、东亚和大洋洲',
        'Mongolia': '东南亚、东亚和大洋洲', 'Brunei': '东南亚、东亚和大洋洲', 'Timor-Leste': '东南亚、东亚和大洋洲',
        'Australia': '东南亚、东亚和大洋洲', 'Papua New Guinea': '东南亚、东亚和大洋洲', 'New Zealand': '东南亚、东亚和大洋洲',
        'Fiji': '东南亚、东亚和大洋洲', 'Solomon Islands': '东南亚、东亚和大洋洲', 'Vanuatu': '东南亚、东亚和大洋洲',
        'Samoa': '东南亚、东亚和大洋洲', 'Kiribati': '东南亚、东亚和大洋洲', 'Tonga': '东南亚、东亚和大洋洲',
        'Micronesia': '东南亚、东亚和大洋洲', 'Palau': '东南亚、东亚和大洋洲', 'Marshall Islands': '东南亚、东亚和大洋洲',
        'Nauru': '东南亚、东亚和大洋洲', 'Tuvalu': '东南亚、东亚和大洋洲',
        
        # 南亚
        'India': '南亚', 'Pakistan': '南亚', 'Bangladesh': '南亚', 'Afghanistan': '南亚',
        'Nepal': '南亚', 'Sri Lanka': '南亚', 'Bhutan': '南亚', 'Maldives': '南亚',
        
        # 高收入
        'United States of America': '高收入', 'Germany': '高收入', 'United Kingdom': '高收入',
        'France': '高收入', 'Italy': '高收入', 'Canada': '高收入', 'Spain': '高收入',
        'Netherlands': '高收入', 'Belgium': '高收入', 'Switzerland': '高收入', 'Austria': '高收入',
        'Sweden': '高收入', 'Norway': '高收入', 'Denmark': '高收入', 'Finland': '高收入',
        'Ireland': '高收入', 'Portugal': '高收入', 'Greece': '高收入', 'Israel': '高收入',
        'Luxembourg': '高收入', 'Iceland': '高收入', 'Malta': '高收入', 'Cyprus': '高收入',
        
        # 中欧、东欧和中亚
        'Russia': '中欧、东欧和中亚', 'Poland': '中欧、东欧和中亚', 'Ukraine': '中欧、东欧和中亚',
        'Romania': '中欧、东欧和中亚', 'Czech Republic': '中欧、东欧和中亚', 'Hungary': '中欧、东欧和中亚',
        'Belarus': '中欧、东欧和中亚', 'Bulgaria': '中欧、东欧和中亚', 'Serbia': '中欧、东欧和中亚',
        'Slovakia': '中欧、东欧和中亚', 'Croatia': '中欧、东欧和中亚', 'Bosnia and Herzegovina': '中欧、东欧和中亚',
        'Albania': '中欧、东欧和中亚', 'Lithuania': '中欧、东欧和中亚', 'Slovenia': '中欧、东欧和中亚',
        'Latvia': '中欧、东欧和中亚', 'Estonia': '中欧、东欧和中亚', 'North Macedonia': '中欧、东欧和中亚',
        'Moldova': '中欧、东欧和中亚', 'Montenegro': '中欧、东欧和中亚', 'Kazakhstan': '中欧、东欧和中亚',
        'Uzbekistan': '中欧、东欧和中亚', 'Tajikistan': '中欧、东欧和中亚', 'Kyrgyzstan': '中欧、东欧和中亚',
        'Turkmenistan': '中欧、东欧和中亚', 'Georgia': '中欧、东欧和中亚', 'Armenia': '中欧、东欧和中亚',
        'Azerbaijan': '中欧、东欧和中亚',
        
        # 拉丁美洲和加勒比海
        'Brazil': '拉丁美洲和加勒比海', 'Mexico': '拉丁美洲和加勒比海', 'Colombia': '拉丁美洲和加勒比海',
        'Argentina': '拉丁美洲和加勒比海', 'Peru': '拉丁美洲和加勒比海', 'Venezuela': '拉丁美洲和加勒比海',
        'Chile': '拉丁美洲和加勒比海', 'Ecuador': '拉丁美洲和加勒比海', 'Guatemala': '拉丁美洲和加勒比海',
        'Cuba': '拉丁美洲和加勒比海', 'Bolivia': '拉丁美洲和加勒比海', 'Haiti': '拉丁美洲和加勒比海',
        'Dominican Republic': '拉丁美洲和加勒比海', 'Honduras': '拉丁美洲和加勒比海', 'Paraguay': '拉丁美洲和加勒比海',
        'Nicaragua': '拉丁美洲和加勒比海', 'El Salvador': '拉丁美洲和加勒比海', 'Costa Rica': '拉丁美洲和加勒比海',
        'Panama': '拉丁美洲和加勒比海', 'Uruguay': '拉丁美洲和加勒比海', 'Jamaica': '拉丁美洲和加勒比海',
        'Trinidad and Tobago': '拉丁美洲和加勒比海', 'Guyana': '拉丁美洲和加勒比海', 'Suriname': '拉丁美洲和加勒比海',
        'Belize': '拉丁美洲和加勒比海', 'Barbados': '拉丁美洲和加勒比海',
        
        # 北非和中东
        'Egypt': '北非和中东', 'Iran': '北非和中东', 'Turkey': '北非和中东', 'Iraq': '北非和中东',
        'Saudi Arabia': '北非和中东', 'Yemen': '北非和中东', 'Syria': '北非和中东', 'Jordan': '北非和中东',
        'Lebanon': '北非和中东', 'United Arab Emirates': '北非和中东', 'Oman': '北非和中东',
        'Kuwait': '北非和中东', 'Qatar': '北非和中东', 'Bahrain': '北非和中东', 'Algeria': '北非和中东',
        'Morocco': '北非和中东', 'Sudan': '北非和中东', 'Tunisia': '北非和中东', 'Libya': '北非和中东'
    }
    
    # 创建AAPC映射字典
    aapc_dict = dict(zip(region_data['分类'], region_data['AAPC(%)']))
    
    try:
        # 尝试加载世界地图数据
        # 使用在线数据源
        world_url = "https://raw.githubusercontent.com/holtzy/The-Python-Graph-Gallery/master/static/data/world-110m2.json"
        try:
            world = gpd.read_file(world_url)
        except:
            # 如果在线数据不可用，创建简单的替代可视化
            print("无法加载世界地图数据，将创建简单的条形图")
            create_simple_chart(region_data)
            return
        
        # 为每个国家分配AAPC值
        world['region'] = world['name'].map(country_region_mapping)
        world['aapc'] = world['region'].map(aapc_dict)
        
        # 创建AAPC分组
        def categorize_aapc(aapc):
            if pd.isna(aapc):
                return 'No data'
            elif aapc < -0.5:
                return '< -0.5'
            elif aapc < 0:
                return '-0.5 to < 0'
            elif aapc < 0.5:
                return '0 to < 0.5'
            elif aapc < 1.0:
                return '0.5 to < 1.0'
            else:
                return '≥ 1.0'
        
        world['aapc_category'] = world['aapc'].apply(categorize_aapc)
        
        # 定义颜色
        colors = ['#2166ac', '#67a9cf', '#f7f7f7', '#fdbf6f', '#e31a1c', '#cccccc']
        categories = ['< -0.5', '-0.5 to < 0', '0 to < 0.5', '0.5 to < 1.0', '≥ 1.0', 'No data']
        color_map = dict(zip(categories, colors))
        
        # 创建图形
        fig, ax = plt.subplots(1, 1, figsize=(20, 12))
        
        # 绘制地图
        for category in categories:
            subset = world[world['aapc_category'] == category]
            if not subset.empty:
                subset.plot(ax=ax, color=color_map[category], edgecolor='white', linewidth=0.5, label=category)
        
        # 设置标题和样式
        ax.set_title('Average Annual Percentage Change in Stroke Prevalence\nAmong people aged ≥65 years, 1990-2019', 
                    fontsize=16, fontweight='bold', pad=20)
        
        # 移除坐标轴
        ax.set_xlim(-180, 180)
        ax.set_ylim(-60, 85)
        ax.axis('off')
        
        # 添加图例
        legend_elements = [plt.Rectangle((0,0),1,1, facecolor=color_map[cat], label=f'{cat}%') 
                          for cat in categories if cat != 'No data']
        legend_elements.append(plt.Rectangle((0,0),1,1, facecolor=color_map['No data'], label='No data'))
        
        ax.legend(handles=legend_elements, loc='lower left', bbox_to_anchor=(0.02, 0.02), 
                 title='AAPC (%)', title_fontsize=12, fontsize=10, frameon=True, fancybox=True, shadow=True)
        
        # 保存图片
        plt.tight_layout()
        plt.savefig('stroke_aapc_global_map.png', dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()
        
        # 打印数据摘要
        print("脑卒中65岁以上人群AAPC数据摘要:")
        print(region_data.to_string(index=False))
        
        print("\nAAPC分组统计:")
        print(world['aapc_category'].value_counts())
        
    except Exception as e:
        print(f"创建地图时出错: {e}")
        print("请确保已安装geopandas和相关依赖包")
        
        # 创建简单的条形图作为替代
        create_simple_chart(region_data)

def create_simple_chart(region_data):
    """创建简单的条形图作为地图的替代"""
    # 创建两个子图：条形图和模拟地图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))

    # 左侧：条形图
    bars = ax1.bar(range(len(region_data)), region_data['AAPC(%)'],
                   color=['#e31a1c' if x > 0 else '#2166ac' for x in region_data['AAPC(%)']])

    # 设置标签
    ax1.set_xticks(range(len(region_data)))
    ax1.set_xticklabels(region_data['分类'], rotation=45, ha='right')
    ax1.set_ylabel('AAPC (%)', fontsize=12)
    ax1.set_title('Average Annual Percentage Change in Stroke Prevalence\nAmong people aged ≥65 years by Region, 1990-2019',
                  fontsize=14, fontweight='bold')

    # 添加数值标签
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + (0.01 if height > 0 else -0.03),
                f'{height:.2f}%', ha='center', va='bottom' if height > 0 else 'top')

    # 添加零线
    ax1.axhline(y=0, color='black', linestyle='-', linewidth=0.8)
    ax1.grid(True, alpha=0.3)

    # 右侧：创建简化的世界地图示意图
    create_simplified_world_map(ax2, region_data)

    plt.tight_layout()
    plt.savefig('stroke_aapc_comprehensive_chart.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()

def create_simplified_world_map(ax, region_data):
    """创建简化的世界地图示意图"""
    # 定义各地区的大致位置（简化的矩形表示）
    regions_coords = {
        '撒哈拉以南非洲': (0.2, 0.2, 0.3, 0.4),  # (x, y, width, height)
        '东南亚、东亚和大洋洲': (0.6, 0.3, 0.35, 0.4),
        '南亚': (0.5, 0.35, 0.15, 0.2),
        '高收入': (0.1, 0.6, 0.4, 0.3),  # 主要是北美和欧洲
        '中欧、东欧和中亚': (0.4, 0.5, 0.3, 0.3),
        '拉丁美洲和加勒比海': (0.05, 0.1, 0.2, 0.5),
        '北非和中东': (0.35, 0.4, 0.2, 0.25)
    }

    # 创建AAPC值到颜色的映射
    aapc_dict = dict(zip(region_data['分类'], region_data['AAPC(%)']))

    # 定义颜色映射
    def get_color(aapc):
        if aapc < -0.5:
            return '#2166ac'
        elif aapc < 0:
            return '#67a9cf'
        elif aapc < 0.5:
            return '#f7f7f7'
        elif aapc < 1.0:
            return '#fdbf6f'
        else:
            return '#e31a1c'

    # 绘制各地区
    for region, coords in regions_coords.items():
        if region in aapc_dict:
            aapc = aapc_dict[region]
            color = get_color(aapc)
            x, y, width, height = coords
            rect = plt.Rectangle((x, y), width, height, facecolor=color,
                               edgecolor='black', linewidth=1, alpha=0.8)
            ax.add_patch(rect)

            # 添加地区标签和AAPC值
            ax.text(x + width/2, y + height/2, f'{region}\n{aapc:.2f}%',
                   ha='center', va='center', fontsize=8, fontweight='bold',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.7))

    # 设置坐标轴
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.set_aspect('equal')
    ax.set_title('Simplified World Map: Stroke AAPC by Region\n(1990-2019, Age ≥65)',
                fontsize=14, fontweight='bold')
    ax.axis('off')

    # 添加图例
    legend_elements = [
        plt.Rectangle((0,0),1,1, facecolor='#2166ac', label='< -0.5%'),
        plt.Rectangle((0,0),1,1, facecolor='#67a9cf', label='-0.5 to < 0%'),
        plt.Rectangle((0,0),1,1, facecolor='#f7f7f7', label='0 to < 0.5%'),
        plt.Rectangle((0,0),1,1, facecolor='#fdbf6f', label='0.5 to < 1.0%'),
        plt.Rectangle((0,0),1,1, facecolor='#e31a1c', label='≥ 1.0%')
    ]
    ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(0.98, 0.98),
             title='AAPC (%)', title_fontsize=10, fontsize=9)

if __name__ == "__main__":
    create_stroke_map()
