# 脑卒中65岁以上人群DALYs年均变化率地图 (1990-2021)
# 基于T1DM风格的完整布局

library(dplyr)
library(ggplot2)
library(maps)
library(viridis)
library(RColorBrewer)
library(gridExtra)
library(grid)

# 1. 数据加载和处理
cat("Loading stroke DALYs data...\n")

# 加载DALYs数据文件
dalys_files <- c(
  '死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-3/IHME-GBD_2021_DATA-336925ca-3.csv',
  '死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-4/IHME-GBD_2021_DATA-336925ca-4.csv'
)

all_dalys_data <- data.frame()
for(file in dalys_files) {
  data <- read.csv(file)
  all_dalys_data <- rbind(all_dalys_data, data)
}

cat("Total DALYs records:", nrow(all_dalys_data), "\n")

# 2. 筛选65岁以上人群数据
stroke_65plus <- all_dalys_data %>%
  filter(
    cause_name == "脑卒中",
    measure_name == "伤残调整生命年",
    age_name %in% c("65-69岁", "70-74岁", "75-79岁", "80-84岁", "85岁以上"),
    sex_name == "合计",
    metric_name == "率"  # 使用年龄标准化率
  ) %>%
  select(location_name, year, age_name, val) %>%
  group_by(location_name, year) %>%
  summarise(dalys_rate = sum(val, na.rm = TRUE), .groups = 'drop')

cat("Countries with 65+ DALYs data:", length(unique(stroke_65plus$location_name)), "\n")
cat("Year range:", min(stroke_65plus$year), "-", max(stroke_65plus$year), "\n")

# 3. 计算AAPC (Average Annual Percentage Change)
calculate_aapc <- function(data) {
  if(nrow(data) < 2) return(NA)
  
  # 对数线性回归
  model <- lm(log(dalys_rate + 0.001) ~ year, data = data)  # 加小常数避免log(0)
  slope <- coef(model)[2]
  
  # AAPC = (e^slope - 1) * 100
  aapc <- (exp(slope) - 1) * 100
  return(aapc)
}

# 按国家计算AAPC
country_aapc <- stroke_65plus %>%
  group_by(location_name) %>%
  do(aapc = calculate_aapc(.)) %>%
  mutate(aapc = unlist(aapc)) %>%
  filter(!is.na(aapc))

cat("Countries with valid AAPC:", nrow(country_aapc), "\n")
cat("AAPC range:", round(min(country_aapc$aapc, na.rm=TRUE), 2), "to", 
    round(max(country_aapc$aapc, na.rm=TRUE), 2), "\n")

# 4. 国家名称标准化
country_mapping <- data.frame(
  gbd_name = c("美国", "英国", "俄罗斯", "韩国", "伊朗", "叙利亚", "委内瑞拉", "玻利维亚", 
               "坦桑尼亚", "刚果民主共和国", "老挝", "越南", "文莱", "北马其顿", "摩尔多瓦",
               "中国", "日本", "德国", "法国", "意大利", "西班牙", "加拿大", "澳大利亚",
               "巴西", "印度", "南非", "埃及", "土耳其", "沙特阿拉伯", "阿联酋"),
  map_name = c("USA", "UK", "Russia", "South Korea", "Iran", "Syria", "Venezuela", "Bolivia",
               "Tanzania", "Democratic Republic of the Congo", "Laos", "Vietnam", "Brunei", 
               "North Macedonia", "Moldova", "China", "Japan", "Germany", "France", "Italy", 
               "Spain", "Canada", "Australia", "Brazil", "India", "South Africa", "Egypt", 
               "Turkey", "Saudi Arabia", "UAE"),
  stringsAsFactors = FALSE
)

# 获取世界地图数据
world_map <- map_data("world")

# 标准化国家名称
country_aapc$region <- country_aapc$location_name
for(i in 1:nrow(country_mapping)) {
  country_aapc$region[country_aapc$location_name == country_mapping$gbd_name[i]] <- country_mapping$map_name[i]
}

# 合并地图数据
map_data_with_aapc <- merge(world_map, country_aapc, by = "region", all.x = TRUE)
map_data_with_aapc <- map_data_with_aapc[order(map_data_with_aapc$order), ]

# 5. 设置颜色分级 - 平衡的9级色阶
aapc_range <- range(country_aapc$aapc, na.rm = TRUE)
cat("Setting up balanced color scale for AAPC range:", round(aapc_range[1], 2), "to", round(aapc_range[2], 2), "\n")

# 基于实际数据范围创建平衡的分级点 (-6.21 to 1.88)
breaks <- c(-6.5, -4.0, -3.0, -2.0, -1.0, -0.5, 0, 0.5, 1.0, 1.88)
labels <- c("< -4.0", "-4.0 to -3.0", "-3.0 to -2.0", "-2.0 to -1.0",
           "-1.0 to -0.5", "-0.5 to 0", "0 to 0.5", "0.5 to 1.0", "> 1.0")

# 平衡的蓝-红色带 (9个颜色对应9个区间)
colors <- c("#08519c", "#2171b5", "#4292c6", "#6baed6", "#9ecae1",
           "#fee0d2", "#fcbba1", "#fc9272", "#de2d26")

# 分类AAPC值
map_data_with_aapc$aapc_category <- cut(map_data_with_aapc$aapc,
                                       breaks = breaks,
                                       labels = labels,
                                       include.lowest = TRUE)

cat("Color distribution:\n")
print(table(map_data_with_aapc$aapc_category, useNA = "ifany"))

# 保存处理后的数据
write.csv(country_aapc, "stroke_dalys_65plus_aapc_results_1990_2021.csv", row.names = FALSE)
cat("AAPC results saved to: stroke_dalys_65plus_aapc_results_1990_2021.csv\n")

cat("Script completed successfully!\n")
cat("Next: Run the visualization script to create the maps.\n")
