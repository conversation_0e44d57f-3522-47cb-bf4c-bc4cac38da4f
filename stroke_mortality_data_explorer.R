# 脑卒中死亡率数据探索脚本
# 基于本地死亡损伤数据库进行1990-2021年AAPC分析

library(readr)
library(dplyr)
library(ggplot2)

# 读取第一个数据文件
cat("正在读取数据文件...\n")
data1 <- read_csv('死亡损伤-数据库/204国家/IHME-GBD_2021_DATA-336925ca-1/IHME-GBD_2021_DATA-336925ca-1.csv')

# 基本数据信息
cat("=== 数据基本信息 ===\n")
cat("年份范围:", min(data1$year), "-", max(data1$year), "\n")
cat("总行数:", nrow(data1), "\n")
cat("脑卒中数据行数:", sum(data1$cause_name == "脑卒中"), "\n")
cat("国家数量:", length(unique(data1$location_name)), "\n")

# 查看年龄组
cat("\n=== 年龄组信息 ===\n")
age_groups <- unique(data1$age_name)
cat("年龄组数量:", length(age_groups), "\n")
cat("年龄组列表:\n")
for(i in 1:min(15, length(age_groups))) {
  cat(i, ":", age_groups[i], "\n")
}

# 查看指标类型
cat("\n=== 指标类型 ===\n")
metrics <- unique(data1$metric_name)
cat("指标类型:", paste(metrics, collapse=", "), "\n")

# 筛选脑卒中死亡率数据（65岁及以上）
cat("\n=== 筛选65岁及以上脑卒中死亡率数据 ===\n")
stroke_data <- data1 %>%
  filter(cause_name == "脑卒中", 
         metric_name == "率",  # 死亡率
         sex_name == "合计") %>%  # 合计性别
  filter(grepl("65|70|75|80|85|90|95", age_name) | age_name == "65岁以上标化") # 65岁及以上年龄组

cat("筛选后数据行数:", nrow(stroke_data), "\n")

if(nrow(stroke_data) > 0) {
  cat("可用年龄组:\n")
  print(unique(stroke_data$age_name))
  
  cat("\n可用年份:\n")
  print(sort(unique(stroke_data$year)))
  
  cat("\n部分国家列表:\n")
  countries <- unique(stroke_data$location_name)
  print(countries[1:min(10, length(countries))])
}

# 保存探索结果
write.csv(stroke_data, "stroke_mortality_65plus_sample.csv", row.names = FALSE)
cat("\n样本数据已保存到: stroke_mortality_65plus_sample.csv\n")
