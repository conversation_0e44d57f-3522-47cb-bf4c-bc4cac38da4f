# 脑卒中地图 - 强制显示完整图例的解决方案
library(ggplot2)
library(dplyr)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(countrycode)
library(gridExtra)
library(grid)

# 读取数据
data <- read.csv("脑卒中65岁以上人群患病率分析表.csv", 
                 stringsAsFactors = FALSE, 
                 fileEncoding = "UTF-8")

# 提取地区AAPC数据
region_aapc <- data %>%
  filter(分类 %in% c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", 
                    "高收入", "中欧、东欧和中亚", "拉丁美洲和加勒比海", 
                    "北非和中东")) %>%
  select(分类, AAPC = `AAPC...`)

# 创建国家到地区映射（简化版，包含主要国家）
country_region_mapping <- data.frame(
  country = c(
    # 高收入国家
    "United States", "Germany", "United Kingdom", "France", "Italy", 
    "Canada", "Spain", "Netherlands", "Belgium", "Switzerland", "Austria", 
    "Sweden", "Norway", "Denmark", "Finland", "Ireland", "Portugal", 
    "Greece", "Israel", "Luxembourg", "Iceland", "Malta", "Cyprus",
    "Australia", "New Zealand", "Japan", "South Korea",
    
    # 撒哈拉以南非洲
    "Nigeria", "Ethiopia", "South Africa", "Kenya", "Uganda", "Tanzania", 
    "Ghana", "Mozambique", "Madagascar", "Cameroon", "Angola", "Niger", 
    "Burkina Faso", "Mali", "Malawi", "Zambia", "Somalia", "Senegal", 
    "Chad", "Zimbabwe", "Guinea", "Rwanda", "Benin", "Burundi",
    
    # 东南亚、东亚和大洋洲
    "China", "Indonesia", "Philippines", "Vietnam", "Thailand", 
    "Myanmar", "Malaysia", "Cambodia", "Laos", "Singapore", "Mongolia",
    
    # 南亚
    "India", "Pakistan", "Bangladesh", "Afghanistan", "Nepal", "Sri Lanka", 
    "Bhutan", "Maldives",
    
    # 中欧、东欧和中亚
    "Russia", "Poland", "Ukraine", "Romania", "Czech Republic", "Hungary", 
    "Belarus", "Bulgaria", "Serbia", "Slovakia", "Croatia", 
    "Bosnia and Herzegovina", "Albania", "Lithuania", "Slovenia", 
    "Latvia", "Estonia", "North Macedonia", "Moldova", "Montenegro",
    
    # 拉丁美洲和加勒比海
    "Brazil", "Mexico", "Colombia", "Argentina", "Peru", "Venezuela", 
    "Chile", "Ecuador", "Guatemala", "Cuba", "Bolivia", "Haiti", 
    "Dominican Republic", "Honduras", "Paraguay", "Nicaragua", 
    "El Salvador", "Costa Rica", "Panama", "Uruguay", "Jamaica",
    
    # 北非和中东
    "Egypt", "Iran", "Turkey", "Iraq", "Saudi Arabia", "Yemen", "Syria", 
    "Jordan", "Lebanon", "United Arab Emirates", "Oman", "Kuwait", "Qatar", 
    "Bahrain", "Algeria", "Morocco", "Sudan", "Tunisia", "Libya"
  ),
  region = c(
    rep("高收入", 27),
    rep("撒哈拉以南非洲", 24),
    rep("东南亚、东亚和大洋洲", 11),
    rep("南亚", 8),
    rep("中欧、东欧和中亚", 20),
    rep("拉丁美洲和加勒比海", 20),
    rep("北非和中东", 19)
  )
)

# 合并数据
country_data <- country_region_mapping %>%
  left_join(region_aapc, by = c("region" = "分类"))

# 获取世界地图数据
world <- ne_countries(scale = "medium", returnclass = "sf")

# 标准化国家名称
world$name_clean <- countrycode(world$name, "country.name", "country.name")
country_data$country_clean <- countrycode(country_data$country, 
                                         "country.name", "country.name")

# 合并地图数据和AAPC数据
world_data <- world %>%
  left_join(country_data, by = c("name_clean" = "country_clean"))

# 定义所有分组级别（确保涵盖实际数据范围）
all_levels <- c("< -0.8", "-0.8 to -0.6", "-0.6 to -0.4", "-0.4 to -0.2", 
               "-0.2 to 0", "0 to 0.2", "0.2 to 0.4", "0.4 to 0.6", 
               "0.6 to 0.8", "0.8 to 1.0", "> 1.0", "No data")

# 创建AAPC分组
world_data <- world_data %>%
  mutate(
    aapc_group = case_when(
      is.na(AAPC) ~ "No data",
      AAPC < -0.8 ~ "< -0.8",
      AAPC >= -0.8 & AAPC < -0.6 ~ "-0.8 to -0.6",
      AAPC >= -0.6 & AAPC < -0.4 ~ "-0.6 to -0.4",
      AAPC >= -0.4 & AAPC < -0.2 ~ "-0.4 to -0.2",
      AAPC >= -0.2 & AAPC < 0 ~ "-0.2 to 0",
      AAPC >= 0 & AAPC < 0.2 ~ "0 to 0.2",
      AAPC >= 0.2 & AAPC < 0.4 ~ "0.2 to 0.4",
      AAPC >= 0.4 & AAPC < 0.6 ~ "0.4 to 0.6",
      AAPC >= 0.6 & AAPC < 0.8 ~ "0.6 to 0.8",
      AAPC >= 0.8 & AAPC < 1.0 ~ "0.8 to 1.0",
      AAPC >= 1.0 ~ "> 1.0"
    ),
    aapc_group = factor(aapc_group, levels = all_levels)
  )

# 创建虚拟数据点以确保所有分组都出现在图例中
dummy_data <- data.frame(
  geometry = st_sfc(st_point(c(0, 0))),  # 虚拟点
  aapc_group = factor(all_levels, levels = all_levels)
) %>%
  st_sf()

# 将虚拟数据添加到主数据中（但不会在地图上显示，因为是点数据）
world_data_with_dummy <- rbind(
  world_data %>% select(geometry, aapc_group),
  dummy_data
)

# 创建颜色调色板
colors <- c("#08306b", "#08519c", "#2171b5", "#4292c6", "#6baed6", "#c6dbef",
           "#fee0d2", "#fcbba1", "#fc9272", "#fb6a4a", "#de2d26", "#cccccc")
names(colors) <- all_levels

# 创建主地图
main_map <- ggplot() +
  # 先绘制包含虚拟数据的完整数据（用于生成完整图例）
  geom_sf(data = world_data_with_dummy, aes(fill = aapc_group), 
          color = NA, alpha = 0) +  # 透明，不显示
  # 再绘制实际的国家边界数据
  geom_sf(data = world_data, aes(fill = aapc_group), 
          color = "white", linewidth = 0.1) +
  scale_fill_manual(values = colors, name = "AAPC (%)", 
                   drop = FALSE, na.value = "#cccccc",
                   guide = guide_legend(
                     nrow = 3, 
                     byrow = TRUE,
                     override.aes = list(color = "black", linewidth = 0.5, alpha = 1)
                   )) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 14, face = "bold"),
    legend.text = element_text(size = 10),
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 13, hjust = 0.5),
    legend.key.size = unit(0.8, "cm"),
    legend.key.width = unit(1.0, "cm"),
    plot.margin = margin(10, 10, 10, 10),
    legend.margin = margin(t = 10)
  ) +
  labs(
    title = "Average Annual Percentage Change in Stroke Mortality",
    subtitle = "Among people aged ≥65 years, 1990-2019"
  )

# 保存地图
ggsave("stroke_complete_legend_main_map.png", main_map, 
       width = 18, height = 12, dpi = 300, bg = "white")

print("完整图例主地图已保存为: stroke_complete_legend_main_map.png")
print(main_map)

# 打印分组统计
cat("\n完整图例版本AAPC分组统计:\n")
print(table(world_data$aapc_group, useNA = "ifany"))

cat("\n图例说明 - 所有12个分组都会显示:\n")
for(i in 1:length(all_levels)) {
  cat(sprintf("%d. %s\n", i, all_levels[i]))
}
