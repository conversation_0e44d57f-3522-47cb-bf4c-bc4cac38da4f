# 基于真实数据的脑卒中DALYs可视化分析

library(ggplot2)
library(dplyr)
library(tidyr)
library(RColorBrewer)
library(gridExtra)

# 1. 读取真实数据结果
load_real_results <- function() {
  cat("读取真实数据分析结果...\n")
  
  # 读取详细结果
  aapc_results <- read.csv("stroke_real_dalys_detailed_results.csv", stringsAsFactors = FALSE)
  
  # 读取汇总统计
  global_summary <- read.csv("stroke_real_global_summary.csv", stringsAsFactors = FALSE)
  sdi_summary <- read.csv("stroke_real_sdi_summary.csv", stringsAsFactors = FALSE)
  risk_summary <- read.csv("stroke_real_risk_summary.csv", stringsAsFactors = FALSE)
  
  cat("数据读取完成\n")
  cat("- AAPC结果:", nrow(aapc_results), "行\n")
  cat("- 全球汇总:", nrow(global_summary), "行\n")
  
  return(list(
    aapc_results = aapc_results,
    global_summary = global_summary,
    sdi_summary = sdi_summary,
    risk_summary = risk_summary
  ))
}

# 2. 创建真实数据AAPC热力图
create_real_heatmap <- function(aapc_results) {
  cat("创建真实数据AAPC热力图...\n")
  
  # 准备热力图数据
  heatmap_data <- aapc_results %>%
    filter(!is.na(aapc), !is.infinite(aapc)) %>%
    mutate(
      risk_factor_short = case_when(
        grepl("吸烟", risk_factor_standard) ~ "吸烟",
        grepl("饮酒", risk_factor_standard) ~ "饮酒",
        grepl("高血压", risk_factor_standard) ~ "高血压",
        grepl("高血糖", risk_factor_standard) ~ "高血糖",
        grepl("饮食", risk_factor_standard) ~ "不健康饮食",
        grepl("身体活动", risk_factor_standard) ~ "身体活动不足",
        grepl("BMI", risk_factor_standard) ~ "高BMI",
        grepl("温度", risk_factor_standard) ~ "温度暴露",
        grepl("胆固醇", risk_factor_standard) ~ "高胆固醇",
        grepl("空气污染", risk_factor_standard) ~ "空气污染",
        grepl("环境", risk_factor_standard) ~ "其他环境因素",
        TRUE ~ risk_factor_standard
      )
    )
  
  # 创建热力图
  p <- ggplot(heatmap_data, aes(x = sdi_level_standard, y = risk_factor_short, fill = aapc)) +
    geom_tile(color = "white", size = 0.5) +
    scale_fill_gradient2(
      low = "#2166ac", mid = "white", high = "#d73027",
      midpoint = 0,
      name = "AAPC (%)",
      breaks = seq(-4, 4, 1),
      labels = seq(-4, 4, 1),
      limits = c(-4, 4)
    ) +
    geom_text(aes(label = round(aapc, 1)), size = 3, color = "black") +
    labs(
      title = "脑卒中相关DALYs年均变化率 (AAPC) - 真实数据",
      subtitle = "1990-2019年，≥65岁人群，按危险因素和SDI分层",
      x = "社会人口发展指数 (SDI) 分层",
      y = "危险因素"
    ) +
    theme_minimal() +
    theme(
      axis.text.x = element_text(angle = 45, hjust = 1, size = 10),
      axis.text.y = element_text(size = 10),
      plot.title = element_text(size = 14, face = "bold"),
      plot.subtitle = element_text(size = 12),
      legend.position = "right",
      panel.grid = element_blank()
    ) +
    scale_x_discrete(limits = c("Global", "High SDI", "High-middle SDI", "Middle SDI", "Low-middle SDI", "Low SDI"))
  
  return(p)
}

# 3. 创建全球AAPC条形图（真实数据）
create_real_global_barplot <- function(global_summary) {
  cat("创建全球AAPC条形图（真实数据）...\n")
  
  # 准备数据
  plot_data <- global_summary %>%
    filter(!is.na(aapc), !is.infinite(aapc)) %>%
    mutate(
      risk_factor_short = case_when(
        grepl("吸烟", risk_factor_standard) ~ "吸烟",
        grepl("饮酒", risk_factor_standard) ~ "饮酒",
        grepl("高血压", risk_factor_standard) ~ "高血压",
        grepl("高血糖", risk_factor_standard) ~ "高血糖",
        grepl("饮食", risk_factor_standard) ~ "不健康饮食",
        grepl("身体活动", risk_factor_standard) ~ "身体活动不足",
        grepl("BMI", risk_factor_standard) ~ "高BMI",
        grepl("温度", risk_factor_standard) ~ "温度暴露",
        grepl("胆固醇", risk_factor_standard) ~ "高胆固醇",
        grepl("空气污染", risk_factor_standard) ~ "空气污染",
        grepl("环境", risk_factor_standard) ~ "其他环境因素",
        TRUE ~ risk_factor_standard
      ),
      color_group = ifelse(aapc >= 0, "增加", "减少"),
      has_ci = !is.na(aapc_lower) & !is.na(aapc_upper) & !is.infinite(aapc_lower) & !is.infinite(aapc_upper)
    ) %>%
    arrange(aapc)
  
  # 创建条形图
  p <- ggplot(plot_data, aes(x = reorder(risk_factor_short, aapc), y = aapc, fill = color_group)) +
    geom_col(width = 0.7) +
    # 只为有有效置信区间的数据添加误差条
    geom_errorbar(
      data = filter(plot_data, has_ci),
      aes(ymin = aapc_lower, ymax = aapc_upper), 
      width = 0.2, alpha = 0.7
    ) +
    scale_fill_manual(values = c("增加" = "#d73027", "减少" = "#2166ac")) +
    geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.5) +
    labs(
      title = "全球脑卒中相关DALYs年均变化率 (AAPC) - 真实数据",
      subtitle = "1990-2019年，≥65岁人群，按危险因素分类",
      x = "危险因素",
      y = "AAPC (%)",
      fill = "变化趋势"
    ) +
    theme_minimal() +
    theme(
      axis.text.x = element_text(angle = 45, hjust = 1, size = 10),
      plot.title = element_text(size = 14, face = "bold"),
      plot.subtitle = element_text(size = 12),
      legend.position = "top"
    ) +
    coord_flip()
  
  return(p)
}

# 4. 创建SDI分层比较图（真实数据）
create_real_sdi_comparison <- function(aapc_results) {
  cat("创建SDI分层比较图（真实数据）...\n")
  
  # 选择主要危险因素
  major_risks <- c("吸烟 (Smoking)", "高血压 (High blood pressure)", 
                   "高血糖 (High fasting plasma glucose)", "空气污染 (Air pollution)")
  
  comparison_data <- aapc_results %>%
    filter(
      risk_factor_standard %in% major_risks,
      !is.na(aapc), !is.infinite(aapc)
    ) %>%
    mutate(
      risk_factor_short = case_when(
        grepl("吸烟", risk_factor_standard) ~ "吸烟",
        grepl("高血压", risk_factor_standard) ~ "高血压",
        grepl("高血糖", risk_factor_standard) ~ "高血糖",
        grepl("空气污染", risk_factor_standard) ~ "空气污染"
      ),
      has_ci = !is.na(aapc_lower) & !is.na(aapc_upper) & !is.infinite(aapc_lower) & !is.infinite(aapc_upper)
    )
  
  p <- ggplot(comparison_data, aes(x = sdi_level_standard, y = aapc, color = risk_factor_short, group = risk_factor_short)) +
    geom_line(size = 1.2, alpha = 0.8) +
    geom_point(size = 3) +
    # 只为有有效置信区间的数据添加置信带
    geom_ribbon(
      data = filter(comparison_data, has_ci),
      aes(ymin = aapc_lower, ymax = aapc_upper, fill = risk_factor_short), 
      alpha = 0.2, color = NA
    ) +
    scale_color_brewer(type = "qual", palette = "Set1") +
    scale_fill_brewer(type = "qual", palette = "Set1") +
    geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.5) +
    labs(
      title = "主要危险因素AAPC在不同SDI分层的变化 - 真实数据",
      subtitle = "脑卒中相关DALYs，1990-2019年，≥65岁人群",
      x = "社会人口发展指数 (SDI) 分层",
      y = "AAPC (%)",
      color = "危险因素",
      fill = "危险因素"
    ) +
    theme_minimal() +
    theme(
      axis.text.x = element_text(angle = 45, hjust = 1),
      plot.title = element_text(size = 14, face = "bold"),
      plot.subtitle = element_text(size = 12),
      legend.position = "bottom"
    ) +
    guides(color = guide_legend(title = "危险因素"), fill = guide_legend(title = "危险因素")) +
    scale_x_discrete(limits = c("Global", "High SDI", "High-middle SDI", "Middle SDI", "Low-middle SDI", "Low SDI"))
  
  return(p)
}

# 5. 创建DALYs绝对值比较图
create_dalys_comparison <- function(aapc_results) {
  cat("创建DALYs绝对值比较图...\n")
  
  # 准备数据
  dalys_data <- aapc_results %>%
    filter(sdi_level_standard == "Global") %>%
    select(risk_factor_standard, dalys_1990, dalys_2019) %>%
    pivot_longer(cols = c(dalys_1990, dalys_2019), names_to = "year", values_to = "dalys") %>%
    mutate(
      year = ifelse(year == "dalys_1990", "1990", "2019"),
      risk_factor_short = case_when(
        grepl("吸烟", risk_factor_standard) ~ "吸烟",
        grepl("饮酒", risk_factor_standard) ~ "饮酒",
        grepl("高血压", risk_factor_standard) ~ "高血压",
        grepl("高血糖", risk_factor_standard) ~ "高血糖",
        grepl("饮食", risk_factor_standard) ~ "不健康饮食",
        grepl("身体活动", risk_factor_standard) ~ "身体活动不足",
        grepl("BMI", risk_factor_standard) ~ "高BMI",
        grepl("温度", risk_factor_standard) ~ "温度暴露",
        grepl("胆固醇", risk_factor_standard) ~ "高胆固醇",
        grepl("空气污染", risk_factor_standard) ~ "空气污染",
        grepl("环境", risk_factor_standard) ~ "其他环境因素",
        TRUE ~ risk_factor_standard
      )
    )
  
  p <- ggplot(dalys_data, aes(x = reorder(risk_factor_short, dalys), y = dalys/1000, fill = year)) +
    geom_col(position = "dodge", width = 0.7) +
    scale_fill_manual(values = c("1990" = "#2166ac", "2019" = "#d73027")) +
    labs(
      title = "全球脑卒中相关DALYs绝对值比较 - 真实数据",
      subtitle = "1990年 vs 2019年，≥65岁人群（千人年）",
      x = "危险因素",
      y = "DALYs (千人年)",
      fill = "年份"
    ) +
    theme_minimal() +
    theme(
      axis.text.x = element_text(angle = 45, hjust = 1),
      plot.title = element_text(size = 14, face = "bold"),
      plot.subtitle = element_text(size = 12),
      legend.position = "top"
    ) +
    coord_flip()
  
  return(p)
}

# 6. 主可视化函数
main_real_visualization <- function() {
  cat("=== 开始创建真实数据可视化 ===\n")
  
  # 读取数据
  results <- load_real_results()
  
  # 创建图表
  heatmap <- create_real_heatmap(results$aapc_results)
  barplot <- create_real_global_barplot(results$global_summary)
  comparison <- create_real_sdi_comparison(results$aapc_results)
  dalys_plot <- create_dalys_comparison(results$aapc_results)
  
  # 保存图表
  ggsave("stroke_real_aapc_heatmap.png", heatmap, width = 14, height = 10, dpi = 300)
  ggsave("stroke_real_global_aapc_barplot.png", barplot, width = 12, height = 8, dpi = 300)
  ggsave("stroke_real_sdi_comparison.png", comparison, width = 14, height = 8, dpi = 300)
  ggsave("stroke_real_dalys_comparison.png", dalys_plot, width = 12, height = 8, dpi = 300)
  
  # 创建组合图
  combined_plot <- grid.arrange(heatmap, barplot, ncol = 1, heights = c(2, 1))
  ggsave("stroke_real_combined_analysis.png", combined_plot, width = 16, height = 14, dpi = 300)
  
  cat("=== 可视化完成！===\n")
  cat("生成的图表文件：\n")
  cat("- stroke_real_aapc_heatmap.png\n")
  cat("- stroke_real_global_aapc_barplot.png\n")
  cat("- stroke_real_sdi_comparison.png\n")
  cat("- stroke_real_dalys_comparison.png\n")
  cat("- stroke_real_combined_analysis.png\n")
  
  return(list(
    heatmap = heatmap,
    barplot = barplot,
    comparison = comparison,
    dalys_plot = dalys_plot
  ))
}

# 执行可视化
if (!interactive()) {
  plots <- main_real_visualization()
}
