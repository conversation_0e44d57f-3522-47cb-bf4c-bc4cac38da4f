# 脑卒中65岁以上人群发病率地图 - 包含六个局部放大视图
# 基于1990-2021年完整数据

library(ggplot2)
library(dplyr)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(RColorBrewer)
library(gridExtra)
library(grid)
library(scales)
# library(cowplot) # 使用gridExtra替代

cat("=== 创建带局部放大视图的脑卒中发病率地图 ===\n")

# 读取最终分析表
stroke_data <- read.csv("脑卒中65岁以上人群发病率最终分析表_1990_2021.csv", 
                       stringsAsFactors = FALSE, 
                       fileEncoding = "UTF-8")

# 提取地区AAPC数据
region_aapc <- stroke_data %>%
  filter(分类 %in% c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", 
                    "高收入", "中欧、东欧和中亚", "拉丁美洲和加勒比海", 
                    "北非和中东")) %>%
  select(
    地区 = 分类,
    AAPC = `AAPC...`
  )

# 获取世界地图
world <- ne_countries(scale = "medium", returnclass = "sf")

# 创建详细的国家映射（包含更多国家以支持局部放大）
create_detailed_country_mapping <- function(world_data) {
  country_names <- world_data$name_en
  
  mapping <- data.frame(
    country = country_names,
    region = case_when(
      # 高收入地区
      country_names %in% c("United States of America", "Canada", "Germany", "France", "United Kingdom", 
                          "Italy", "Spain", "Netherlands", "Belgium", "Switzerland", 
                          "Austria", "Sweden", "Norway", "Denmark", "Finland", "Ireland", 
                          "Portugal", "Greece", "Australia", "New Zealand", "Japan", 
                          "South Korea", "Israel", "Luxembourg", "Iceland", "Czech Republic",
                          "Slovenia", "Estonia", "Latvia", "Lithuania", "Slovakia", "Poland",
                          "Hungary", "Croatia", "Cyprus", "Malta") ~ "高收入",
      
      # 撒哈拉以南非洲
      country_names %in% c("Nigeria", "Ethiopia", "South Africa", "Kenya", "Uganda", "Tanzania", 
                          "Ghana", "Mozambique", "Madagascar", "Cameroon", "Angola", "Niger", 
                          "Burkina Faso", "Mali", "Malawi", "Zambia", "Somalia", "Senegal", 
                          "Chad", "Zimbabwe", "Guinea", "Rwanda", "Benin", "Burundi", 
                          "South Sudan", "Togo", "Sierra Leone", "Liberia", "Botswana", 
                          "Namibia", "Gabon", "Lesotho", "Gambia", "Guinea-Bissau", 
                          "Equatorial Guinea", "Mauritius", "Eswatini", "Djibouti", 
                          "Comoros", "Cape Verde", "São Tomé and Príncipe", "Seychelles",
                          "Central African Republic", "Republic of the Congo", 
                          "Democratic Republic of the Congo", "Ivory Coast", "Mauritania") ~ "撒哈拉以南非洲",
      
      # 东南亚、东亚和大洋洲
      country_names %in% c("China", "Indonesia", "Philippines", "Vietnam", "Thailand", 
                          "Myanmar", "Malaysia", "Cambodia", "Laos", "Singapore", 
                          "Brunei", "East Timor", "Papua New Guinea", "Fiji", 
                          "Solomon Islands", "Vanuatu", "Samoa", "Tonga", "Kiribati", 
                          "Tuvalu", "Nauru", "Palau", "Marshall Islands", 
                          "Federated States of Micronesia", "Mongolia", "North Korea") ~ "东南亚、东亚和大洋洲",
      
      # 南亚
      country_names %in% c("India", "Pakistan", "Bangladesh", "Afghanistan", "Nepal", 
                          "Sri Lanka", "Bhutan", "Maldives") ~ "南亚",
      
      # 中欧、东欧和中亚
      country_names %in% c("Russia", "Ukraine", "Belarus", "Moldova", "Romania", "Bulgaria", 
                          "Serbia", "Bosnia and Herzegovina", "Montenegro", "Albania", 
                          "North Macedonia", "Kosovo", "Kazakhstan", "Uzbekistan", 
                          "Turkmenistan", "Kyrgyzstan", "Tajikistan", "Armenia", 
                          "Azerbaijan", "Georgia") ~ "中欧、东欧和中亚",
      
      # 拉丁美洲和加勒比海
      country_names %in% c("Brazil", "Mexico", "Argentina", "Colombia", "Peru", "Venezuela", 
                          "Chile", "Ecuador", "Guatemala", "Cuba", "Bolivia", "Haiti", 
                          "Dominican Republic", "Honduras", "Paraguay", "Nicaragua", 
                          "Costa Rica", "Panama", "Uruguay", "Jamaica", "Trinidad and Tobago", 
                          "Bahamas", "Belize", "Barbados", "Saint Lucia", "Grenada", 
                          "Saint Vincent and the Grenadines", "Antigua and Barbuda", 
                          "Dominica", "Saint Kitts and Nevis", "Suriname", "Guyana",
                          "El Salvador") ~ "拉丁美洲和加勒比海",
      
      # 北非和中东
      country_names %in% c("Egypt", "Iran", "Turkey", "Iraq", "Saudi Arabia", "Yemen", 
                          "Syria", "Jordan", "Lebanon", "Libya", "Tunisia", "Algeria", 
                          "Morocco", "Sudan", "Oman", "Kuwait", "United Arab Emirates", 
                          "Qatar", "Bahrain") ~ "北非和中东",
      
      TRUE ~ "其他"
    ),
    stringsAsFactors = FALSE
  )
  
  return(mapping)
}

# 创建映射
country_mapping <- create_detailed_country_mapping(world)

# 合并地图数据和AAPC数据
world_with_aapc <- world %>%
  left_join(country_mapping, by = c("name_en" = "country")) %>%
  left_join(region_aapc, by = c("region" = "地区"))

# 创建颜色方案
aapc_range <- range(region_aapc$AAPC, na.rm = TRUE)
breaks <- seq(aapc_range[1], aapc_range[2], length.out = 11)
colors <- colorRampPalette(c("#2166AC", "#4393C3", "#92C5DE", "#D1E5F0", "#F7F7F7",
                            "#FDBF6F", "#FD8D3C", "#E31A1C", "#B10026"))(10)

# 创建基础地图主题
base_theme <- theme_void() +
  theme(
    legend.position = "none",
    plot.title = element_text(size = 8, face = "bold", hjust = 0.5),
    plot.margin = margin(2, 2, 2, 2)
  )

# 创建主地图
main_map <- ggplot(world_with_aapc) +
  geom_sf(aes(fill = AAPC), color = "white", size = 0.1) +
  scale_fill_gradientn(
    colors = colors,
    values = scales::rescale(breaks),
    name = "AAPC (%)",
    na.value = "grey90",
    breaks = pretty(aapc_range, n = 5),
    guide = guide_colorbar(
      title.position = "top",
      title.hjust = 0.5,
      barwidth = 15,
      barheight = 1
    )
  ) +
  theme_void() +
  theme(
    legend.position = "bottom",
    legend.title = element_text(size = 12, face = "bold"),
    legend.text = element_text(size = 10),
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 12, hjust = 0.5),
    plot.caption = element_text(size = 10, hjust = 0.5)
  ) +
  labs(
    title = "脑卒中65岁以上人群发病率变化趋势 (1990-2021)",
    subtitle = "年均百分比变化 (AAPC) 按地区分布",
    caption = "基于32年完整时间序列数据 | 数据来源: GBD 2021"
  )

cat("主地图创建完成\n")

# 定义六个局部放大区域的边界
regions <- list(
  caribbean = list(
    name = "Caribbean and Central America",
    xlim = c(-95, -55),
    ylim = c(5, 30)
  ),
  persian_gulf = list(
    name = "Persian Gulf",
    xlim = c(45, 60),
    ylim = c(22, 32)
  ),
  balkan = list(
    name = "Balkan Peninsula",
    xlim = c(12, 30),
    ylim = c(38, 48)
  ),
  southeast_asia = list(
    name = "South East Asia",
    xlim = c(90, 145),
    ylim = c(-15, 25)
  ),
  west_africa_med = list(
    name = "West Africa & Eastern Mediterranean",
    xlim = c(-20, 45),
    ylim = c(10, 40)
  ),
  northern_europe = list(
    name = "Northern Europe",
    xlim = c(-10, 35),
    ylim = c(50, 72)
  )
)

# 创建局部放大地图函数
create_zoom_map <- function(region_info) {
  ggplot(world_with_aapc) +
    geom_sf(aes(fill = AAPC), color = "white", size = 0.2) +
    scale_fill_gradientn(
      colors = colors,
      values = scales::rescale(breaks),
      na.value = "grey90",
      guide = "none"
    ) +
    coord_sf(
      xlim = region_info$xlim,
      ylim = region_info$ylim,
      expand = FALSE
    ) +
    base_theme +
    labs(title = region_info$name)
}

# 创建所有局部放大地图
zoom_maps <- lapply(regions, create_zoom_map)

cat("局部放大地图创建完成\n")

# 创建综合布局使用gridExtra
# 首先创建局部地图的网格
zoom_grid <- grid.arrange(
  zoom_maps$caribbean,
  zoom_maps$persian_gulf,
  zoom_maps$balkan,
  zoom_maps$southeast_asia,
  zoom_maps$west_africa_med,
  zoom_maps$northern_europe,
  ncol = 3,
  nrow = 2
)

# 保存主地图
ggsave("stroke_incidence_main_with_regions_1990_2021.png",
       main_map,
       width = 16, height = 10, dpi = 300, bg = "white")

# 保存局部放大地图网格
ggsave("stroke_incidence_zoom_regions_1990_2021.png",
       zoom_grid,
       width = 18, height = 12, dpi = 300, bg = "white")

cat("主地图已保存: stroke_incidence_main_with_regions_1990_2021.png\n")
cat("局部放大地图已保存: stroke_incidence_zoom_regions_1990_2021.png\n")

cat("=== 带局部放大视图的地图创建完成 ===\n")
cat("包含以下六个局部放大区域:\n")
cat("1. Caribbean and Central America (加勒比海和中美洲)\n")
cat("2. Persian Gulf (波斯湾地区)\n")
cat("3. Balkan Peninsula (巴尔干半岛)\n")
cat("4. South East Asia (东南亚)\n")
cat("5. West Africa & Eastern Mediterranean (西非和东地中海)\n")
cat("6. Northern Europe (北欧)\n")
