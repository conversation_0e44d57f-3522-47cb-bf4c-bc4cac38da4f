# 创建更新的脑卒中65岁以上人群发病率分析表
# 使用1990-2021年完整数据计算AAPC

library(dplyr)
library(readr)

cat("=== 创建更新的脑卒中发病率分析表 ===\n")

# 读取全球汇总数据
global_data <- read.csv("stroke_incidence_global_summary_1990_2021.csv", 
                       stringsAsFactors = FALSE)

# 读取完整的国家数据
country_data <- read.csv("stroke_incidence_65plus_1990_2021.csv", 
                        stringsAsFactors = FALSE)

# 计算AAPC的函数
calculate_aapc <- function(data, value_col = "global_rate") {
  if(nrow(data) < 2) return(NA)

  # 使用对数线性回归计算AAPC
  years <- data$year
  values <- data[[value_col]]

  # 过滤掉NA值
  valid_idx <- !is.na(values) & values > 0
  if(sum(valid_idx) < 2) return(NA)

  years <- years[valid_idx]
  values <- values[valid_idx]

  # 对数线性回归
  log_values <- log(values)
  model <- lm(log_values ~ years)
  slope <- coef(model)[2]

  # 计算AAPC
  aapc <- (exp(slope) - 1) * 100

  return(aapc)
}

# 计算组内AAPC的函数
calculate_group_aapc <- function(group_data, value_col = "global_rate") {
  return(calculate_aapc(group_data, value_col))
}

# 计算全球各组的AAPC
cat("计算全球各组AAPC...\n")

# 全球总体（合计性别）
global_total <- global_data %>%
  filter(sex_name == "合计") %>%
  group_by(age_name) %>%
  do({
    group_data <- .
    data.frame(
      rate_1990 = group_data$global_rate[group_data$year == 1990],
      rate_2019 = group_data$global_rate[group_data$year == 2019],
      rate_2020 = group_data$global_rate[group_data$year == 2020],
      rate_2021 = group_data$global_rate[group_data$year == 2021],
      aapc_full = calculate_aapc(group_data),
      aapc_recent = calculate_aapc(group_data[group_data$year >= 2019, ])
    )
  })

# 全球总体（所有年龄组合计）
global_overall <- global_data %>%
  filter(sex_name == "合计") %>%
  group_by(year) %>%
  summarise(
    global_rate = mean(global_rate, na.rm = TRUE),
    .groups = 'drop'
  ) %>%
  summarise(
    rate_1990 = global_rate[year == 1990],
    rate_2021 = global_rate[year == 2021],
    aapc = calculate_aapc(cur_data())
  )

# 性别分组
global_sex <- global_data %>%
  filter(sex_name %in% c("男", "女")) %>%
  group_by(sex_name, year) %>%
  summarise(
    global_rate = mean(global_rate, na.rm = TRUE),
    .groups = "drop"
  ) %>%
  group_by(sex_name) %>%
  do({
    group_data <- .
    data.frame(
      rate_1990 = group_data$global_rate[group_data$year == 1990],
      rate_2021 = group_data$global_rate[group_data$year == 2021],
      aapc = calculate_aapc(group_data)
    )
  })

# 创建分析表
cat("创建分析表...\n")

analysis_table <- data.frame(
  分类 = character(),
  "1990年年龄标准化率(每10万人)" = numeric(),
  "2021年年龄标准化率(每10万人)" = numeric(),
  "AAPC(%)" = numeric(),
  stringsAsFactors = FALSE
)

# 添加全球总体数据
analysis_table <- rbind(analysis_table, data.frame(
  分类 = "全球",
  "X1990年年龄标准化率.每10万人." = round(global_overall$rate_1990, 2),
  "X2021年年龄标准化率.每10万人." = round(global_overall$rate_2021, 2),
  "AAPC..." = round(global_overall$aapc, 2)
))

# 添加性别分组
analysis_table <- rbind(analysis_table, data.frame(
  分类 = "性别",
  "X1990年年龄标准化率.每10万人." = NA,
  "X2021年年龄标准化率.每10万人." = NA,
  "AAPC..." = NA
))

for(i in 1:nrow(global_sex)) {
  sex_name <- ifelse(global_sex$sex_name[i] == "男", "男性", "女性")
  analysis_table <- rbind(analysis_table, data.frame(
    分类 = sex_name,
    "X1990年年龄标准化率.每10万人." = round(global_sex$rate_1990[i], 2),
    "X2021年年龄标准化率.每10万人." = round(global_sex$rate_2021[i], 2),
    "AAPC..." = round(global_sex$aapc[i], 2)
  ))
}

# 添加年龄组
analysis_table <- rbind(analysis_table, data.frame(
  分类 = "年龄组(岁)",
  "X1990年年龄标准化率.每10万人." = NA,
  "X2021年年龄标准化率.每10万人." = NA,
  "AAPC..." = NA
))

for(i in 1:nrow(global_total)) {
  analysis_table <- rbind(analysis_table, data.frame(
    分类 = global_total$age_name[i],
    "X1990年年龄标准化率.每10万人." = round(global_total$rate_1990[i], 2),
    "X2021年年龄标准化率.每10万人." = round(global_total$rate_2021[i], 2),
    "AAPC..." = round(global_total$aapc[i], 2)
  ))
}

# 修正列名
names(analysis_table) <- c("分类", "1990年年龄标准化率(每10万人)", 
                          "2021年年龄标准化率(每10万人)", "AAPC(%)")

# 保存更新的分析表
write.csv(analysis_table, "脑卒中65岁以上人群发病率分析表_更新版.csv", 
          row.names = FALSE, fileEncoding = "UTF-8")

cat("更新的分析表已保存到: 脑卒中65岁以上人群发病率分析表_更新版.csv\n")

# 显示分析表
cat("=== 更新的分析表内容 ===\n")
print(analysis_table)

# 添加地区数据
cat("计算地区AAPC...\n")

# 读取GBD super region数据
super_region_data <- read.csv("死亡损伤-数据库/GBD super region/IHME-GBD_2021_DATA-26cc51e1-1.csv",
                             stringsAsFactors = FALSE)

# 筛选发病率数据（如果有的话）
# 注意：这个文件可能只包含死亡数据，我们需要检查
if("发病率" %in% unique(super_region_data$measure_name)) {
  region_incidence <- super_region_data %>%
    filter(
      cause_name == "脑卒中",
      measure_name == "发病率",
      age_name %in% c("65-69岁", "70-74岁", "75-79岁", "80-84岁", "85岁以上"),
      metric_name == "率",
      sex_name == "合计"
    ) %>%
    group_by(location_name, year) %>%
    summarise(
      avg_rate = mean(val, na.rm = TRUE),
      .groups = 'drop'
    ) %>%
    group_by(location_name) %>%
    do({
      group_data <- .
      data.frame(
        rate_1990 = group_data$avg_rate[group_data$year == 1990],
        rate_2021 = group_data$avg_rate[group_data$year == 2021],
        aapc = calculate_aapc(group_data, "avg_rate")
      )
    })

  # 添加地区数据到分析表
  region_header <- data.frame(
    分类 = "地区",
    stringsAsFactors = FALSE
  )
  region_header[["1990年年龄标准化率(每10万人)"]] <- NA
  region_header[["2021年年龄标准化率(每10万人)"]] <- NA
  region_header[["AAPC(%)"]] <- NA

  analysis_table <- rbind(analysis_table, region_header)

  for(i in 1:nrow(region_incidence)) {
    region_row <- data.frame(
      分类 = region_incidence$location_name[i],
      stringsAsFactors = FALSE
    )
    region_row[["1990年年龄标准化率(每10万人)"]] <- round(region_incidence$rate_1990[i], 2)
    region_row[["2021年年龄标准化率(每10万人)"]] <- round(region_incidence$rate_2021[i], 2)
    region_row[["AAPC(%)"]] <- round(region_incidence$aapc[i], 2)

    analysis_table <- rbind(analysis_table, region_row)
  }

  # 重新保存更新的分析表
  write.csv(analysis_table, "脑卒中65岁以上人群发病率分析表_更新版.csv",
            row.names = FALSE, fileEncoding = "UTF-8")

  cat("地区数据已添加到分析表\n")
} else {
  cat("Super region数据中未找到发病率数据，跳过地区分析\n")
}

cat("=== 分析表创建完成 ===\n")
