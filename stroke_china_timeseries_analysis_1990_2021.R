# 中国35岁以上人群缺血性脑卒中年龄标准化患病率及AAPC分析 (1990-2021)
# 使用完整时间序列数据进行分析

library(readr)
library(dplyr)
library(tidyr)

cat("=== 创建中国35岁以上人群缺血性脑卒中患病率时间序列分析 ===\n")

# 读取中国数据文件
china_files <- list.files("死亡损伤-数据库/204国家", pattern = "IHME-GBD_2021_DATA.*\\.csv$", 
                         recursive = TRUE, full.names = TRUE)

# 改进的AAPC计算函数 - 使用完整时间序列
calculate_aapc_timeseries <- function(data, value_col = "val") {
  if(nrow(data) < 2) return(list(aapc = NA, r_squared = NA, p_value = NA))

  # 确保数据按年份排序
  data <- data %>% arrange(year)

  # 移除缺失值和零值
  data <- data %>%
    filter(!is.na(!!sym(value_col)), !!sym(value_col) > 0)

  if(nrow(data) < 2) return(list(aapc = NA, r_squared = NA, p_value = NA))

  # 对数线性回归
  tryCatch({
    # 添加调试信息
    cat("计算AAPC - 数据行数:", nrow(data), "\n")
    cat("年份范围:", min(data$year), "-", max(data$year), "\n")
    cat("数值范围:", min(data[[value_col]], na.rm = TRUE), "-", max(data[[value_col]], na.rm = TRUE), "\n")

    model <- lm(log(data[[value_col]]) ~ data$year)
    slope <- coef(model)[2]
    aapc <- (exp(slope) - 1) * 100

    # 计算统计指标
    summary_model <- summary(model)
    r_squared <- summary_model$r.squared
    p_value <- summary_model$coefficients[2, 4]  # year的p值

    # 计算置信区间
    conf_int <- confint(model, "data$year", level = 0.95)
    aapc_lower <- (exp(conf_int[1]) - 1) * 100
    aapc_upper <- (exp(conf_int[2]) - 1) * 100

    cat("AAPC计算成功:", aapc, "\n")

    return(list(
      aapc = aapc,
      aapc_lower = aapc_lower,
      aapc_upper = aapc_upper,
      r_squared = r_squared,
      p_value = p_value,
      n_years = nrow(data)
    ))
  }, error = function(e) {
    cat("AAPC计算错误:", e$message, "\n")
    return(list(aapc = NA, aapc_lower = NA, aapc_upper = NA,
                r_squared = NA, p_value = NA, n_years = nrow(data)))
  })
}

# 读取和处理中国数据
cat("正在处理中国完整时间序列数据...\n")
china_data <- data.frame()

for(file in china_files) {
  temp_data <- read_csv(file, show_col_types = FALSE)
  
  # 筛选中国的脑卒中死亡率数据（1990-2021年完整时间序列）
  filtered_data <- temp_data %>%
    filter(location_name == "中国",  # 中国
           cause_id == 494,         # 脑卒中
           measure_id == 1,         # 死亡率
           metric_id == 3,          # 率
           year >= 1990, year <= 2021) %>%
    select(location_name, sex_id, sex_name, age_id, age_name, year, val, upper, lower)
  
  china_data <- rbind(china_data, filtered_data)
}

cat("中国完整时间序列数据行数:", nrow(china_data), "\n")
cat("时间范围:", min(china_data$year, na.rm = TRUE), "-", max(china_data$year, na.rm = TRUE), "\n")

# 定义35岁以上年龄组
age_groups_35plus <- c(12, 13, 14, 15, 16, 17, 18, 19, 20, 30, 31, 32, 235)
age_names <- c("35-39", "40-44", "45-49", "50-54", "55-59", "60-64", 
               "65-69", "70-74", "75-79", "80-84", "85-89", "90-94", "≥95")

# 筛选35岁以上数据
china_35plus <- china_data %>%
  filter(age_id %in% age_groups_35plus)

cat("35岁以上完整时间序列数据行数:", nrow(china_35plus), "\n")

if(nrow(china_35plus) > 0) {
  # 创建最终表格
  final_table <- data.frame()
  
  # 1. 中国总体数据（35岁以上合计）
  cat("计算中国总体数据...\n")
  total_data <- china_35plus %>%
    filter(sex_id == 3) %>%  # 合计性别
    group_by(year) %>%
    summarise(
      avg_rate = mean(val, na.rm = TRUE),
      avg_lower = mean(lower, na.rm = TRUE),
      avg_upper = mean(upper, na.rm = TRUE),
      .groups = 'drop'
    )
  
  # 获取1990年和2021年的数据用于显示
  total_1990 <- total_data %>% filter(year == 1990)
  total_2021 <- total_data %>% filter(year == 2021)
  
  # 检查数据
  cat("总体数据行数:", nrow(total_data), "\n")
  cat("总体数据年份范围:", min(total_data$year), "-", max(total_data$year), "\n")
  cat("总体数据样本:\n")
  print(head(total_data))

  # 计算时间序列AAPC
  total_aapc <- calculate_aapc_timeseries(total_data, "avg_rate")
  cat("总体AAPC结果:\n")
  print(total_aapc)
  
  if(nrow(total_1990) > 0 && nrow(total_2021) > 0) {
    final_table <- rbind(final_table, data.frame(
      Category = "中国总体",
      Prevalence_1990_count = "N/A",
      Prevalence_1990_rate = sprintf("%.1f (%.1f to %.1f)", 
                                    total_1990$avg_rate, total_1990$avg_lower, total_1990$avg_upper),
      Prevalence_2021_count = "N/A",
      Prevalence_2021_rate = sprintf("%.1f (%.1f to %.1f)",
                                    total_2021$avg_rate, total_2021$avg_lower, total_2021$avg_upper),
      AAPC = sprintf("%.2f (%.2f to %.2f)", 
                     total_aapc$aapc, total_aapc$aapc_lower, total_aapc$aapc_upper),
      R_squared = sprintf("%.3f", total_aapc$r_squared),
      P_value = sprintf("%.4f", total_aapc$p_value),
      N_years = total_aapc$n_years
    ))
  }
  
  # 2. 性别分层数据
  cat("计算性别分层数据...\n")
  for(sex in c("女", "男")) {
    sex_data <- china_35plus %>%
      filter(sex_name == sex) %>%
      group_by(year) %>%
      summarise(
        avg_rate = mean(val, na.rm = TRUE),
        avg_lower = mean(lower, na.rm = TRUE),
        avg_upper = mean(upper, na.rm = TRUE),
        .groups = 'drop'
      )
    
    sex_1990 <- sex_data %>% filter(year == 1990)
    sex_2021 <- sex_data %>% filter(year == 2021)
    sex_aapc <- calculate_aapc_timeseries(sex_data, "avg_rate")
    
    if(nrow(sex_1990) > 0 && nrow(sex_2021) > 0) {
      final_table <- rbind(final_table, data.frame(
        Category = sex,
        Prevalence_1990_count = "N/A",
        Prevalence_1990_rate = sprintf("%.1f (%.1f to %.1f)", 
                                      sex_1990$avg_rate, sex_1990$avg_lower, sex_1990$avg_upper),
        Prevalence_2021_count = "N/A",
        Prevalence_2021_rate = sprintf("%.1f (%.1f to %.1f)",
                                      sex_2021$avg_rate, sex_2021$avg_lower, sex_2021$avg_upper),
        AAPC = sprintf("%.2f (%.2f to %.2f)", 
                       sex_aapc$aapc, sex_aapc$aapc_lower, sex_aapc$aapc_upper),
        R_squared = sprintf("%.3f", sex_aapc$r_squared),
        P_value = sprintf("%.4f", sex_aapc$p_value),
        N_years = sex_aapc$n_years
      ))
    }
  }
  
  # 3. 年龄分组数据
  cat("计算年龄分组数据...\n")
  for(i in 1:length(age_groups_35plus)) {
    age_id <- age_groups_35plus[i]
    age_name <- age_names[i]
    
    age_data <- china_35plus %>%
      filter(age_id == !!age_id, sex_id == 3) %>%  # 合计性别
      select(year, val, lower, upper) %>%
      arrange(year)
    
    if(nrow(age_data) >= 2) {
      age_1990 <- age_data %>% filter(year == 1990)
      age_2021 <- age_data %>% filter(year == 2021)
      age_aapc <- calculate_aapc_timeseries(age_data, "val")
      
      if(nrow(age_1990) > 0 && nrow(age_2021) > 0) {
        final_table <- rbind(final_table, data.frame(
          Category = age_name,
          Prevalence_1990_count = "N/A",
          Prevalence_1990_rate = sprintf("%.1f (%.1f to %.1f)", 
                                        age_1990$val[1], age_1990$lower[1], age_1990$upper[1]),
          Prevalence_2021_count = "N/A",
          Prevalence_2021_rate = sprintf("%.1f (%.1f to %.1f)",
                                        age_2021$val[1], age_2021$lower[1], age_2021$upper[1]),
          AAPC = sprintf("%.2f (%.2f to %.2f)", 
                         age_aapc$aapc, age_aapc$aapc_lower, age_aapc$aapc_upper),
          R_squared = sprintf("%.3f", age_aapc$r_squared),
          P_value = sprintf("%.4f", age_aapc$p_value),
          N_years = age_aapc$n_years
        ))
      }
    }
  }
  
  # 4. SDI分组（中国属于中高SDI）
  final_table <- rbind(final_table, data.frame(
    Category = "中高SDI",
    Prevalence_1990_count = "N/A",
    Prevalence_1990_rate = sprintf("%.1f (%.1f to %.1f)", 
                                  total_1990$avg_rate, total_1990$avg_lower, total_1990$avg_upper),
    Prevalence_2021_count = "N/A",
    Prevalence_2021_rate = sprintf("%.1f (%.1f to %.1f)",
                                  total_2021$avg_rate, total_2021$avg_lower, total_2021$avg_upper),
    AAPC = sprintf("%.2f (%.2f to %.2f)", 
                   total_aapc$aapc, total_aapc$aapc_lower, total_aapc$aapc_upper),
    R_squared = sprintf("%.3f", total_aapc$r_squared),
    P_value = sprintf("%.4f", total_aapc$p_value),
    N_years = total_aapc$n_years
  ))
  
  # 添加表头
  header_row <- data.frame(
    Category = "Table 1 | 35岁及以上人群中缺血性脑卒中年龄标准化患病率及AAPC，中国区域层面，1990-2021年（完整时间序列分析）",
    Prevalence_1990_count = "1990年患者人数（千人）",
    Prevalence_1990_rate = "1990年年龄标准化患病率（每10万人）", 
    Prevalence_2021_count = "2021年患者人数（千人）",
    Prevalence_2021_rate = "2021年年龄标准化患病率（每10万人）",
    AAPC = "AAPC（95% CI）",
    R_squared = "R²",
    P_value = "P值",
    N_years = "年数"
  )
  
  # 合并表头和数据
  final_table_with_header <- rbind(header_row, final_table)
  
  # 保存CSV文件
  write_csv(final_table_with_header, "stroke_china_timeseries_analysis_1990_2021.csv")
  cat("时间序列分析表格已保存到: stroke_china_timeseries_analysis_1990_2021.csv\n")
  
  # 显示完整表格
  cat("\n完整时间序列分析表格:\n")
  print(final_table_with_header)
  
} else {
  cat("错误：未找到中国的35岁以上脑卒中时间序列数据\n")
}

cat("\n=== 时间序列分析完成 ===\n")

# 添加备注说明
cat("\n备注说明:\n")
cat("AAPC=average annual percentage change; CI=confidence interval; SDI=sociodemographic index;\n")
cat("R²=决定系数，表示模型拟合优度; P值=统计显著性检验结果;\n")
cat("本分析使用1990-2021年完整时间序列数据进行对数线性回归计算AAPC。\n")
cat("注：由于数据库限制，此处使用脑卒中死亡率数据作为患病率的代理指标。\n")
