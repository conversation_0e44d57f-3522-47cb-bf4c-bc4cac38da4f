# 复现Fig 3: 全球65岁及以上脑卒中患者患病率年均变化率地图
# 基于1990-2019年数据的AAPC分析 - 脑卒中版本

# ==================== 1. 研究目的 ====================
# 展示1990-2019年，全球各国（地区）65岁及以上脑卒中患者
# 患病率年均百分比变化（AAPC）的空间分布，参考Fig 3的设计风格

# ==================== 2. 加载必要包 ====================
library(ggplot2)
library(dplyr)
library(sf)
library(rnaturalearth)
library(rnaturalearthdata)
library(RColorBrewer)
library(countrycode)
library(gridExtra)
library(grid)
library(scales)
library(viridis)

# ==================== 3. 数据准备 ====================

# 3.1 读取脑卒中数据
cat("=== 数据准备阶段 ===\n")
stroke_data <- read.csv("脑卒中65岁以上人群患病率分析表.csv", 
                       stringsAsFactors = FALSE, 
                       fileEncoding = "UTF-8")

# 3.2 提取地区AAPC数据
region_aapc <- stroke_data %>%
  filter(分类 %in% c("撒哈拉以南非洲", "东南亚、东亚和大洋洲", "南亚", 
                    "高收入", "中欧、东欧和中亚", "拉丁美洲和加勒比海", 
                    "北非和中东")) %>%
  select(
    地区 = 分类,
    AAPC = `AAPC...`,
    AAPC_lower = `AAPC.95.CI下限`,
    AAPC_upper = `AAPC.95.CI上限`
  )

cat("地区AAPC数据:\n")
print(region_aapc)

# ==================== 4. 创建详细的国家映射 ====================

# 4.1 创建基于实际地图数据的国家映射
create_country_mapping <- function(world_data) {

  # 使用正确的字段名
  country_name_field <- if("name_en" %in% names(world_data)) {
    "name_en"
  } else if("admin" %in% names(world_data)) {
    "admin"
  } else {
    "name"
  }

  country_names <- world_data[[country_name_field]]

  # 根据地理位置和经济发展水平进行分组
  mapping <- data.frame(
    country = country_names,
    region = case_when(
      # 高收入国家（主要是欧美发达国家）
      country_names %in% c("United States of America", "Canada", "Germany", "France", "United Kingdom",
                          "Italy", "Spain", "Netherlands", "Belgium", "Switzerland",
                          "Austria", "Sweden", "Norway", "Denmark", "Finland", "Ireland",
                          "Portugal", "Greece", "Australia", "New Zealand", "Japan",
                          "South Korea", "Israel", "Luxembourg", "Iceland") ~ "高收入",

      # 撒哈拉以南非洲
      country_names %in% c("Nigeria", "Ethiopia", "South Africa", "Kenya", "Uganda", "Tanzania",
                          "Ghana", "Mozambique", "Madagascar", "Cameroon", "Angola", "Niger",
                          "Burkina Faso", "Mali", "Malawi", "Zambia", "Somalia", "Senegal",
                          "Chad", "Zimbabwe", "Guinea", "Rwanda", "Benin", "Burundi",
                          "South Sudan", "Togo", "Sierra Leone", "Liberia", "Botswana",
                          "Namibia", "Gabon", "Lesotho") ~ "撒哈拉以南非洲",

      # 南亚
      country_names %in% c("India", "Pakistan", "Bangladesh", "Afghanistan",
                          "Nepal", "Sri Lanka", "Bhutan", "Maldives") ~ "南亚",

      # 东南亚、东亚和大洋洲
      country_names %in% c("China", "Indonesia", "Philippines", "Vietnam", "Thailand",
                          "Myanmar", "Malaysia", "Cambodia", "Laos", "Singapore",
                          "Mongolia", "Brunei", "Timor-Leste", "Papua New Guinea",
                          "Fiji", "Solomon Islands", "Vanuatu", "Samoa") ~ "东南亚、东亚和大洋洲",

      # 中欧、东欧和中亚
      country_names %in% c("Russia", "Ukraine", "Belarus", "Moldova", "Romania", "Bulgaria",
                          "Serbia", "Montenegro", "Bosnia and Herzegovina", "Albania",
                          "North Macedonia", "Kosovo", "Kazakhstan", "Uzbekistan",
                          "Turkmenistan", "Kyrgyzstan", "Tajikistan", "Armenia",
                          "Azerbaijan", "Georgia", "Poland", "Czech Republic",
                          "Slovakia", "Hungary", "Croatia", "Slovenia", "Estonia",
                          "Latvia", "Lithuania") ~ "中欧、东欧和中亚",

      # 拉丁美洲和加勒比海
      country_names %in% c("Brazil", "Mexico", "Colombia", "Argentina", "Peru", "Venezuela",
                          "Chile", "Ecuador", "Guatemala", "Cuba", "Bolivia", "Haiti",
                          "Dominican Republic", "Honduras", "Paraguay", "Nicaragua",
                          "El Salvador", "Costa Rica", "Panama", "Uruguay", "Jamaica",
                          "Trinidad and Tobago", "Guyana", "Suriname", "Belize") ~ "拉丁美洲和加勒比海",

      # 北非和中东
      country_names %in% c("Egypt", "Iran", "Turkey", "Iraq", "Saudi Arabia", "Yemen",
                          "Syria", "Jordan", "Lebanon", "United Arab Emirates",
                          "Oman", "Kuwait", "Qatar", "Bahrain", "Algeria", "Morocco",
                          "Sudan", "Tunisia", "Libya") ~ "北非和中东",

      # 默认分组
      TRUE ~ "其他"
    ),
    stringsAsFactors = FALSE
  )

  cat("国家映射统计:\n")
  region_counts <- table(mapping$region)
  for(region in names(region_counts)) {
    cat("-", region, ":", region_counts[region], "个国家\n")
  }
  cat("- 总计:", nrow(mapping), "个国家\n")

  return(mapping)
}

# 获取世界地图数据
world <- ne_countries(scale = "medium", returnclass = "sf")

# 创建国家映射
country_mapping <- create_country_mapping(world)

# 4.2 合并AAPC数据
country_aapc <- country_mapping %>%
  left_join(region_aapc, by = c("region" = "地区")) %>%
  select(country, region, AAPC, AAPC_lower, AAPC_upper)

cat("国家AAPC数据样本:\n")
print(head(country_aapc, 10))

# ==================== 5. Fig 3风格的颜色分级系统 ====================

# 5.1 定义Fig 3风格的AAPC分级（调整为脑卒中数据范围）
# 基于实际数据范围：-0.92 到 1.69
create_fig3_breaks <- function() {
  breaks <- c(-1.0, -0.8, -0.6, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 2.0)
  labels <- c(
    "-1.0 to <-0.8",
    "-0.8 to <-0.6", 
    "-0.6 to <-0.4",
    "-0.4 to <-0.2",
    "-0.2 to <0.0",
    "0.0 to <0.2",
    "0.2 to <0.4",
    "0.4 to <0.6", 
    "0.6 to <0.8",
    "0.8 to <1.0",
    "1.0 to <1.2",
    "1.2 to <1.4",
    "1.4 to <1.6",
    "≥1.6"
  )
  
  return(list(breaks = breaks, labels = labels))
}

fig3_scale <- create_fig3_breaks()

# 5.2 创建Fig 3风格的颜色调色板（深蓝到深红）
create_fig3_colors <- function() {
  # 14个颜色等级：深蓝 -> 浅蓝 -> 白色 -> 浅红 -> 深红
  colors <- c(
    "#08306b",  # 深蓝色 (最低值)
    "#08519c",  # 深蓝
    "#3182bd",  # 中蓝
    "#6baed6",  # 浅蓝
    "#9ecae1",  # 很浅蓝
    "#c6dbef",  # 极浅蓝
    "#fee0d2",  # 极浅红
    "#fcbba1",  # 很浅红
    "#fc9272",  # 浅红
    "#fb6a4a",  # 中红
    "#ef3b2c",  # 深红
    "#cb181d",  # 很深红
    "#a50f15",  # 极深红
    "#67000d"   # 最深红 (最高值)
  )
  
  return(colors)
}

fig3_colors <- create_fig3_colors()

# 5.3 为国家数据分配颜色组
assign_color_groups <- function(country_aapc, breaks) {
  country_aapc$aapc_group <- cut(
    country_aapc$AAPC, 
    breaks = breaks$breaks, 
    labels = breaks$labels, 
    include.lowest = TRUE,
    right = FALSE
  )
  
  # 处理超出范围的值
  country_aapc$aapc_group[country_aapc$AAPC < min(breaks$breaks, na.rm = TRUE)] <- breaks$labels[1]
  country_aapc$aapc_group[country_aapc$AAPC >= max(breaks$breaks, na.rm = TRUE)] <- breaks$labels[length(breaks$labels)]
  
  return(country_aapc)
}

country_aapc <- assign_color_groups(country_aapc, fig3_scale)

cat("颜色分组统计:\n")
print(table(country_aapc$aapc_group, useNA = "always"))

# ==================== 6. 准备地图数据 ====================

cat("\n=== 准备地图数据 ===\n")
# 世界地图数据已在前面获取

# 6.1 使用正确的字段名进行匹配
country_name_field <- if("name_en" %in% names(world)) {
  "name_en"
} else if("admin" %in% names(world)) {
  "admin"
} else {
  "name"
}

world$country_standard <- world[[country_name_field]]

# 6.3 合并AAPC数据到地图
world_with_data <- world %>%
  left_join(country_aapc, by = c("country_standard" = "country"))

# 检查匹配情况
matched_countries <- sum(!is.na(world_with_data$AAPC))
total_countries <- nrow(world_with_data)
cat("成功匹配的国家数量:", matched_countries, "/", total_countries, "\n")

# 显示未匹配的国家
unmatched <- world_with_data %>%
  filter(is.na(AAPC)) %>%
  select(country_standard) %>%
  st_drop_geometry()

if(nrow(unmatched) > 0) {
  cat("未匹配的国家:\n")
  print(head(unmatched, 10))
}

# ==================== 7. 创建主地图 ====================

# 7.1 创建Fig 3风格的主地图
create_main_map <- function(world_data, colors, breaks) {

  # 创建颜色映射
  color_mapping <- setNames(colors, breaks$labels)

  # 创建主地图
  main_map <- ggplot(world_data) +
    geom_sf(aes(fill = aapc_group), color = "white", linewidth = 0.1) +
    scale_fill_manual(
      values = color_mapping,
      name = "AAPC (%)",
      na.value = "grey90",
      drop = FALSE,
      guide = guide_legend(
        title = "Average Annual Percentage Change (%)\nStroke Prevalence (≥65 years)\n1990-2019",
        title.position = "top",
        title.hjust = 0.5,
        ncol = 1,
        keywidth = 1.2,
        keyheight = 0.8,
        label.theme = element_text(size = 9),
        title.theme = element_text(size = 10, face = "bold")
      )
    ) +
    coord_sf(crs = "+proj=robin +lon_0=0 +x_0=0 +y_0=0 +ellps=WGS84 +datum=WGS84 +units=m +no_defs") +
    theme_void() +
    theme(
      legend.position = "right",
      legend.margin = margin(l = 20),
      plot.title = element_text(size = 14, face = "bold", hjust = 0.5, margin = margin(b = 20)),
      plot.subtitle = element_text(size = 12, hjust = 0.5, margin = margin(b = 15)),
      plot.margin = margin(10, 10, 10, 10),
      panel.background = element_rect(fill = "white", color = NA),
      plot.background = element_rect(fill = "white", color = NA)
    ) +
    labs(
      title = "Global Stroke Prevalence: Average Annual Percentage Change (1990-2019)",
      subtitle = "Population aged ≥65 years"
    )

  return(main_map)
}

main_map <- create_main_map(world_with_data, fig3_colors, fig3_scale)

# ==================== 8. 创建区域放大地图 ====================

# 8.1 定义Fig 3风格的重点区域
define_regional_focus <- function() {
  regions <- list(
    caribbean = list(
      xlim = c(-90, -55),
      ylim = c(10, 30),
      title = "Caribbean and Central America",
      crs = "+proj=merc +datum=WGS84"
    ),
    persian_gulf = list(
      xlim = c(45, 60),
      ylim = c(22, 32),
      title = "Persian Gulf",
      crs = "+proj=merc +datum=WGS84"
    ),
    balkans = list(
      xlim = c(12, 30),
      ylim = c(40, 48),
      title = "Balkan Peninsula",
      crs = "+proj=merc +datum=WGS84"
    ),
    southeast_asia = list(
      xlim = c(90, 140),
      ylim = c(-10, 25),
      title = "South East Asia",
      crs = "+proj=merc +datum=WGS84"
    ),
    west_africa = list(
      xlim = c(-20, 20),
      ylim = c(0, 20),
      title = "West Africa & Eastern Mediterranean",
      crs = "+proj=merc +datum=WGS84"
    ),
    northern_europe = list(
      xlim = c(-10, 35),
      ylim = c(55, 72),
      title = "Northern Europe",
      crs = "+proj=merc +datum=WGS84"
    )
  )

  return(regions)
}

regional_focus <- define_regional_focus()

# 8.2 创建区域地图函数
create_regional_map <- function(world_data, region_info, colors, breaks) {

  # 创建颜色映射
  color_mapping <- setNames(colors, breaks$labels)

  regional_map <- ggplot(world_data) +
    geom_sf(aes(fill = aapc_group), color = "white", linewidth = 0.3) +
    scale_fill_manual(
      values = color_mapping,
      name = "AAPC (%)",
      na.value = "grey90",
      drop = FALSE
    ) +
    coord_sf(
      xlim = region_info$xlim,
      ylim = region_info$ylim,
      expand = FALSE,
      crs = region_info$crs
    ) +
    theme_void() +
    theme(
      legend.position = "none",
      plot.title = element_text(size = 11, face = "bold", hjust = 0.5),
      plot.margin = margin(5, 5, 5, 5),
      panel.border = element_rect(color = "black", fill = NA, linewidth = 1),
      panel.background = element_rect(fill = "white", color = NA)
    ) +
    labs(title = region_info$title)

  return(regional_map)
}

# 8.3 生成所有区域地图
cat("\n=== 创建区域放大地图 ===\n")
regional_maps <- list()

for(region_name in names(regional_focus)) {
  cat("创建", region_name, "地图...\n")
  regional_maps[[region_name]] <- create_regional_map(
    world_with_data,
    regional_focus[[region_name]],
    fig3_colors,
    fig3_scale
  )
}

# ==================== 9. Fig 3风格的最终组合 ====================

# 9.1 创建Fig 3风格的完整布局
create_fig3_layout <- function(main_map, regional_maps) {

  # 创建区域地图的网格布局 (2行3列)
  regional_grid <- arrangeGrob(
    regional_maps$caribbean,
    regional_maps$persian_gulf,
    regional_maps$balkans,
    regional_maps$southeast_asia,
    regional_maps$west_africa,
    regional_maps$northern_europe,
    ncol = 3, nrow = 2,
    top = textGrob(
      "Regional Focus Areas",
      gp = gpar(fontsize = 12, fontface = "bold"),
      vjust = 0.5
    )
  )

  # 组合主地图和区域地图
  final_layout <- arrangeGrob(
    main_map,
    regional_grid,
    heights = c(2, 1),  # 主地图占2/3，区域地图占1/3
    ncol = 1
  )

  return(final_layout)
}

# 9.2 生成最终的Fig 3风格图表
cat("\n=== 生成最终图表 ===\n")
fig3_layout <- create_fig3_layout(main_map, regional_maps)

# ==================== 10. 保存输出 ====================

# 10.1 保存高质量的Fig 3风格地图
save_fig3_outputs <- function(layout, main_map, regional_maps) {

  # 保存完整的Fig 3风格图表
  cat("保存完整的Fig 3风格图表...\n")
  ggsave(
    "stroke_fig3_complete_layout.png",
    layout,
    width = 16, height = 12, dpi = 300, bg = "white"
  )

  # 保存主地图（单独）
  cat("保存主地图...\n")
  ggsave(
    "stroke_fig3_main_map.png",
    main_map,
    width = 14, height = 8, dpi = 300, bg = "white"
  )

  # 保存区域地图组合
  cat("保存区域地图组合...\n")
  regional_combined <- arrangeGrob(
    regional_maps$caribbean, regional_maps$persian_gulf,
    regional_maps$balkans, regional_maps$southeast_asia,
    regional_maps$west_africa, regional_maps$northern_europe,
    ncol = 3, nrow = 2,
    top = textGrob("Regional Focus Areas: Stroke Prevalence AAPC (1990-2019)",
                   gp = gpar(fontsize = 14, fontface = "bold"))
  )

  ggsave(
    "stroke_fig3_regional_maps.png",
    regional_combined,
    width = 15, height = 8, dpi = 300, bg = "white"
  )

  cat("所有图表已保存完成！\n")
}

save_fig3_outputs(fig3_layout, main_map, regional_maps)

# ==================== 11. 生成数据摘要报告 ====================

# 11.1 创建数据摘要
create_data_summary <- function(region_aapc, country_aapc) {

  cat("\n=== Fig 3 脑卒中数据摘要报告 ===\n")
  cat("研究时间范围: 1990-2019年\n")
  cat("研究人群: ≥65岁脑卒中患者\n")
  cat("主要指标: 患病率年均变化率 (AAPC)\n\n")

  cat("=== 地区AAPC分析结果 ===\n")
  region_summary <- region_aapc %>%
    arrange(AAPC) %>%
    mutate(
      trend = case_when(
        AAPC < -0.5 ~ "显著改善",
        AAPC >= -0.5 & AAPC < 0 ~ "轻微改善",
        AAPC >= 0 & AAPC < 0.5 ~ "轻微恶化",
        AAPC >= 0.5 ~ "显著恶化"
      )
    )

  print(region_summary)

  cat("\n=== 趋势分类统计 ===\n")
  trend_stats <- table(region_summary$trend)
  print(trend_stats)

  cat("\n=== 颜色分组分布 ===\n")
  color_stats <- table(country_aapc$aapc_group, useNA = "always")
  print(color_stats)

  # 保存摘要到文件
  write.csv(region_summary, "stroke_fig3_regional_summary.csv", row.names = FALSE)
  write.csv(country_aapc, "stroke_fig3_country_data.csv", row.names = FALSE)

  cat("\n数据摘要已保存到CSV文件\n")
}

create_data_summary(region_aapc, country_aapc)

# ==================== 12. 执行完成信息 ====================

cat("\n", paste(rep("=", 60), collapse = ""), "\n")
cat("Fig 3 脑卒中版本复现完成！\n")
cat("生成的文件:\n")
cat("1. stroke_fig3_complete_layout.png - 完整的Fig 3风格图表\n")
cat("2. stroke_fig3_main_map.png - 主地图\n")
cat("3. stroke_fig3_regional_maps.png - 区域放大地图组合\n")
cat("4. stroke_fig3_regional_summary.csv - 地区数据摘要\n")
cat("5. stroke_fig3_country_data.csv - 国家级数据\n")
cat(paste(rep("=", 60), collapse = ""), "\n")
