# 国家级别可视化修改总结
# 对比修改前后的差异

library(ggplot2)
library(dplyr)

cat("=== 国家级别可视化修改总结 ===\n")

cat("\n【修改前的问题】:\n")
cat("1. 按大洲级别划分: 只有7个大洲，所有国家被分配到7种颜色\n")
cat("2. 图例缺失颜色: 某些AAPC区间没有对应数据，图例显示空白\n")
cat("3. 无法体现国家差异: 同一大洲内的国家都是相同颜色\n")
cat("4. 数据来源: 使用地区级别的汇总数据\n")

cat("\n【修改后的改进】:\n")
cat("1. 国家级别数据: 204个国家，每个国家独立的AAPC值和颜色\n")
cat("2. 分位数分级: 使用10分位数确保每个颜色级别都有相等数量的国家\n")
cat("3. 完整的图例: 所有10个颜色级别都有对应的数据\n")
cat("4. 更大的数据范围: AAPC从-6.16%到+2.01%，比原来的范围更广\n")
cat("5. 数据来源: GBD 2021数据库，35岁以上人群脑卒中DALYs\n")

cat("\n【技术改进】:\n")
cat("1. 数据源: 从地区汇总数据改为国家级别原始数据\n")
cat("2. 颜色分配: 从固定断点改为分位数断点\n")
cat("3. 图例样式: 统一为与参考脚本一致的guide_legend样式\n")
cat("4. 标签系统: 使用Q1-Q10分位数标签，更清晰的数值范围\n")

# 读取国家数据进行分析
country_aapc <- read.csv("stroke_dalys_35plus_aapc_results_1990_2021.csv", 
                        stringsAsFactors = FALSE, 
                        fileEncoding = "UTF-8")

cat("\n【数据统计】:\n")
cat("总国家数:", nrow(country_aapc), "\n")
cat("AAPC范围:", round(min(country_aapc$aapc, na.rm = TRUE), 2), "% 到", 
    round(max(country_aapc$aapc, na.rm = TRUE), 2), "%\n")
cat("平均AAPC:", round(mean(country_aapc$aapc, na.rm = TRUE), 2), "%\n")
cat("中位数AAPC:", round(median(country_aapc$aapc, na.rm = TRUE), 2), "%\n")

# 统计正负值分布
positive_count <- sum(country_aapc$aapc > 0, na.rm = TRUE)
negative_count <- sum(country_aapc$aapc < 0, na.rm = TRUE)
zero_count <- sum(country_aapc$aapc == 0, na.rm = TRUE)

cat("AAPC > 0 (上升):", positive_count, "个国家 (", round(positive_count/nrow(country_aapc)*100, 1), "%)\n")
cat("AAPC < 0 (下降):", negative_count, "个国家 (", round(negative_count/nrow(country_aapc)*100, 1), "%)\n")
cat("AAPC = 0 (稳定):", zero_count, "个国家\n")

cat("\n【分位数分布】:\n")
quantiles <- quantile(country_aapc$aapc, probs = seq(0, 1, 0.1), na.rm = TRUE)
for(i in 1:10) {
  cat("Q", i, ": ", round(quantiles[i], 2), "% 到 ", round(quantiles[i+1], 2), "%\n", sep="")
}

cat("\n【极值国家】:\n")
# 最低和最高AAPC的国家
min_country <- country_aapc[which.min(country_aapc$aapc), ]
max_country <- country_aapc[which.max(country_aapc$aapc), ]

cat("DALYs下降最快:", min_country$location_name, "(", round(min_country$aapc, 2), "%)\n")
cat("DALYs上升最快:", max_country$location_name, "(", round(max_country$aapc, 2), "%)\n")

cat("\n【图例样式统一】:\n")
cat("✅ 使用scale_fill_manual替代scale_fill_gradientn\n")
cat("✅ 使用guide_legend替代guide_colorbar\n")
cat("✅ 图例标题: 'AAPC (% per year)'\n")
cat("✅ 图例布局: 2行，顶部标题，居中对齐\n")
cat("✅ 字体大小: 标题14pt粗体，文本9pt\n")
cat("✅ 图例键: 0.8cm高，1.2cm宽\n")

cat("\n【生成文件】:\n")
cat("• stroke_incidence_balanced_colors_main_map_1990_2021.png\n")
cat("• stroke_incidence_balanced_colors_complete_layout_1990_2021.png\n")

cat("\n=== 修改完成！现在地图显示真正的国家级别差异 ===\n")
