#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业版脑卒中65岁以上人群患病率AAPC全球地图可视化
模仿T1DM地图样式，基于1990-2019年数据
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from matplotlib.colors import ListedColormap, BoundaryNorm
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_professional_stroke_map():
    """创建专业版脑卒中AAPC全球地图"""
    
    # 读取数据
    try:
        data = pd.read_csv("脑卒中65岁以上人群患病率分析表.csv", encoding='utf-8')
    except:
        data = pd.read_csv("脑卒中65岁以上人群患病率分析表.csv", encoding='gbk')
    
    # 提取地区AAPC数据
    region_data = data[data['分类'].isin([
        '撒哈拉以南非洲', '东南亚、东亚和大洋洲', '南亚', '高收入',
        '中欧、东欧和中亚', '拉丁美洲和加勒比海', '北非和中东'
    ])][['分类', 'AAPC(%)']].copy()
    
    # 创建主图
    fig = plt.figure(figsize=(16, 10))
    
    # 主地图区域
    ax_main = fig.add_subplot(111)
    
    # 创建世界地图的简化版本
    create_world_map_visualization(ax_main, region_data)
    
    # 添加标题
    fig.suptitle('Average Annual Percentage Change in Stroke Prevalence\nAmong people aged ≥65 years, 1990-2019', 
                fontsize=16, fontweight='bold', y=0.95)
    
    # 保存图片
    plt.tight_layout()
    plt.savefig('stroke_professional_world_map.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    # 打印数据摘要
    print("脑卒中65岁以上人群AAPC数据摘要:")
    print(region_data.to_string(index=False))

def create_world_map_visualization(ax, region_data):
    """创建世界地图可视化"""
    
    # 创建AAPC值字典
    aapc_dict = dict(zip(region_data['分类'], region_data['AAPC(%)']))
    
    # 定义颜色方案（模仿T1DM地图）
    # 从蓝色（负值/低值）到红色（高值）
    colors = ['#2166ac', '#4393c3', '#92c5de', '#d1e5f0', '#f7f7f7', '#fdbf6f', '#fd8d3c', '#e31a1c']
    boundaries = [-1.0, -0.8, -0.6, -0.4, -0.2, 0.2, 0.6, 1.0, 1.4]
    
    def get_color_for_aapc(aapc):
        """根据AAPC值获取颜色"""
        if aapc < -0.8:
            return '#2166ac'  # 深蓝色
        elif aapc < -0.6:
            return '#4393c3'  # 蓝色
        elif aapc < -0.4:
            return '#92c5de'  # 浅蓝色
        elif aapc < 0:
            return '#d1e5f0'  # 很浅蓝色
        elif aapc < 0.5:
            return '#f7f7f7'  # 白色/灰色
        elif aapc < 1.0:
            return '#fdbf6f'  # 橙色
        elif aapc < 1.2:
            return '#fd8d3c'  # 深橙色
        else:
            return '#e31a1c'  # 红色
    
    # 定义各地区在地图上的位置和形状（模拟真实地理位置）
    regions_shapes = {
        '高收入': [  # 北美、欧洲、澳洲等
            {'type': 'rect', 'coords': (0.05, 0.65, 0.25, 0.25)},  # 北美
            {'type': 'rect', 'coords': (0.35, 0.6, 0.25, 0.2)},    # 欧洲
            {'type': 'rect', 'coords': (0.75, 0.15, 0.15, 0.1)},   # 澳洲
        ],
        '中欧、东欧和中亚': [
            {'type': 'rect', 'coords': (0.4, 0.5, 0.35, 0.25)},    # 东欧和中亚
        ],
        '东南亚、东亚和大洋洲': [
            {'type': 'rect', 'coords': (0.6, 0.35, 0.3, 0.3)},     # 东亚
            {'type': 'rect', 'coords': (0.65, 0.25, 0.2, 0.15)},   # 东南亚
        ],
        '南亚': [
            {'type': 'rect', 'coords': (0.52, 0.35, 0.12, 0.15)},  # 南亚
        ],
        '北非和中东': [
            {'type': 'rect', 'coords': (0.38, 0.4, 0.2, 0.2)},     # 中东
            {'type': 'rect', 'coords': (0.32, 0.35, 0.15, 0.1)},   # 北非
        ],
        '撒哈拉以南非洲': [
            {'type': 'rect', 'coords': (0.32, 0.15, 0.18, 0.25)},  # 撒哈拉以南非洲
        ],
        '拉丁美洲和加勒比海': [
            {'type': 'rect', 'coords': (0.12, 0.1, 0.15, 0.45)},   # 南美
            {'type': 'rect', 'coords': (0.08, 0.5, 0.12, 0.15)},   # 中美和加勒比
        ]
    }
    
    # 绘制各地区
    for region, shapes in regions_shapes.items():
        if region in aapc_dict:
            aapc = aapc_dict[region]
            color = get_color_for_aapc(aapc)
            
            for shape in shapes:
                if shape['type'] == 'rect':
                    x, y, width, height = shape['coords']
                    rect = patches.Rectangle((x, y), width, height, 
                                           facecolor=color, edgecolor='white', 
                                           linewidth=1, alpha=0.9)
                    ax.add_patch(rect)
    
    # 添加地区标签
    label_positions = {
        '高收入': (0.2, 0.75),
        '中欧、东欧和中亚': (0.57, 0.62),
        '东南亚、东亚和大洋洲': (0.75, 0.5),
        '南亚': (0.58, 0.42),
        '北非和中东': (0.48, 0.5),
        '撒哈拉以南非洲': (0.41, 0.27),
        '拉丁美洲和加勒比海': (0.19, 0.32)
    }
    
    for region, (x, y) in label_positions.items():
        if region in aapc_dict:
            aapc = aapc_dict[region]
            ax.text(x, y, f'{aapc:.2f}%', ha='center', va='center', 
                   fontsize=10, fontweight='bold',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    
    # 设置坐标轴
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.set_aspect('equal')
    ax.axis('off')
    
    # 创建图例
    legend_data = [
        ('<-0.8', '#2166ac'),
        ('-0.8 to <-0.6', '#4393c3'),
        ('-0.6 to <-0.4', '#92c5de'),
        ('-0.4 to <0', '#d1e5f0'),
        ('0 to <0.5', '#f7f7f7'),
        ('0.5 to <1.0', '#fdbf6f'),
        ('1.0 to <1.2', '#fd8d3c'),
        ('≥1.2', '#e31a1c')
    ]
    
    # 在右下角添加图例
    legend_x = 0.02
    legend_y = 0.02
    legend_width = 0.15
    legend_height = 0.25
    
    # 绘制图例背景
    legend_bg = patches.Rectangle((legend_x, legend_y), legend_width, legend_height,
                                facecolor='white', edgecolor='black', linewidth=1, alpha=0.9)
    ax.add_patch(legend_bg)
    
    # 添加图例标题
    ax.text(legend_x + legend_width/2, legend_y + legend_height - 0.02, 'AAPC (%)',
           ha='center', va='top', fontsize=11, fontweight='bold')
    
    # 添加图例项
    item_height = 0.025
    for i, (label, color) in enumerate(legend_data):
        y_pos = legend_y + legend_height - 0.05 - (i * item_height)
        
        # 颜色块
        color_rect = patches.Rectangle((legend_x + 0.01, y_pos - item_height/2), 
                                     0.02, item_height*0.8, facecolor=color, 
                                     edgecolor='black', linewidth=0.5)
        ax.add_patch(color_rect)
        
        # 标签
        ax.text(legend_x + 0.04, y_pos, label, ha='left', va='center', fontsize=9)
    
    # 添加数据来源说明
    ax.text(0.98, 0.02, 'Data source: GBD 2019', ha='right', va='bottom', 
           fontsize=8, style='italic', transform=ax.transAxes)

if __name__ == "__main__":
    create_professional_stroke_map()
