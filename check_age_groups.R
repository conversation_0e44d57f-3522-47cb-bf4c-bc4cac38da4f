# 检查中国数据中的年龄组
library(dplyr)
library(readr)

cat("=== 检查中国数据中的年龄组 ===\n")

# 读取数据
china_files <- list.files('死亡损伤-数据库/204国家', 
                         pattern='IHME-GBD_2021_DATA.*\\.csv$', 
                         recursive=TRUE, full.names=TRUE)

china_data <- data.frame()
for(file in china_files) {
  temp_data <- read_csv(file, show_col_types = FALSE)
  filtered_data <- temp_data %>%
    filter(location_name == "中国", 
           cause_id == 494, 
           measure_id == 1, 
           metric_id == 3, 
           year >= 1990, year <= 2021) %>%
    select(location_name, sex_id, sex_name, age_id, age_name, year, val, upper, lower)
  china_data <- rbind(china_data, filtered_data)
}

cat("总数据行数:", nrow(china_data), "\n")

# 检查年龄组
age_groups_35plus <- c(12, 13, 14, 15, 16, 17, 18, 19, 20, 30, 31, 32, 235)
china_35plus <- china_data %>% filter(age_id %in% age_groups_35plus)

cat("35岁以上数据行数:", nrow(china_35plus), "\n")

# 显示可用的年龄组
unique_ages <- china_35plus %>% 
  select(age_id, age_name) %>% 
  distinct() %>% 
  arrange(age_id)

cat("可用的年龄组:\n")
print(unique_ages)

# 检查每个年龄组的数据量
age_counts <- china_35plus %>%
  group_by(age_id, age_name) %>%
  summarise(count = n(), .groups = 'drop') %>%
  arrange(age_id)

cat("\n每个年龄组的数据量:\n")
print(age_counts)

# 检查是否有缺失的年龄组
expected_ages <- c(12, 13, 14, 15, 16, 17, 18, 19, 20, 30, 31, 32, 235)
available_ages <- unique_ages$age_id
missing_ages <- setdiff(expected_ages, available_ages)

if(length(missing_ages) > 0) {
  cat("\n缺失的年龄组ID:", paste(missing_ages, collapse=", "), "\n")
} else {
  cat("\n所有预期的年龄组都存在\n")
}
