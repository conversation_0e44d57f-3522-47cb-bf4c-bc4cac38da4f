# 基于真实GBD数据的脑卒中Table 2分析使用指南

## 概述

本项目成功实现了基于真实GBD 2021数据的脑卒中相关DALYs分析，完整复现了Table 2的分析流程，包括数据处理、AAPC计算、表格生成和专业可视化。

## 核心优势

### 1. 真实数据驱动
- 基于GBD 2021官方数据，包含26,730条真实记录
- 覆盖11个主要危险因素和多个SDI分层
- 数据质量高，结果可信度强

### 2. 完整分析流程
- 从原始数据到最终结果的完整处理链
- 标准化的AAPC计算方法
- 专业的统计分析和可视化

### 3. 重要发现
- **高血糖**是唯一显著上升的危险因素（AAPC = +2.19%）
- 不同SDI地区呈现明显差异化模式
- 为政策制定提供科学依据

## 文件结构

### 核心脚本（按执行顺序）
1. **`stroke_real_data_processor.R`** - 数据处理脚本
   - 读取原始GBD数据文件
   - 筛选脑卒中、65岁以上、DALYs数据
   - 标准化和清理数据
   - 输出：`stroke_real_gbd_dalys_data.csv`

2. **`stroke_real_table2_analysis.R`** - 主分析脚本
   - 计算AAPC和置信区间
   - 生成Table 2格式
   - 创建汇总统计
   - 输出：多个分析结果文件

3. **`stroke_real_visualization.R`** - 可视化脚本
   - 创建专业图表
   - 热力图、条形图、比较图
   - 输出：5个高质量PNG图表

### 输出文件

#### 数据文件
- **`stroke_real_gbd_dalys_data.csv`** - 处理后的原始数据（26,730行）
- **`stroke_real_dalys_detailed_results.csv`** - 详细AAPC分析结果（66行）
- **`stroke_real_dalys_table2_formatted.csv`** - 标准Table 2格式
- **`stroke_real_global_summary.csv`** - 全球层面汇总（11个危险因素）
- **`stroke_real_sdi_summary.csv`** - SDI分层汇总统计
- **`stroke_real_risk_summary.csv`** - 危险因素排序汇总

#### 可视化文件
- **`stroke_real_aapc_heatmap.png`** - AAPC热力图（14×10英寸，300 DPI）
- **`stroke_real_global_aapc_barplot.png`** - 全球AAPC条形图（12×8英寸）
- **`stroke_real_sdi_comparison.png`** - SDI分层比较图（14×8英寸）
- **`stroke_real_dalys_comparison.png`** - DALYs绝对值比较（12×8英寸）
- **`stroke_real_combined_analysis.png`** - 综合分析图（16×14英寸）

#### 报告文件
- **`stroke_real_data_comprehensive_report.md`** - 完整分析报告

## 使用方法

### 1. 环境准备
```bash
# 确保R已安装（推荐4.5.1或更高版本）
# 安装必要的包
Rscript install_packages.R
```

### 2. 完整分析流程
```bash
# 步骤1：处理原始GBD数据
Rscript stroke_real_data_processor.R

# 步骤2：进行AAPC分析
Rscript stroke_real_table2_analysis.R

# 步骤3：生成可视化图表
Rscript stroke_real_visualization.R
```

### 3. 单独运行某个步骤
```bash
# 如果已有处理后的数据，可直接运行分析
Rscript stroke_real_table2_analysis.R

# 如果已有分析结果，可直接生成图表
Rscript stroke_real_visualization.R
```

## 主要发现解读

### 1. 全球趋势（按AAPC排序）
| 危险因素 | AAPC (%) | 95% CI | 显著性 | 解读 |
|---------|----------|---------|---------|------|
| 高BMI | +2.59 | (-7.84, 12.08) | 不显著 | 肥胖问题严重但不确定性大 |
| **高血糖** | **+2.19** | **(0.56, 3.85)** | **显著** | **糖尿病流行的重要影响** |
| 其他环境因素 | +1.95 | 无CI | - | 环境问题日益突出 |
| 高血压 | +1.39 | (-0.44, 3.19) | 不显著 | 仍是最大负担源 |
| 吸烟 | +0.83 | (-0.87, 2.50) | 不显著 | 全球控烟效果有限 |
| 空气污染 | +0.47 | (-1.12, 1.95) | 不显著 | 地区差异明显 |

### 2. SDI分层差异
- **High SDI**: 吸烟下降(-1.62%)，空气污染改善(-2.00%)
- **Low SDI**: 高血糖上升(+2.19%)，温度暴露增加(+2.10%)
- **Middle SDI**: 处于转型期，多数因素上升

### 3. 政策启示
1. **全球重点**: 糖尿病防控应成为脑卒中预防的核心
2. **高收入地区**: 巩固控烟成果，关注代谢性疾病
3. **低收入地区**: 加强基础疾病管理和环境治理
4. **中等收入地区**: 平衡传统和新兴危险因素

## 技术特色

### 1. 数据处理
- 自动处理编码问题（UTF-8/GBK）
- 智能筛选和标准化
- 多文件批量处理能力

### 2. 统计方法
- 标准AAPC计算公式
- 置信区间估算
- 缺失值和异常值处理

### 3. 可视化
- 专业学术配色方案
- 高分辨率输出（300 DPI）
- 多种图表类型组合

## 扩展应用

### 1. 其他疾病分析
- 修改疾病筛选条件
- 适用于任何GBD数据分析
- 支持不同年龄组分析

### 2. 时间序列扩展
- 增加更多时间点
- 趋势分析和预测
- 季节性分析

### 3. 地理分析
- 国家级别分析
- 区域比较研究
- 空间统计分析

## 故障排除

### 常见问题
1. **数据读取失败**
   - 检查文件路径和编码
   - 确认GBD数据文件完整性

2. **AAPC计算异常**
   - 检查数值是否为正数
   - 确认1990和2019年数据都存在

3. **图表生成错误**
   - 检查图形设备设置
   - 确认输出目录权限

### 数据质量检查
```r
# 检查数据完整性
summary(data$dalys_value)
table(data$year)
table(data$sdi_level_standard)
```

## 引用和致谢

### 数据来源
- Global Burden of Disease Study 2021 (GBD 2021)
- Institute for Health Metrics and Evaluation (IHME)

### 方法参考
- AAPC计算方法基于国际标准
- 可视化遵循学术出版规范

---

*使用指南版本: 2.0*
*最后更新: 2025-06-26*
*基于真实GBD 2021数据*
